<svg width="521" height="779" viewBox="0 0 521 779" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_169_984)">
<path d="M0 0H521V31L513 37.5V50.25L521 57V778.5H0V57L8 50.25V37.5L0 31V0Z" fill="#014BA5" fill-opacity="0.2"/>
</g>
<path d="M512.369 36.7239L512 37.024V37.5V50.25V50.7147L512.355 51.0143L520 57.4647V777.5H1V57.4646L8.64487 51.0143L9 50.7147V50.25V37.5V37.024L8.63059 36.7239L1 30.524V1H520V30.524L512.369 36.7239Z" stroke="#014BA5" stroke-width="2"/>
<defs>
<filter id="filter0_i_169_984" x="0" y="0" width="521" height="778.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.45098 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_169_984"/>
</filter>
</defs>
</svg>
