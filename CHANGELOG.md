# [1.3.0](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/compare/v1.2.0...v1.3.0) (2025-07-10)


### Bug Fixes

* `当前直流供电状态为${data.DCOutStatus}，无法停止交流供电 ([52996b1](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/52996b1ec208195ce55632a75ac062e0646ff703))
* BatteryGroupSOC ([d00a057](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d00a057f8b45b2387a7c5fe515a69be11d9d17fc))
* Cannot read properties of undefined (reading 'localeCompare') ([a261a75](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/a261a759fea632d06d37df9f33ffe14830d20c4d))
* cors ([5a24cc4](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/5a24cc442af8cb22f5c45dce838be8d4adac5624))
* formatter: metric.formatter 引用不对问题 ([a6ab644](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/a6ab6440e44f04f28e0233cbe74db6648d403e3f))
* pcs和bms名称排序 ([f148f2b](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/f148f2bd92fca33e580bb44d7cb179f8267508b0))
* replace static msgId with dynamic UUID generation in GongDianSection ([a7227b8](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/a7227b84be65d1bd1c4686e2f30a508a4628bb78))
* RSRP 无值认为没信号 ([f4893e2](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/f4893e2f96c4731760c7cc873bed57744aa5d7ee))
* update DC charging status check in BatteryCard components for HangZhou, JiangXi, and LiYuan sections ([2fc41e5](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/2fc41e5b50f51b52a7adb04bd084a217f9a03564))
* update input placeholders for clarity in PcsStatusSection and GongDianSection ([9967008](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/9967008438463420a27edf1f0fd7ae21ee7ab2d5))
* 充电桩和机组排序 ([2f7be67](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/2f7be6756f64787b02a0ee35087b5eec7ea25aa3))
* 利源 BatteryGroupSOC ([c6930c1](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/c6930c10713727e623bb177987e0d265a64fe80b))
* 字体缩小 ([d9d310f](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d9d310f070346cceefdc6f8fc9783f0eae985c6c))
* 并机通讯ID配置为0时，显示为- ([d55efa3](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d55efa36253adc1d7d7bbb648b347d1db0370980))
* 按钮disabled ([48b575e](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/48b575eed005fe88b1d0cd435b63e9a51b58c890))
* 支持PCS1和PCS2同时修改 ([f89a4d9](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/f89a4d93297a5400526175c9265da244b150205b))
* 放电文字改为供电 ([e0b61d8](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/e0b61d8e0f2ebf3b87cb49e6efdd58e22c062252))
* 本次供电时长 ([374af1f](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/374af1f1f3c10aaf407d256ddaad44dc3c875f3d))
* 杭州按钮不能点击和江西样式 ([3897a99](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/3897a994d71e78e1f6795c86929c8622b78b5c89))
* 水冷tab取数&江西补能 ([4adb49f](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/4adb49fd58575244a2fb356d424ff739aaed7fd9))
* 水冷机组样式 ([8cc98fb](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/8cc98fb57eeae9106c23affc35fd44be370a048f))
* **ChargingStatusSection:** synchronize power value with useEffect ([f032010](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/f0320106dac0828ceb7db75511444459dbc09b3d))
* **deploy:** increase proxy timeouts for /EMS/ location ([3109b05](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/3109b05eb31c0f5dd19b02c2b55c928916861ccf))
* **DeviceStatus:** update project type check to include 'liyuan' ([8a75c96](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/8a75c96138ae330fea9b627a0403bfd6b4e959ab))
* 系统状态为1的时候才 显示开机菜单 ([d88301d](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d88301d1e8533ef97575a1154ed01d6315c075b7))


### Features

* add lafangda development script and update BatteryCard and ChargeGunCard to improve index display logic ([c5c1adf](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/c5c1adfe790094fd75b9c72bc50792e31313c0cc))
* add support for lafangda branch in CI configuration and routing, enhancing project coverage ([5fb70fd](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/5fb70fdf29e735f13fa5cd1a4e8cb09571d9d882))
* add support for lafangda environment in docker script, enhancing deployment options ([b141b43](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/b141b43cdceb9a00777e3dff73f987a05c945510))
* enhance CI configuration to dynamically set artifact names based on branch, improving clarity and organization ([54e98f1](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/54e98f146dc6a5925498c4c0f9272fbe38323250))
* implement cooldown mechanism for operation actions in BuNengSection and GongDianSection across HangZhou, JiangXi, and LiYuan, enhancing user experience and preventing rapid re-triggering ([08251f0](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/08251f04cb9e6c31773b528ba67ea6f4e8142736))
* 优化 如果获取不到 系统状态 不允许交流供电和交流补能 ([273d1bf](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/273d1bfcaf6580fec29a6eb5f5efb356442ee155))
* 并机通讯ID ([2c88a85](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/2c88a858398c0eb9c06828b31787cadf41d98675))
* 清除所有PCS告警 按钮 ([230a3db](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/230a3db48b8446f35ca1cb2844485907fec42152))
* 设置并机功率比例&键盘 ([7baa562](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/7baa562c1ddbbda3ab22153c6788b4d266d4493e))
* **Alarm:** conditionally render PCS alarm button based on project type ([11fc5b3](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/11fc5b365d380f9d4afb4b6d3e415dcc5113d72e))
* **BatteryCard:** enhance operateDcStop functionality with cooldown mechanism and integrate acInputStatus for improved charging status checks in JiangXi and LiYuan sections ([9a4459e](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/9a4459edb366b2b19919949856d8799e493a0f80))
* **BatterySlots:** enhance component to support children and improve layout; add operateDcStop function in BuNengSection for stopping charging ([06eaa1a](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/06eaa1abb83b4bb5fdab15dfadba5a8c4c99c35f))
* **BuNengSection:** add DC charging status check and update operateDcStop function to prevent stopping when not charging ([2dc5fcb](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/2dc5fcb800f5295cc42d4d929bdd6e2c4fd904fd))
* **Header:** add moreMenu style and enforce homepage check for shutdown/startup actions ([8027aa7](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/8027aa720f051aa45115ba0429b3e43b5c3e1077))
* 不能停止接口 ([55c0824](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/55c0824854e23707592391e30e0d8e9fbfb1cc6c))
* 设备监控优化 ([b3058a5](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/b3058a5da8e7d0f24da84317959d20d967d85a65))
* **Header:** add GPS location display with Popover ([a78f9fc](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/a78f9fc1d59df9a90f6dd626881c53240ffdad0c))
* **JiangXi:** add follow-up commands for circuit breaker operations ([4528f58](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/4528f58cada1406c65ffef28896a7cc42dc2b38d))
* 断路器开关 ([1e27538](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/1e275385d72105ad8a44461bce979b9321db5a31))

# [1.2.0](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/compare/v1.1.0...v1.2.0) (2025-04-22)


### Bug Fixes

*   "type": "module", ([4a60e9b](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/4a60e9bdde16dc426fbbd8d16e1216e47c21201a))
*  build js ([1a6f07a](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/1a6f07a7f8a7a8bc55b0e3c99cc02725e8b43543))
* correct device type and handle null values consistently ([1ffa993](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/1ffa993555aa2f7dcb29ffc92fd383d339598c35)), closes [#979797](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/issues/979797)
* correct device type and system status checks ([e98f5de](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/e98f5de40727d106ea49114c07c6fbc4ff73f3bb))
* correct device type name from 'TemperContr1' to 'TemperContr' ([a00238d](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/a00238df0f303233cd150dcdaed949c4803e94ef))
* correct device type name in WaterCoolingCommandLogs ([227ff5e](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/227ff5eded9e184bb022c807aa31c9a038429c39))
* correct status mapping and add debug logs ([d4ca473](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d4ca4736f9e75d76147f7bdeee5e5da147778ca4))
* disable quick jumper in Alarm pagination ([d3c7d46](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d3c7d46a7b63f21188a18a49af8471b6ac2b9ced))
* 安装自动启动脚本 ([d611205](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d611205be402b647d7f56e441ef107f8a7c67655))
* 指令日志键盘问题&添加键盘调试日志 ([3e5a701](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/3e5a701fced8bbcf75bd7f36bbcf945be4c96e8b))
* **Keyboard:** add touch events ([6c67b24](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/6c67b242e34d151c44b160185d8fe7344a64dbd3))
* __dirname ([ba59752](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/ba5975220214a92fbbc830dbfe8529dd254420a4))
* electron build info ([5913949](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/591394998fa0fa31a11efffb0f975e2cdac4621d))
* fileURLToPath ([3f80b1c](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/3f80b1c526c8bc1bd2f7bfb26aa06c53f5b6cb55))
* iframe test url ([2ef0721](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/2ef07216b431012230a32257cbea2cb1fb5a30e5))
* 充电枪属性遮挡问题 ([42f9912](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/42f9912b707f7a4b6540b043d700fae33f46f83e))
* **ChargeGunCard:** add chargeStatus to gunOperate params ([fd07b40](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/fd07b40105c47691bfa74a659ad243bbd448597a))
* **CoolingSystemSection:** correct typo in getGunCCCSText function call ([1df2fee](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/1df2feeef2de63fc75e1fce2ea03fa42fe4b4395))
* **StatusSection:** correct device type from 'ChargingGun' to 'ChargeGun' ([ad6a8b7](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/ad6a8b7bd2f8120c8a73f027a0f795245481cf47))
* **StatusSection:** replace "充电枪" with "充电桩" for accuracy ([bb20af1](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/bb20af1bda6b32ce55415c263eb9c1317aa98d1f))
* uncomment and update status indicator styles in multiple components ([95ce100](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/95ce100b4dea179089bdb68e894c96d56239a942))
* **JiangXi:** update stop power supply confirmation message and charging status color ([cf981ea](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/cf981ea164d5ab12fb91b30ce113047f5e39b3b3))
* **JiangXi/ChargeGunCard:** correct charge status variable name ([6a872f6](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/6a872f6708e424518cf13ddfe27e4081a2871af5))
* PCS_Operation_Status ([a35c6b8](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/a35c6b8c66d42daa0ef5f56cf6e8f0417369b619))
* update device type and status property names ([5dbce54](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/5dbce54ec09dedafda674b318ca5963aae7b01af))
* **DeviceStatus:** correct device type name for temperature control ([edea8be](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/edea8beac7a367b34bf35a1ba91fce6468849ad6))
* update status checks and error message display ([7957461](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/79574617dc6c8220fa492522b53dffa0d68ff958)), closes [#fe4545](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/issues/fe4545)
* update status icon logic to handle undefined status ([511d795](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/511d795b963c3cd937b3dc9773fd48910f0671d9))
* url ([e4a6e62](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/e4a6e62426df042b7d6ace886662a9f7e82389c4))
* 样式问题 ([d1541bb](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d1541bbcf96a675e0e39eb974164df589f89fd52))


### Features

*  gps和设备信号强弱 ([4ae01ff](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/4ae01ff0254a85351279584fbf4a847e7d6518e1))
* add Redux DevTools extension for debugging ([12d54aa](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/12d54aa2dbc3c9208090b0984e3e96662a1f0eea))
* **ActionButtonsSection:** add dcOperate prop and onClick handler ([9412184](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/94121840fb0d9ded235fa2e7fd4d884fc3c62430))
* **auth:** add setUserInfo and clearUserInfo functions ([204ce1c](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/204ce1c8f7a3b03e9a7b4a9bd58a5c3726ba42ed))
* **BusinessStatistics:** add business statistics functionality and improve UI ([f819ecb](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/f819ecb58552a2c5a2d18c4e542cab804167dd98))
* **buttons:** update button text and color based on status ([716704e](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/716704e0a222c28d2ca47bb2c6cd280281a945b7))
* **ChargeGunCard:** update UI for gun status and add dynamic SVG icons ([8e2dff4](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/8e2dff4affddcf3033e0d2252f6198720062277b))
* **charging:** enhance charging functionality with power validation and status handling ([e686534](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/e686534fdbbe13646b085777919170870c78ab0e))
* **CommandLogs:** add filtering, pagination, and API integration ([c215ba7](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/c215ba7c0e500e12aa9d7cc1eea47021c33ca785))
* **deploy:** add proxy configuration for /api/businessstat/ ([55224c9](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/55224c9df0f2a230bca7c890a050811b6aede966))
* **device:** enhance device status management and UI updates ([40d08cd](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/40d08cd354da3c6d6e98f3bca13d5d09297f1426))
* **DeviceStatus:** add gun status mapping and display in CoolingSystemSection ([fd7f2d0](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/fd7f2d01277d7abb5494365fe8e2f6b90a1a8cca))
* **EchartsProgress:** add dynamic color based on progress value ([9e610f5](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/9e610f5107aa8071139cf628aadbd9392ce91f08))
* **electron:** add main.ts and update package.json for electron build ([df5de6d](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/df5de6df9cf88aa58fe0d4d3796a551337e8e6db))
* **iframe:** add message handling for Tauri actions in iframe ([66c4bc2](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/66c4bc205c84613742a4254458a453ee0c52d04a))
* **liyuan:** add LiYuan branch support with Docker, CI, and UI updates ([1696128](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/16961284c53b91438487de8df25c76c8fc4bd546))
* add Guiyang dev script and AC input power configuration ([5613f68](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/5613f68ee493cdbcbda2871f9b3ee3329196b7de))
* add Hangzhou  home page ([c1803b8](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/c1803b83d9bb1cc63e89fd87bbe1f7803c82b0e6))
* add MQTT integration and update real-time data handling ([09ea41f](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/09ea41fddabafe8e393f6c671fe344a17b2a1b77))
* add new components and update UI for charging status sections ([ff560fe](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/ff560fe033df16410e38d89983d077aee34b444b))
* **Home:** add new sections for charging status, power details, connection status, and action buttons ([edc3c68](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/edc3c68d409c1de476cf7a0a7b9df18f229531bd))
* **Home:** add tab navigation and adjust layout in DivWrapperScreen ([5627695](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/5627695668041549fdea5ce500b89554a22069f5))
* **Home:** add useRealTime hook for WebSocket connection ([ba89486](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/ba89486d2a6357490af87bb2a78cfab089246a35))
* **proxy:** add RootInterface API proxy configuration ([57c5319](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/57c5319198aa4f9bd877968a2c5e581c62437f95))
* **router:** dynamically load homepage based on branch name ([66faae5](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/66faae5bf53d92d55173c1dbc1964905fab0ae9a))
* add Tauri API integration for keybindings and update power logic ([3b6f539](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/3b6f539c39c27073049f9449935bf50bae290944))
* 江西-首页-补能UI ([1b4f4af](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/1b4f4af83f366c4bc7bd2e5d81d5dfbfb196e51e))
* **auth:** add md5 hashing for password and update login logic ([854a686](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/854a686dcdb94c53b645b097498a9ef6caf903bd))
* **BMSMonitoring:** add battery cluster filtering and selection ([96abb01](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/96abb0117d3fb5d427a8a8499f5251c6a02b7140))
* **BusinessStatistics:** add dynamic API endpoint and close button for keyboard ([f2927c5](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/f2927c5d5b7778c20d0a9c75f8fe1837f46f2bc2))
* **ChargingStatusSection:** add keyboard input and clear icon styling ([60d0af8](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/60d0af892fd86eb383ed4684994abd923b418048))
* **Header:** add device shutdown, startup, and logout functionality ([b7ae009](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/b7ae0099782358159cbe19d7e02950a1a7989b2e))
* **Home:** add power management services for AC and DC operations ([9d3da64](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/9d3da6460b034da0fff0f2ef2b21d993b552da10))
* **iframe:** add message handling for Tauri API synchronization ([218b1ea](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/218b1ea3d14d341fa63538d8a15b34f6c003c0f4))
* **keyboard:** add virtual keyboard support for search inputs ([826ce2e](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/826ce2e5178ba13bda2a70ff77ed3bd259fc9ec7))
* **power-supply:** add AC and DC power supply start/stop functionality ([ab93992](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/ab939926274cf2503086055ff1f109901e280954))
* **StatusSection:** integrate WebSocket data for dynamic status display ([2255f8e](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/2255f8efe68a5f6a876083902666e6808320f516))
* **StatusSection:** replace custom progress bar with EchartsProgress component ([390a740](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/390a74072c4e20740ca8cfff9165764f32cff7bb))
* **system-maintenance:** add command logs page and update styles ([2d1c1d7](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/2d1c1d7dfd5ab3d9d6704fecdb0f83e657657015))
* **tauri:** add dev script and update tauri configuration ([bb50476](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/bb5047695c9d738098025ca66ab0bf10dfd39c2b))
* **utils:** add formatNullValue function to handle null values ([5c38963](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/5c38963298d500ebc87c8b6ab302d49eaba4ebb0))
* add UUID generation utility and react-simple-keyboard dependency ([d21b453](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/d21b4538ce0d587bdeeea8965efbe534a2fb4f5e))
* 对接报警接口 ([0970aa5](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/0970aa5004ec359731a1df6de96407e04d6813d1))
* 新增告警页面 ([ac3c3a0](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/ac3c3a07289fc0936ba7acde4424e03a794deced))
* 设备状态 ([83c9625](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/83c9625812f7bdca23fd20e36a63c3b7bf87795c))
* 设备状态UI ([6cdbb9c](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/6cdbb9cd7f72c8fd3023bc312a4eeb3769ed8bba))

# [1.1.0](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/compare/v1.0.0...v1.1.0) (2025-03-17)


### Features

* 新增登陆页面 ([18e711c](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/18e711c39afe15fa2df59120710b1a5ff59d7368))

# 1.0.0 (2025-03-14)


### Features

* BMS ([2b4e3cb](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/2b4e3cb533519c21fcb316f53c3b052c740e8e7f))
* 业务统计 ([1e4dc08](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/1e4dc080784592c8d5b9060738dd8af8b59c7947))
* 创建所有页面路由和AI2Code模板 ([f1d5cdc](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/f1d5cdc36dddf2cbcfa06349b3599e739d4ea8c2))
* 指令日志前端UI ([6c43041](http://gitlab.irootech.com/rootcloud-platform/1-rootlink/sanyleemsui/commit/6c43041f56b8a631abded637e30a397c04e0016b))
