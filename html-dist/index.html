<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>三一锂能EMS</title>
  <style>
    html,
    body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    iframe {
      border: none;
    }

    /*     #overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1000;
    } */
  </style>
</head>

<body>
  <iframe id="iframeRef" src="http://127.0.0.1:80/" style="width: 1280px; height:800px;"></iframe>
  <script>
    // 监听来自 iframe 的消息
    window.addEventListener('message', async (event) => {
      // 验证消息来源
      if (event.data.type === 'tauri-action') {
        try {
          const { window: tauriWindow } = window.__TAURI__;
          if (tauriWindow?.getCurrentWindow) {
            const currentWindow = await tauriWindow.getCurrentWindow();

            switch (event.data.action) {
              case 'toggleFullscreen':
                await currentWindow.toggleFullscreen();
                break;
              case 'closeWindow':
                await currentWindow.close();
                break;
            }
          }

        } catch (error) {
          console.error('Tauri action error:', error);
        }
      }
    });
  </script>
</body>

</html>