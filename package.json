{"name": "sanyems-ui", "version": "1.3.0", "author": "<PERSON><<EMAIL>>", "homepage": "https://www.rootcloud.com/", "description": "三一锂能EMS", "type": "module", "scripts": {"dev:hz": "cross-env BRANCH_NAME=hangzhou vite --host", "dev:ly": "cross-env BRANCH_NAME=liyuan vite --host", "dev:jx": "cross-env BRANCH_NAME=jiangxi vite --host", "dev:gy": "cross-env BRANCH_NAME=guiyang vite --host", "dev:lfd": "cross-env BRANCH_NAME=lafangda vite --host", "dev:jn": "cross-env BRANCH_NAME=jinan vite --host", "dev:zj": "cross-env BRANCH_NAME=zhongji vite --host", "start": "cross-env VITE_ENV=test vite --host", "dev:prod": "cross-env BRANCH_NAME=jiangxi vite --host --mode production", "dev:coating": "vite --host --mode printDevelopment", "build": "tsc && vite build", "build1": "node -v", "build:jx": "cross-env BRANCH_NAME=jiangxi && tsc && vite build", "build:zj": "cross-env BRANCH_NAME=zhongji && tsc && vite build", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "lint": "npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "lint:eslint": "eslint --fix \"./**/*.{mjs,ts,tsx}\" --cache --cache-location node_modules/.cache/eslint/", "lint:prettier": "prettier --cache --write --log-level warn \"./**/*.{json,mjs,ts,tsx,css,html,md}\"", "lint:stylelint": "stylelint --fix \"./**/*.css\" --cache --cache-location node_modules/.cache/stylelint/", "lint-staged": "lint-staged", "tauri": "tauri", "dev:tauri": "tauri dev", "tauri:build": "tauri build", "tauri:debug": "cross-env BRANCH_NAME=jiangxi tauri build --debug"}, "lint-staged": {"./**/*.{json,mjs,ts,tsx,css,html,md}": ["npx prettier --cache --write --log-level warn"], "./**/*.{mjs,ts,tsx}": ["npx eslint --fix --cache --cache-location node_modules/.cache/eslint/"], "./**/*.css": ["npx stylelint --fix --cache --cache-location node_modules/.cache/stylelint/"]}, "resolutions": {"glob": "7.1.6", "rimraf": "3.0.2"}, "dependencies": {"@ant-design/icons": "5.6.1", "@emotion/react": "11.13.3", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@types/md5": "2.3.5", "@vitejs/plugin-legacy": "5.4.3", "ahooks": "^3.8.1", "antd": "^5.21.6", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "core-js": "3.39.0", "cron-parser": "4.9.0", "date-fns": "^4.1.0", "dayjs": "1.11.13", "echarts": "^5.5.1", "js-cookie": "^3.0.5", "lodash": "4.17.21", "md5": "2.3.0", "moment": "2.30.1", "mqtt": "5.10.4", "react": "18.3.1", "react-dom": "18.3.1", "react-is": "18.3.1", "react-router": "6.26.2", "react-router-dom": "6.26.2", "react-simple-keyboard": "3.8.57", "react-window": "1.8.11", "regenerator-runtime": "0.14.1", "simple-keyboard-layouts": "3.4.72", "tailwind-merge": "2.5.4", "zustand": "5.0.0"}, "devDependencies": {"@eslint/eslintrc": "2.1.4", "@semantic-release/changelog": "3.0.4", "@semantic-release/commit-analyzer": "6.3.0", "@semantic-release/git": "8.0.0", "@semantic-release/gitlab": "6.2.2", "@semantic-release/npm": "5.1.15", "@semantic-release/release-notes-generator": "7.3.0", "@tauri-apps/cli": "2.3.1", "@types/date-fns": "^2.6.3", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "20.16.5", "@types/react": "18.3.7", "@types/react-dom": "18.3.0", "@types/react-window": "1.8.8", "@typescript-eslint/eslint-plugin": "6.10.0", "@typescript-eslint/parser": "6.10.0", "@vitejs/plugin-react-swc": "3.7.0", "autoprefixer": "10.4.20", "cross-env": "7.0.3", "dotenv": "16.4.5", "eslint": "8.53.0", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.4", "globals": "15.9.0", "husky": "9.1.6", "less": "^4.2.0", "less-loader": "^12.2.0", "lint-staged": "15.2.10", "postcss-load-config": "6.0.1", "postcss-styled-syntax": "0.6.4", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "0.6.6", "semantic-release": "17.0.7", "shelljs": "^0.8.5", "stylelint": "16.9.0", "stylelint-config-standard": "36.0.1", "stylelint-order": "6.0.4", "tailwindcss": "3.4.12", "terser": "5.37.0", "typescript": "5.6.2", "vite": "5.4.6", "vite-plugin-checker": "0.8.0"}, "release": {"branches": ["main", {"name": "pre-release", "range": "1.46.x", "channel": "pre-release"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", ["@semantic-release/gitlab", {"gitlabUrl": "http://gitlab.irootech.com"}]]}, "license": "MIT", "engines": {"node": ">=16"}}