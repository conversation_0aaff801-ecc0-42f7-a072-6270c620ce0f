#!/bin/sh

set -ex

STATUS='构建成功✅'
EXIT_CODE=0
if [ -n "${2}" ]; then
  STATUS="构建失败❌"
  EXIT_CODE=1
fi
PACKAGE_VERSION=$(cat package.json \
  | grep version \
  | head -1 \
  | awk -F: '{ print $2 }' \
  | sed 's/[",]//g' \
  | awk '{$1=$1};1')

curl "${WECHAT_ROBOT_WEBHOOK}" \
  -H 'Content-Type: application/json' \
  -d "{ \
    \"msgtype\": \"markdown\", \
    \"markdown\": { \
      \"content\": \"### [${CI_PROJECT_NAME}](${CI_PROJECT_URL}) ${STATUS}\n\n\
> 提交者：<font color='comment'>${GITLAB_USER_NAME}(${GITLAB_USER_EMAIL})</font>\n\
> Pipeline：[${CI_COMMIT_TITLE}](${CI_PIPELINE_URL})\n\
> 分支：<font color='info'>${1}</font>\n\
> 版本：<font color='info'>${PACKAGE_VERSION}</font>\n\
> Job Artifacts：<font color='comment'>${CI_JOB_URL}/artifacts/download?file_type=archive </font>\n\
> 镜像：<font color='warning'>${3}</font>\n\
> 其他：${2}\"\
    }}"
exit ${EXIT_CODE}