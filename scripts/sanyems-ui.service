[Unit]
Description=Sanyems UI Service
After=network.target
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Environment=DISPLAY=:0
Environment=XAUTHORITY=/home/<USER>/.Xauthority
Environment=RUST_BACKTRACE=1
ExecStartPre=/bin/bash -c 'export DISPLAY=:0; export XAUTHORITY=/home/<USER>/.Xauthority'
ExecStart=/usr/bin/sanyems-ui
Restart=always
User=user
RestartSec=5

[Install]
WantedBy=multi-user.target