#!/bin/bash

set -e


# 获取 mode 参数
mode=
while :; do
  case $1 in
    --mode=?*)
      mode=${1#*=}
      ;;
   *)
    break
  esac

  shift
done

echo "$mode"
# if [[ "$mode" != "main" && "$mode" != "test" && "$mode" != "release" && "$mode" != "dev" && "$mode" != "local" ]]; then
#         echo "$mode"
#         echo "mode must be main or release or dev or local，CI_BRANCH=$2"
#         exit 1
# fi

DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$DIR/.."

DOCKER_REPO="${DOCKER_REGISTRY}" # 变量有 /rootcloud-platform 了
# DOCKER_REPO="${DOCKER_REGISTRY}/rootcloud-platform"
# DOCKER_REPO="rootcloud"
PROJECT_NAME="sanyleems-ui"

DOCKER_IMAGE_NAME="$DOCKER_REPO/$PROJECT_NAME"

echo "----> Start Build Docker Images at $PROJECT_ROOT"

cd $PROJECT_ROOT

## 根据 mode 获取版本号
VERSION=
DOCKER_FILE_PARAMS=
DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile"
VERSION=`date "+%Y%m%d_%H%M%S"`
if [[ "$mode" == "feat" ]] || [[ "$mode" == "test" ]]; then # 测试环境（贵阳）
  VERSION="test-$VERSION"
  DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile.test"
elif [[ "$mode" == "guiyang" ]]; then # 贵阳环境
  VERSION="gy-$VERSION"
  DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile.gy"
elif [[ "$mode" == "jiangxi" ]]; then # 江西环境
  VERSION="jx-$VERSION"
  DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile.jx"
elif [[ "$mode" == "liyuan" ]]; then # 利源工贸环境
  VERSION="lygm-$VERSION"
  DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile.lygm"
elif [[ "$mode" == "lafangda" ]]; then # 拉凡达环境
  VERSION="lafangda-$VERSION"
  DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile.lygm"
elif [[ "$mode" == "hangzhou" ]]; then # 杭州环境
  VERSION="hz-$VERSION"
  DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile.hz"
elif [[ "$mode" == "hotfix" ]]; then
  VERSION="hotfix-$VERSION"
elif [[ "$mode" == "main" ]]; then
  VERSION=$(node -p "require('$PROJECT_ROOT/package.json').version")
  VERSION="$VERSION"
  # tke
  DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile.tke"
else # 指定 release version
  RELEASE_VERSION=$(node -p "require('$PROJECT_ROOT/package.json').releaseVersion")
  VERSION="$RELEASE_VERSION"
  # 宁乡环境
  DOCKER_FILE_PARAMS="-f ./deploy/Dockerfile.nx"
fi


DOCKER_IMAGE_NAME_VERSION="$DOCKER_IMAGE_NAME:$VERSION"

echo "----> Build Docker Image $DOCKER_IMAGE_NAME_VERSION"

docker build ${DOCKER_FILE_PARAMS} -t ${DOCKER_IMAGE_NAME_VERSION} .

if [[ "$mode" == "local" ]];then
  echo  "----> Skip pushing image"
  exit 0
fi

echo "----> Push Docker Image to remote repository"

docker push ${DOCKER_IMAGE_NAME_VERSION}

echo "----> Clean Console Image"
docker rmi ${DOCKER_IMAGE_NAME_VERSION}

echo "----> Send Notification"
pwd
bash ./scripts/wechat_hook.sh ${mode} "" ${DOCKER_IMAGE_NAME_VERSION}
