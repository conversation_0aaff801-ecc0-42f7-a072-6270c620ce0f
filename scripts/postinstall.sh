#!/bin/bash

start_command="/usr/bin/sanyems-ui"
# 创建 systemd 服务文件
service_file="/etc/systemd/system/sanyems-ui.service"
echo "[Unit]
Description=Sanyems UI Service
After=network.target graphical.target
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Environment=DISPLAY=:0
Environment=XAUTHORITY=/home/<USER>/.Xauthority
ExecStartPre=/bin/bash -c 'export DISPLAY=:0; export XAUTHORITY=/home/<USER>/.Xauthority'
Environment=RUST_BACKTRACE=1
ExecStart=$start_command
Restart=always
User=$USER
RestartSec=5

[Install]
WantedBy=multi-user.target" | sudo tee "$service_file" > /dev/null

# 重新加载 systemd 管理器配置
sudo systemctl daemon-reload

# 启用服务以实现开机自动启动
sudo systemctl enable sanyems-ui.service

# 启动服务
sudo systemctl start sanyems-ui.service

echo "sanyems-ui 已安装并设置为开机自动启动。"

exit 0