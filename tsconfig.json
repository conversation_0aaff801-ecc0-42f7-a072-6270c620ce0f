{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "preserve", "jsxImportSource": "@emotion/react", "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "strict": true, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": ".", "types": ["vite/client", "@emotion/react/types/css-prop", "node"], "paths": {"@/*": ["src/*"]}}, "include": ["./src"], "exclude": ["node_modules"]}