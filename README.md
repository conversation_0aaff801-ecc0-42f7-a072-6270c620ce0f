# sanyleems-ui

三一锂能EMS 前端工程

- UI: https://www.figma.com/design/xIMr3bS9ZPPYIJnr1HJVDQ/%E4%B8%89%E4%B8%80%E9%94%82%E8%83%BDEMS?node-id=0-1&p=f&t=o3dnoOA6ECZIv5W5-0
- Node-RED： http://10.66.231.114:1880/

## 开始

- pnpm install
- pnpm dev or pnpm start
- pnpm build

各工程启动命令见 `package.json`

eg:

- pnpm run dev:jx # 表示启动江西环境
- pnpm run dev:ly # 表示启动利源工贸环境项目

## 代码工程说明

各项目的版本都是一套代码，主要是首页和设备监控两个地方做了不同的项目环境适配

比如首页根据运行环境变量`projectType` 动态加载对应工程的首页代码：

```jsx
const getHomePage = () => {
  switch (projectType) {
    case 'jiangxi':
      return import('./pages/Home/JiangXi').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'liyuan':
      return import('./pages/Home/LiYuan').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'hangzhou':
      return import('./pages/Home/HangZhou').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'guiyang':
      return import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage);
    default:
      return import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage);
  }
};
```

其他地方如果有工程差异变化的也基本是这样控制。


## 部署

`./deploy` 文件夹是dockerfile 和 nginx 文件，CICD执行的是 `./scripts/docker.sh`，**不同的分支构建不同的项目版本**
部署文档： `./deploy/ubuntu环境部署.md`
相关nginx配置文件也在`/deploy`件下，若存在变更部署时注意同步更新

## 接口文档

http://***********:9886/swagger/index.html
这是获取实时的
http://***********:9001/swagger/index.html
这是获取历史的

 实时数据接口 `GetEMSDeviceCurrentValues`   
- 实时数据websocket :  `ws://***********:9886/ws`
- websocket 发送数据格式：

```js
{
//接口类型 0： 实时数据
"InterfaceType":0,
//操作类型  1：开启 0：停止
"OperationType":1,
"ParameterContent":[
    {
     "DeviceList":null, //设备ID集合
      "Items":null //设备点位集合
    }
  ]
}
```

## VPN连接
- HUB名 ： linkdc
- 目标服务地址 :  ilinkremote.rootcloudapp.com
- 用户名 : jzf01
- 密码 :  jzf@123
- 端口号是 5555
只有需要产环境或者排查生产环境的bug时才使用vpn连接，其他情况需要连接vpn，生产环境使用要小心，与测试或者现场操作人员沟通确认再操作

## 公共组件：
RhKeyboard（全键盘）、RhNumberKeyboard(数字键盘)、RhPagination表格分页组件、RhSelectInput(同时支持输入和下拉选中的输入框)、RhDatePicker新版时间范围选择（历史趋势中用到，后续其他时间范围可能需要替换）

## TODO
目前项目分为多个环境，不同环境的配置不同，目前杭州环境配置文件(`src\pages\Home\HangZhou\MainViewRequestAddress.cfg`)调试完毕，其他环境还需要再调试，可通过customHome文件下的测试首页调试。
 剩余待调试部分：
 - vin码配置单个/多个需要修改配置文件
  ```jsx
    // VINCode2={true} 开启第二个vin码
    <div className="absolute right-[110px] top-0">
      <VinSection VINCode2={true} border={false} />
    </div>
  ```
  - OptButton 组件，不同卡片的按钮传参可能不同，需要根据实际情况调试
  - 补能复位目前未使用配置文件相关api,如果需要调整不同环境接口不同需要变更相关部分代码
  ```js
    const handleDCRest = async (deviceId: string) => {
    try {
      setIsOperating(true);
      const res = await httpPost('EMS/DCInputPower/Stop', {
        chargingDockId: deviceId || 'DcChargingDock1', //充电座的充电口id
      });
      if (res.result == 'successful') {
        message.info('补能复位操作成功！');
        setTimeout(() => {
          setIsOperating(false);
        }, 3000);
      } else {
        message.error(res?.resultInfo || '补能复位操作失败！');
        // 失败后立即可以再次点击
        setIsOperating(false);
      }
    } catch (error) {
      message.error('操作失败，请重试');
      // 异常后立即可以再次点击
      setIsOperating(false);
    }
  };
  ```
  