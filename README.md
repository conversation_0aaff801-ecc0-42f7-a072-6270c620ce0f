# sanyleems-ui

三一锂能EMS 前端工程

- UI: https://www.figma.com/design/xIMr3bS9ZPPYIJnr1HJVDQ/%E4%B8%89%E4%B8%80%E9%94%82%E8%83%BDEMS?node-id=0-1&p=f&t=o3dnoOA6ECZIv5W5-0
- Node-RED： http://*************:1880/

## 开始

- pnpm install
- pnpm dev or pnpm start
- pnpm build

各工程启动命令见 `package.json`

eg:

- pnpm run dev:jx # 表示启动江西环境
- pnpm run dev:ly # 表示启动利源工贸环境项目

## 代码工程说明

各项目的版本都是一套代码，主要是首页和设备监控两个地方做了不同的项目环境适配

比如首页根据运行环境变量`projectType` 动态加载对应工程的首页代码：

```jsx
const getHomePage = () => {
  switch (projectType) {
    case 'jiangxi':
      return import('./pages/Home/JiangXi').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'liyuan':
      return import('./pages/Home/LiYuan').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'hangzhou':
      return import('./pages/Home/HangZhou').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'guiyang':
      return import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage);
    default:
      return import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage);
  }
};
```

其他地方如果有工程差异变化的也基本是这样控制。


## 部署

`./deploy` 文件夹是dockerfile 和 nginx 文件，CICD执行的是 `./scripts/docker.sh`，**不同的分支构建不同的项目版本**


## 接口文档

http://*************:9886/swagger/index.html
这是获取实时的
http://*************:9001/swagger/index.html
这是获取历史的

 实时数据接口 `GetEMSDeviceCurrentValues`   
- 实时数据websocket :  `ws://*************:9886/ws`
- websocket 发送数据格式：

```js
{
//接口类型 0： 实时数据
"InterfaceType":0,
//操作类型  1：开启 0：停止
"OperationType":1,
"ParameterContent":[
    {
     "DeviceList":null, //设备ID集合
      "Items":null //设备点位集合
    }
  ]
}
```
