{"root": true, "plugins": ["stylelint-order"], "extends": ["stylelint-config-standard"], "customSyntax": "postcss-styled-syntax", "rules": {"selector-class-pattern": null, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global"]}], "at-rule-no-unknown": [true, {"ignoreAtRules": ["function", "if", "each", "include", "mixin"]}], "no-empty-source": null, "no-descending-specificity": null, "font-family-name-quotes": null, "font-family-no-missing-generic-family-keyword": null, "rule-empty-line-before": ["always", {"ignore": ["after-comment", "first-nested"]}], "unit-no-unknown": [true, {"ignoreUnits": ["rpx"]}], "order/properties-order": ["content", "position", "top", "right", "bottom", "left", "z-index", "float", "display", "flex-flow", "flex-direction", "flex-wrap", "align-items", "justify-content", "flex", "flex-grow", "flex-shrink", "flex-basis", "margin", "margin-top", "margin-right", "margin-bottom", "margin-left", "margin-collapse", "margin-top-collapse", "margin-right-collapse", "margin-bottom-collapse", "margin-left-collapse", "width", "height", "max-width", "max-height", "min-width", "min-height", "border", "border-collapse", "border-top", "border-right", "border-bottom", "border-left", "border-color", "border-image", "border-top-color", "border-right-color", "border-bottom-color", "border-left-color", "border-spacing", "border-style", "border-top-style", "border-right-style", "border-bottom-style", "border-left-style", "border-width", "border-top-width", "border-right-width", "border-bottom-width", "border-left-width", "border-radius", "border-top-right-radius", "border-bottom-right-radius", "border-bottom-left-radius", "border-top-left-radius", "padding", "padding-top", "padding-right", "padding-bottom", "padding-left", "overflow", "overflow-x", "overflow-y", "box-align", "box-flex", "box-orient", "box-pack", "box-shadow", "box-sizing", "background", "background-attachment", "background-color", "background-image", "background-position", "background-repeat", "background-size", "clip", "clear", "font", "font-family", "font-size", "font-smoothing", "osx-font-smoothing", "font-style", "font-weight", "hyphens", "src", "line-height", "letter-spacing", "word-spacing", "color", "text-align", "text-decoration", "text-indent", "text-overflow", "text-rendering", "text-size-adjust", "text-shadow", "text-transform", "word-break", "word-wrap", "white-space", "vertical-align", "list-style", "list-style-type", "list-style-position", "list-style-image", "pointer-events", "cursor", "quotes", "outline", "outline-offset", "opacity", "filter", "visibility", "size", "zoom", "transform", "table-layout", "animation", "animation-delay", "animation-duration", "animation-iteration-count", "animation-name", "animation-play-state", "animation-timing-function", "animation-fill-mode", "transition", "transition-delay", "transition-duration", "transition-property", "transition-timing-function", "background-clip", "backface-visibility", "resize", "appearance", "user-select", "interpolation-mode", "direction", "marks", "page", "set-link-source", "unicode-bidi", "speak"]}, "ignoreFiles": ["**/*.tsx", "**/*.js", "**/*.ts"]}