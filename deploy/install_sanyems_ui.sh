#!/bin/bash

# 检查是否提供了 .deb 文件作为参数
if [ $# -eq 0 ]; then
    echo "错误: 请提供 .deb 文件的名称作为参数。"
    exit 1
fi

# 获取 .deb 文件的名称
deb_file="$1"

# 检查 .deb 文件是否存在
if [ ! -f "$deb_file" ]; then
    echo "错误: $deb_file 文件不存在。"
    exit 1
fi

# 安装 .deb 软件包
echo "开始安装 $deb_file ..."
sudo dpkg -i "$deb_file"

# 检查安装是否成功
if [ $? -ne 0 ]; then
    echo "安装失败，尝试修复依赖问题..."
    sudo apt-get -f install
fi

start_command="/usr/bin/sanyems-ui"

# 创建 systemd 服务文件
service_file="/etc/systemd/system/sanyems-ui.service"
echo "[Unit]
Description=Sanyems UI Service
After=network.target graphical.target
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Environment=DISPLAY=:0
Environment=XAUTHORITY=/home/<USER>/.Xauthority
Environment=RUST_BACKTRACE=1
ExecStartPre=/bin/bash -c 'export DISPLAY=:0; export XAUTHORITY=/home/<USER>/.Xauthority'
ExecStart=$start_command
Restart=always
User=$USER
RestartSec=5

[Install]
WantedBy=multi-user.target" | sudo tee "$service_file" > /dev/null

# 重新加载 systemd 管理器配置
sudo systemctl daemon-reload

# 启用服务以实现开机自动启动
sudo systemctl enable sanyems-ui.service

# 启动服务
sudo systemctl start sanyems-ui.service

echo "sanyems-ui 已安装并设置为开机自动启动。"
