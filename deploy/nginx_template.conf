#user  nobody;
worker_processes 1;

# error_log logs/error.log;
# error_log logs/error.log notice;
# error_log logs/error.log info;

#pid        logs/nginx.pid;
events {
  worker_connections 1024;
}


http {
  include mime.types;
  default_type application/octet-stream;

  #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
  #                  '$status $body_bytes_sent "$http_referer" '
  #                  '"$http_user_agent" "$http_x_forwarded_for"';

  #access_log  logs/access.log  main;
  sendfile on;
  #tcp_nopush     on;

  #keepalive_timeout  0;
  keepalive_timeout 65;

  gzip on;
  # 是否在http header中添加Vary: Accept-Encoding，建议开启
  gzip_vary on;
  gzip_comp_level 6;
  # 设置压缩所需要的缓冲区大小
  gzip_buffers 16 8k;
  gzip_types text/plain text/css application/json application/javascript
  gzip_min_length 1k;

  map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
  }

  server {
    listen 80 default_server;
    listen [::]:80 default_server;
    #build files on server
    root /usr/share/nginx/html;
    # limit file upload sizes
    client_max_body_size 4096m;
    client_body_timeout 3000s;
    #proxy web-socket
    # location =/api/v1/datasources/realtime {
    #   proxy_pass http://************:8000;
    #   proxy_http_version 1.1;
    #   proxy_set_header Upgrade $http_upgrade;
    #   proxy_set_header Connection $connection_upgrade;
    #   proxy_set_header Host $host;
    # }
    # 非docker里的nginx可以这么配，但docker中不行
    # location /api {
    #   proxy_pass http://************:8000;
    # }
    # docker 里因为做了端口映射，绝对路径要有 / 后缀，或者是用正则匹配 ~
    location /api/v1/mgmt/mobile/{
      proxy_pass http://plcc-dataview-plm:56799/api/v1/mgmt/mobile/;
    }
    location /api/v1/mgmt/{
      proxy_pass http://plcc-dataview-plm:56798/api/v1/mgmt/;
    }
   
    #cache long-term static assets
    location /static {
      add_header cache-control max-age=315360000000;
    }
    #support fe router
    try_files $uri $uri/ /index.html;
    #no cache with/out static files
    add_header cache-control "no-store, no-cache, must-revalidate";

    server_name _;
  }

}