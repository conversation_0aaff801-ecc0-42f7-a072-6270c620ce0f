## ubuntu 环境准备

apt 镜像源网络问题：

- 1、VPN 解决
- 2、通过修改镜像源为阿里云

```bash
sudo sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list
sudo sed -i 's/security.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list

# 
sudo sed -i 's|cn.mirrors.aliyun.com|mirrors.aliyun.com|g' /etc/apt/sources.list
```

## 安装nginx

> 前期nginx部署


### 1. 安装 Nginx
更新完软件包列表后，就可以安装 Nginx 了。在终端中运行以下命令：
```bash
sudo apt install nginx
```
执行该命令后，系统会提示你确认安装，输入 `Y` 并回车，等待安装完成。

### 2. 验证安装
安装完成后，你可以通过以下几种方式验证 Nginx 是否安装成功：

#### 检查服务状态

使用以下命令检查 Nginx 服务的运行状态：
```bash
sudo systemctl status nginx
```

#### 访问默认页面
在浏览器中输入服务器的 IP 地址或者域名，如果看到 Nginx 的默认欢迎页面，则说明 Nginx 安装成功。

### 3. 管理 Nginx 服务
安装完成后，你可以使用以下命令来管理 Nginx 服务：

#### 启动 Nginx
```bash
sudo systemctl start nginx
```

#### 停止 Nginx
```bash
sudo systemctl stop nginx
```

#### 重启 Nginx
```bash
sudo systemctl restart nginx
```

#### 重新加载配置
当你修改了 Nginx 的配置文件后，可以使用以下命令重新加载配置，而无需重启服务：
```bash
sudo systemctl reload nginx
```

#### 设置开机自启
如果你希望 Nginx 在系统启动时自动启动，可以使用以下命令：
```bash
sudo systemctl enable nginx
```
#### 替换nginx配置文件

> 文件： `deploy\nginx.conf`

```bash
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.bak
sudo cp nginx.conf /etc/nginx/nginx.conf
sudo nginx -s reload
```

## 将代码文件复制到nginx目录下

```bash
```bash

# 进入到目录
 cd /usr/share/nginx/html/

 # 删除全部文件
sudo rm assets/* home/* device/* index.html

# 移动文件到本目录
sudo mv /home/<USER>/ems/dist528.zip /usr/share/nginx/html/

# 解压
sudo unzip dist.zip

# 查看文件
ls -lh

# 删除压缩包
sudo rm dist0419.zip

# 最后打开浏览器验证部署是否正常
```

## deb包安装环境

```
sudo apt update && sudo apt install -y libwebkit2gtk-4.1-dev build-essential curl wget file libssl-dev libayatana-appindicator3-dev librsvg2-dev libgtk-3-dev 
```

然后安装 `sanyems-ui.deb` 包 : `sudo dpkg -i sanyems-ui.deb`

执行 `sudo -u user /usr/bin/sanyems-ui` 命令测试启动是否正常

ubuntu桌面配置自动启动

如果启动有问题，可能需要编辑一下环境变量 

```
Failed to initialize gtk backend!: BoolError { message: "Failed to initialize GTK", filename: "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/gtk-0.18.2/src/rt.rs", function: "gtk::rt::init", line: 141 }
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace

```

解决问题：

```bash
nano ~/.bashrc

```

加入

```bash
export DISPLAY=:0
export XAUTHORITY=/home/<USER>/.Xauthority
```

`source ~/.bashrc`

然后重新启动测试

```bash
sudo -u user /usr/bin/sanyems-ui
```

自动启动方式2

>  `~/.config/autostart/autostart_ui.desktop` （文件名自定义）

```bash
[Desktop Entry]
Type=Application
Exec=sudo -u user /usr/bin/sanyems-ui
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
Name[zh_CN]=ems
Name=ems
Comment[zh_CN]=
Comment=EMS前端服务
```

## TODO

脚本自动化

- [ ] 全量安装deb包
- [ ] 字段配置开机自启
- [ ] 脱离nginx，支持代理转发，避免跨域
