{"$schema": "https://schema.tauri.app/config/2", "productName": "sanyems-ui", "version": "0.2.2", "identifier": "com.sany.sanyems", "mainBinaryName": "sanyems-ui", "build": {"frontendUrl": "http://localhost:80", "beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build"}, "app": {"withGlobalTauri": true, "windows": [{"title": "sanyems-ui", "width": 1280, "height": 800, "resizable": true, "fullscreen": false, "devtools": true}], "security": {"csp": null, "capabilities": [{"identifier": "custom-permision", "windows": ["*"], "permissions": ["core:window:allow-create", "core:window:allow-close", "core:webview:allow-create-webview", "core:webview:allow-webview-close", "core:webview:allow-create-webview-window"], "allowlist": {"dangerousDisableAssetCorsProtection": true, "net": {"all": true, "scope": ["http://127.0.0.1:*"]}, "http": {"all": true, "request": true, "dangerousUseHttpScheme": true, "scope": ["http://127.0.0.1:*"]}, "protocol": {"asset": true, "assetScope": ["**"]}}}]}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "linux": {"deb": {"postInstallScript": "../scripts/postinstall.sh"}}}}