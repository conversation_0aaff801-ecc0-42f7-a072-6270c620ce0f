import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { defineConfig } from 'vite';
import React from '@vitejs/plugin-react-swc';
import type { ConfigEnv } from 'vite';
import checker from 'vite-plugin-checker';
import legacy from '@vitejs/plugin-legacy';

/**
 * https://vitejs.dev/config/
 */
const hzHost = '*************'; // 杭州prod环境
// const hzHost = '************' // 杭州测试环境
// const jxHost = '***********' // 江西测试环境
const jxHost = '*************'; // 江西prod环境
const lygmHost = '*************'; // 利源工贸
const gyHost = '*************'; // 贵阳开发环境
// const zjHost = '*************'; // 重机开发环境
const zjHost = '*************'; // 重机开发环境
// const zjHost = '************'; // 重机设备环境
const dev = '***********';
// const dev = '************';

// const dev = '************';
// const dev='*************'

//'************'
// '***********'
const branch: string = 'zhongji'//process.env.BRANCH_NAME || 'test';

let host = gyHost;

switch (branch) {
  case 'jiangxi':
    host = jxHost;
    break;
  case 'hangzhou':
    host = hzHost;
    break;
  case 'liyuan':
    host = lygmHost;
    break;
  case 'guiyang':
    host = gyHost;
    break;
  case 'jinan':
    host = dev;
    break;
  case 'zhongji':
    host = zjHost;
    break;
  default:
    // host = gyHost;
    // host = "***********"
    host = dev;
}
// host = dev;

const baseConfig = () => ({
  define: {
    'process.env.BRANCH_NAME': JSON.stringify(process.env.BRANCH_NAME),
  },
  plugins: [
    React({ jsxImportSource: '@emotion/react' }),
    checker({ typescript: true }),
    legacy({
      targets: ['defaults', 'android >= 9', 'chrome >= 70'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
    }),
  ],
  server: {
    port: 3500,
    host: '0.0.0.0',
    proxy: {
      '/ws/': {
        target: `http://${host}:1880`,
        changeOrigin: true,
        ws: true,
        configure: (proxy, _options) => {
          // 静默吞掉所有 ws 代理层的错误
          proxy.on('error', (err, _req, _res) => {
            console.log('[ws proxy silent]', err.message);
          });
        },
      },
      '/api/Ems': {
        target: `http://${host}:9886`,
        changeOrigin: true,
      },
      '/api/RootInterface': {
        target: `http://${host}:9886`,
        changeOrigin: true,
      },
      '/api/history': {
        target: `http://${host}:9001`,
        changeOrigin: true,
      },
      '/api/auth': {
        target: `http://${host}:9001`,
        changeOrigin: true,
      },
      '/api/configs': {
        target: `http://${host}:9001`,
        changeOrigin: true,
      },
      '/api/task': {
        target: `http://${host}:5000`,
        changeOrigin: true,
      },
      '/api/file/': {
        target: `http://${host}:9001`,
        changeOrigin: true,
      },
      '/api/': {
        target: `http://${host}:9001`,
        changeOrigin: true,
      },
      '/EMS/': {
        target: `http://${host}:1880`,
        changeOrigin: true,
      },
      '/ems/': {
        target: `http://${host}:1880`,
        changeOrigin: true,
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '~': '/src',
    },
  },
});

export default ({ mode, command }: ConfigEnv) => {
  console.log('🚀 ~ mode, command:', mode, command);
  const { VITE_APP_NODE_ENV, VITE_APP_TITLE } = dotenv.parse(fs.readFileSync(`.env.${mode}`));

  console.log('\x1b[33m%s\x1b[0m', `🏭--NODE ENV (VITE_APP_NODE_ENV): ${VITE_APP_NODE_ENV}`);
  console.log('\x1b[36m%s\x1b[0m', `🏠--APP TITLE (VITE_APP_TITLE): ${VITE_APP_TITLE}`);
  // const port=VITE_APP_TITLE==='CoatingProdline'?56800:56799

  if (command === 'serve') {
    return defineConfig({ ...baseConfig() });
  } else {
    return defineConfig({ ...baseConfig() });
  }
};
