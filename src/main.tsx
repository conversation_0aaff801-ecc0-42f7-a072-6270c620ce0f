import 'core-js';
import React from 'react'
import "regenerator-runtime/runtime"
import { createRoot } from 'react-dom/client'
import { getElementById } from '@/utils'
import { App } from '@/App'
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

const rootElement = getElementById('root')

const app = createRoot(rootElement)

app.render(
    <ConfigProvider locale={zhCN as any}>
        <App />
    </ConfigProvider>
)
