export interface Device {
  Name: string;
  DeviceID: string;
  Type: string;
  Status: string;
  LastStatus: string;
}

export interface DeviceItem {
  Name: string;
  Description: string;
  Value: number;
}

export interface DeviceData {
  Device: Device;
  ItemList: DeviceItem[];
}

export interface WebSocketData {
  SOC: number;
  ChargePower: number;
  DCInputStatus: number;
  ACInputStatus: number;
  DCOutStatus: number;
  Capacity: number;
  TodayChargePower: number;
  TodayDisChargePower: number;
  AllDisChargePower: number;
  AllChargePower: number;
  AlarmCount: number;
  CurrentSystemStatus: number;
  Devices: DeviceData[];
}