import { Router } from '@/router';
import { useEffect } from 'react';
import { createIdToTypeMap, useDeviceStore } from '@/store/deviceStore';
import { httpGet } from '@/shared/http';
import { checkLoginStatus } from './shared/auth';
import { projectType } from './constants';

import '@/styles/index.less';
import { useDictStore } from './store/dictStore';
import locale from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs';
import { ConfigProvider, message } from 'antd';
import { GlobalErrorProvider } from './contexts/GlobalErrorContext';
import useProgress from './pages/Home/useProgress';
import { useConfigStore } from './store/configStore';

dayjs.locale('zh-cn');
export const App: React.FC = () => {
  const { setDeviceMap, setACInputPower, setDeviceIdToTypeMap } = useDeviceStore();
  const { setConfig } = useConfigStore();
  const { setDictConfig } = useDictStore();
  // 交流供电
  const {
    data: progress,
    isConnected: isConnectedACOut,
    reconnect: reconnectACOut,
    isReconnecting: isReconnectingACOut,
  } = useProgress({ type: 'ACOut' });

  // 直流补能
  const {
    data: progressDCInput,
    isConnected: isConnectedDCInput,
    reconnect: reconnectDCInput,
  } = useProgress({ type: 'DCInput' });
  // 交流补能
  const {
    data: progressACInput,
    isConnected: isConnectedACInput,
    reconnect: reconnectACInput,
  } = useProgress({ type: 'ACInput' });
  // 并网放电
  const {
    data: progressGridOut,
    isConnected: isConnectedACGridOut,
    reconnect: reconnectACGridOut,
  } = useProgress({ type: 'ACGridOut' });

  useEffect(() => {
    if (!isConnectedDCInput) {
      reconnectDCInput();
    }
  }, [isConnectedDCInput]);

  useEffect(() => {
    if (!isConnectedACInput) {
      reconnectACInput();
    }
  }, [isConnectedACInput]);

  useEffect(() => {
    if (!isConnectedACGridOut) {
      reconnectACGridOut();
    }
  }, [isConnectedACGridOut]);

  // 获取字典数据
  const fetchDictData = async () => {
    const config = await httpGet('/api/configs/get?fileName=DataMatchDict.cfg');
    setDictConfig(config || {});
  };

  useEffect(() => {
    if (!isConnectedACOut && !isReconnectingACOut) {
      reconnectACOut();
    }
  }, [isConnectedACOut, isReconnectingACOut]);

  // 获取首页配置信息
  const getConfig = async () => {
    try {
      const res = await httpGet('/api/configs/get?fileName=MainViewRequestAddress.cfg');
      setConfig(res);
    } catch (error) {
      message.error('获取首页配置信息失败');
    }
  };

  useEffect(() => {
    // getConfig();
  }, []);

  useEffect(() => {
    checkLoginStatus();
    // 获取设备映射
    const fetchDeviceMap = async () => {
      try {
        const res = await httpGet('/api/Ems/Config/GetAllDeviceMap');
        if (res.code === 0) {
          setDeviceMap(res.data);
          setDeviceIdToTypeMap(createIdToTypeMap(res.data));
        }
        const configRes = await httpGet('/api/Ems/Config/GetMemoryDevice');
        if (configRes.code === 0) {
          setACInputPower(configRes.data?.acInputMaxPower, configRes.data?.acInputDefaultPower);
        }
      } catch (error) {
        console.error('获取设备映射失败:', error);
      }
    };

    fetchDeviceMap();
    // fetchDictData();

    console.info?.(`#🏠# -- ${projectType}`);
  }, []);

  return (
    <ConfigProvider locale={locale}>
      {/* <GlobalErrorProvider
        progress={progress}
        progressDCInput={progressDCInput}
        progressACInput={progressACInput}
        progressGridOut={progressGridOut}
      > */}
        <Router />
      {/* </GlobalErrorProvider> */}
    </ConfigProvider>
  );
};
