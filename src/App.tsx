import { Router } from '@/router'
import { useEffect } from 'react';
import { useDeviceStore } from '@/store/deviceStore';
import { httpGet } from '@/shared/http';
import { checkLoginStatus } from './shared/auth';
import { projectType } from './constants';

import '@/styles/index.less'

export const App: React.FC = () => {
  const { setDeviceMap, setACInputPower } = useDeviceStore();

  useEffect(() => {
    checkLoginStatus();
    // 获取设备映射
    const fetchDeviceMap = async () => {
      try {
        const res = await httpGet('/api/Ems/Config/GetAllDeviceMap');
        if (res.code === 0) {
          setDeviceMap(res.data);
        }
        const configRes = await httpGet('/api/Ems/Config/GetMemoryDevice');
        if (configRes.code === 0) {
          setACInputPower(configRes.data?.acInputMaxPower, configRes.data?.acInputDefaultPower);
        }
      } catch (error) {
        console.error('获取设备映射失败:', error);
      }
    };

    fetchDeviceMap();

    console.info?.(`#🏠# -- ${projectType}`)
  }, []);

  return <Router />
}
