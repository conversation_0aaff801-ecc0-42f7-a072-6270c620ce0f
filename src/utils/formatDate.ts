import { format } from 'date-fns'

export function formatDate(date: number | string | Date = 0, formatStr = 'yyyy-MM-dd HH:mm:ss') {
  if (typeof date === 'string' || typeof date === 'number') {
    if (Number.isNaN(Number(date))) {
      return format(new Date(date), formatStr)
    } else {
      return format(new Date(Number(date)), formatStr)
    }
  }
  if (!date) {
    return date
  }
  return format(date, formatStr)
}

/**
 * 格式化时分秒
 * @param ms
 * @returns
 * console.log(formatMilliseconds(7265000)); // 输出：2 时 1 分 5 秒
 */
export function formatMilliseconds(ms: number | string) {
  const totalSeconds = Math.floor(Number(ms) / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours) {
    return `${hours}小时${minutes}分钟${seconds}秒`;
  } else if (minutes) {
    if (seconds > 0) {
      return `${minutes}分钟${seconds}秒`;
    }
    return `${minutes}分钟`;
  }
      return `${minutes}分钟`;
  // return `${seconds}秒`;
}