import { httpGet } from '@/shared/http';
import { message } from 'antd';

export interface DownloadOptions {
  defaultFilename?: string;
  defaultType?: string;
}

/**
 * 从响应头中提取文件名
 * @param response HTTP响应对象
 * @returns 提取到的文件名
 */
export const extractFilenameFromResponse = (response: any): string => {
  let filename = 'download.xlsx'; // 默认文件名
  
  const contentDisposition =
    response.headers?.['content-disposition'] || response.headers?.['Content-Disposition'];

  if (contentDisposition) {
    // 尝试匹配 filename*=UTF-8'' 格式
    let filenameMatch = contentDisposition.match(/filename\*=UTF-8''([^;]+)/i);
    if (filenameMatch) {
      try {
        filename = decodeURIComponent(filenameMatch[1].trim());
        return filename.replace(/['"]/g, '');
      } catch (e) {
        console.warn('UTF-8文件名解码失败:', e);
      }
    }

    // 尝试匹配 filename="filename" 格式
    filenameMatch = contentDisposition.match(/filename="([^"]+)"/i);
    if (filenameMatch) {
      return filenameMatch[1].trim();
    }

    // 尝试匹配 filename=filename 格式（无引号）
    filenameMatch = contentDisposition.match(/filename=([^;]+)/i);
    if (filenameMatch) {
      return filenameMatch[1].trim().replace(/['"]/g, '');
    }
  }

  return filename;
};

/**
 * 通用的文件下载函数
 * @param response HTTP响应对象
 * @param options 下载选项
 */
export const downloadFileFromResponse = (
  response: any,
  options: DownloadOptions = {},
  slient: boolean = false
): void => {
  try {
    const {
      defaultFilename = 'download.xlsx',
      defaultType = 'application/octet-stream'
    } = options;

    // 获取文件名
    const filename = extractFilenameFromResponse(response) || defaultFilename;

    // 确保有正确的数据
    const data = response.data || response;
    if (!data) {
      throw new Error('没有可用的文件数据');
    }

    // 创建Blob对象
    const blob = new Blob([data], { type: defaultType });
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 清理URL对象
    window.URL.revokeObjectURL(url);

    if (!slient) {
      message.success('导出成功');
    }
  } catch (error) {
    if (!slient) {
      message.error('导出失败');
    }
    console.error('文件下载失败:', error);
  }
};

/**
 * 异步下载文件
 * @param url 下载URL
 * @param params 请求参数
 * @param options 下载选项
 */
export const asyncDownloadFile = async (
  url: string,
  params?: any,
  options?: DownloadOptions,
  slient: boolean = false
): Promise<void> => {
  try {
    const response = await httpGet(url, params || {}, {
      format: 'blob',
    });

    downloadFileFromResponse(response, options, slient);
  } catch (error) {
    if (!slient) {
      message.error('导出失败');
    }
    console.error('文件下载失败:', error);
    throw error;
  }
};