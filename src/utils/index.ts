import { isFunction, isNil } from 'lodash'

const getElementById = (id: string) => document.getElementById(id)!

const keyBefore = 'vite-react-ts-'

const setLocalStorage = (key: string, value: any): void => {
  value &&
    localStorage.setItem(
      `${keyBefore}${key}`,
      typeof value === 'string' ? value : JSON.stringify(value),
    )
}

const getLocalStorage = <T>(key: string, isParse = false): any => {
  const cache = localStorage.getItem(`${keyBefore}${key}`) ?? ''
  return isParse ? JSON.parse(cache) : cache
}

const setSessionStorage = (key: string, value: any): void => {
  value &&
    sessionStorage.setItem(
      `${keyBefore}${key}`,
      typeof value === 'string' ? value : JSON.stringify(value),
    )
}

const getSessionStorage = <T>(key: string, isParse = false): any => {
  const cache: any = sessionStorage.getItem(`${keyBefore}${key}`) ?? ''
  return isParse ? JSON.parse(cache) : cache
}

export { getElementById, setLocalStorage, getLocalStorage, setSessionStorage, getSessionStorage }


/**
 * 处理空值，返回默认占位符
 * @param value 需要处理的值
 * @param placeholder 自定义占位符，默认为 '--'
 */
export const formatNullValue = (value: any, placeholder = '--'): string | number => {
  return isNil(value) ? placeholder : value;
};

export const formatterValue = (item: any, unit = "") => {
  let value = item.value;
  if (isNil(value)) {
    return '--';
  }
  if (isFunction(item.formatter)) {
    value = item.formatter(item.value);
  }
  return `${value} ${unit}`;
}