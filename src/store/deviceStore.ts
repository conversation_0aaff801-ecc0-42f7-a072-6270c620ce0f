import { create } from 'zustand'

interface DeviceMap {
  id: string
  deviceID?: string
  name: string
  type: string
  pid: string
  descript: string
  isVirtual: boolean
  powerType: string
  capacity: number
}

interface DeviceValue {
  device: {
    name: string
    deviceID: string
    type: string
    status: string
    lastStatus: string
  }
  itemList: Array<{
    name: string
    description: string
    value: number
    lastValue: number | null
  }>
}

interface DeviceState {
  isDeviceOn: boolean
  systemStatus: number
  gpsDevice: Record<string, any>
  cellularDevice: Record<string, any>
  acInputDefaultPower?: number
  acInputMaxPower?: number
  deviceMap: DeviceMap[]
  deviceValues: string[]
  setSystemStatus: (status: number) => void
  setACInputPower: (acInputMaxPower: number, acInputDefaultPower: number) => void
  setDeviceStatus: (status: boolean) => void
  setGpsDevice: (d: Record<string, any>) => void
  setCellularDevice: (d: Record<string, any>) => void
  setDeviceMap: (devices: DeviceMap[]) => void
  setDeviceValues: (values: string[]) => void
  getDevicesByType: (type: string) => DeviceMap[]
}

export const useDeviceStore = create<DeviceState>((set, get) => ({
  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  systemStatus: 1,
  acInputMaxPower: 380,
  acInputDefaultPower: 220,
  isDeviceOn: true,
  gpsDevice: {},
  cellularDevice: {},
  deviceMap: [],
  deviceValues: [],
  setSystemStatus: (status) => {
    set({ systemStatus: status, isDeviceOn: status !== 1 });
  },
  setACInputPower: (acInputMaxPower = 380, acInputDefaultPower = 220) => {
    set({ acInputMaxPower, acInputDefaultPower });
  },
  setDeviceStatus: (status) => set({ isDeviceOn: status }),
  setGpsDevice: (d) => set({ gpsDevice: d }),
  setCellularDevice: (d) => set({ cellularDevice: d }),
  setDeviceMap: (devices) => {
    set({ deviceMap: devices });
    const ids = devices?.map?.(item => item.id);
    set({ deviceValues: ids })
  },
  setDeviceValues: (values) => set({ deviceValues: values }),
  getDevicesByType: (type) => get().deviceMap.filter(device => device.type === type)
}))