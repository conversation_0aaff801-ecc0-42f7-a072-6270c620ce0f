import { create } from 'zustand'

export interface DeviceMap {
  id: string
  deviceID?: string
  name: string
  type: string
  pid: string
  descript: string
  isVirtual: boolean
  powerType: string
  capacity: number;
  status?: string;
}

interface DeviceValue {
  device: {
    name: string
    deviceID: string
    type: string
    status: string
    lastStatus: string
  }
  itemList: Array<{
    name: string
    description: string
    value: number
    lastValue: number | null
  }>
}

interface DeviceState {
  isDeviceOn: boolean
  systemStatus: number
  gpsDevice: Record<string, any>
  cellularDevice: Record<string, any>
  acInputDefaultPower?: number
  acInputMaxPower?: number
  deviceIdToTypeMap: Record<string, string>
  deviceMap: DeviceMap[]
  deviceValues: string[]
  setSystemStatus: (status: number) => void
  setACInputPower: (acInputMaxPower: number, acInputDefaultPower: number) => void
  setDeviceStatus: (status: boolean) => void
  setGpsDevice: (d: Record<string, any>) => void
  setCellularDevice: (d: Record<string, any>) => void
  setDeviceMap: (devices: DeviceMap[]) => void
  setDeviceValues: (values: string[]) => void
  getDevicesByType: (type: string) => DeviceMap[]
  setDeviceIdToTypeMap: (map: Record<string, string>) => void
  getTypeById: (id: string) => string | null
}

export const useDeviceStore = create<DeviceState>((set, get) => ({
  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电 6 为直流供电
  systemStatus: 1,
  acInputMaxPower: 380,
  acInputDefaultPower: -220,
  isDeviceOn: true,
  gpsDevice: {},
  cellularDevice: {},
  deviceIdToTypeMap: {},
  deviceMap: [],
  deviceValues: [],
  setSystemStatus: (status) => {
    set({ systemStatus: status, isDeviceOn: status !== 1 });
  },
  setACInputPower: (acInputMaxPower = 380, acInputDefaultPower = -220) => {
    set({ acInputMaxPower, acInputDefaultPower });
  },
  setDeviceStatus: (status) => set({ isDeviceOn: status }),
  setGpsDevice: (d) => set({ gpsDevice: d }),
  setCellularDevice: (d) => set({ cellularDevice: d }),
  setDeviceMap: (devices) => {
    set({ deviceMap: devices });
    const ids = devices?.map?.(item => item.id);
    set({ deviceValues: ids })
  },
  setDeviceValues: (values) => set({ deviceValues: values }),
  getDevicesByType: (type) => get().deviceMap.filter(device => device.type === type),
  setDeviceIdToTypeMap: (map: any) => set({ deviceIdToTypeMap: map }),
  getTypeById: (id: string) => get().deviceIdToTypeMap[id]||null,
}))

/**
 * 创建id到type的映射表
 * @param data - 原始设备数据数组
 * @returns 映射表，key为设备id，value为对应的type
 */
export function createIdToTypeMap(data: DeviceMap[]): Record<string, string> {
    return data.reduce((map, item) => {
        map[item.id] = item.type;
        return map;
    }, {} as Record<string, string>);
}
