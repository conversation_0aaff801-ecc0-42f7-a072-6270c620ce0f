import { create } from 'zustand'

// 字典配置接口定义
interface DictConfig {
  Match: DictMatch[]
}

// 字典类型匹配项接口
interface DictMatch {
  Type: string
  ItemList: DictItem[]
}

// 字典项接口
interface DictItem {
  Name: string[]
  Dict: Record<string, string>
}

// 字典存储状态接口
interface DictState {
  // 字典配置
  dictConfig: DictConfig
  // 是否已加载字典数据
  isLoaded: boolean
  
  // 设置整个字典配置
  setDictConfig: (config: DictConfig) => void
  // 设置加载状态
  setLoaded: (loaded: boolean) => void
  
  // 获取字典项 - 根据类型和名称
  getDictItem: (type: string, name: string) => DictItem | undefined
  // 获取字典值 - 根据类型、名称和键
  getDictValue: (type: string, name: string, key: string) => string
  // 获取特定类型的所有字典项
  getDictByType: (type: string) => DictItem[]
  // 检查指定类型是否存在
  hasType: (type: string) => boolean
  // 检查指定类型和名称的字典项是否存在
  hasName: (type: string, name: string) => boolean
}

// 创建字典存储
export const useDictStore = create<DictState>((set, get) => ({
  // 初始字典配置
  dictConfig: {
    Match: []
  },
  isLoaded: false,

  // 设置整个字典配置
  setDictConfig: (config) => {
    set({ dictConfig: config });
  },

  // 设置加载状态
  setLoaded: (loaded) => {
    set({ isLoaded: loaded });
  },

  // 获取特定类型的所有字典项
  getDictByType: (type) => {
    const { dictConfig } = get();
    const matchItem = dictConfig.Match.find(m => m.Type === type);
    return matchItem ? matchItem.ItemList : [];
  },

  // 获取字典项 - 根据类型和名称
  getDictItem: (type, name) => {
    const dictItems = get().getDictByType(type);
    return dictItems.find(item => item.Name.includes(name));
  },

  // 获取字典值 - 根据类型、属性名称和属性值
  getDictValue: (type, name, value) => {
    const dictItem = get().getDictItem(type, name);
    if (!dictItem) return value;
    return dictItem.Dict[value] || value;
  },

  // 检查指定类型是否存在
  hasType: (type) => {
    const { dictConfig } = get();
    return dictConfig.Match.some(m => m.Type === type);
  },

  // 检查指定类型和名称的字典项是否存在
  hasName: (type, name) => {
    return !!get().getDictItem(type, name);
  }
}));

// 示例用法:
// 1. 初始化字典
// useDictStore.getState().setDictConfig(dictConfigData);
// 
// 2. 获取字典值
// const status = useDictStore.getState().getDictValue('PCS', 'PCS_Operation_Status', '0'); // 返回 "关机"
// 
// 3. 检查字典类型是否存在
// const hasType = useDictStore.getState().hasType('BMS'); // 返回 true 