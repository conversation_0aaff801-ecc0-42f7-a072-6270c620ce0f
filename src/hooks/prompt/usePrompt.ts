import { useEffect, useCallback, useRef } from 'react';
import { useBlocker, BlockerFunction } from 'react-router-dom';

export function usePrompt(message: string | BlockerFunction, when = true) {
  const blockerFn = useCallback<BlockerFunction>(
    (tx) => {
      const shouldBlock = typeof message === 'function' ? message(tx) : window.confirm(message);

      return shouldBlock;
    },
    [message],
  );

  const blocker = useBlocker(when ? blockerFn : false);

  return {
    isBlocked: blocker.state === 'blocked',
  };
}
