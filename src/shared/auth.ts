import { USER_INFO_KEY } from './config';
import { HttpClient } from './http/http-client';
import { RhStorage } from './storage';


const storage = new RhStorage({ expires: 3600 * 24 * 365 });


export const toLoginPage = () => {
    const hostName = window.location.hostname;
    if (window.location.pathname.indexOf('login') !== -1) {
        return;
    }
    window.location.href = `${window.location.origin}/login`;
};

/**
 * 获取用户信息，判断是否已登录
 */
export const getUserInfo = async () => {
    const httpClient = new HttpClient({ silent: true });
    try {
        const res: Record<string, any> = await httpClient.request({
            path: '/api/v1/loginUser/info',
            method: 'GET',
            query: {},
        });
        storage.set(USER_INFO_KEY, res?.data);
        return res?.data;
    } catch (err) {
        return {};
    }
};

export const setUserInfo = (data: any = {}) => {
    storage.set('token', data.token);
    storage.set('freshToken', data.freshToken);
    storage.set('userName', data.userName);
}

export const clearUserInfo = () => {
    storage.remove('token');
    storage.remove('freshToken');
    storage.remove('userName');
}


export const checkLoginStatus = async () => {
    const token = storage.get('token');
    if (!token) {
        toLoginPage();
        return false;
    }
    return true
    /*   const userInfo = await getUserInfo();
      if (!userInfo?.userId) {
          // toLoginPage();
          return false;
      }
      return true; */
};