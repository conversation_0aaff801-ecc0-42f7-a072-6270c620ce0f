/**
 * <AUTHOR>
 * @description  Http Adapter 实现类，解耦
 * 默认使用 DefaultHttpAdapterImp
 */

import { message } from 'antd'
import { AxiosError, AxiosInstance, AxiosResponse } from 'axios'

import { HttpAdapter } from '@/shared/http/interface'

import {
  ERR_MESSAGE_SHOW_DURATION,
  // ERR_MESSAGE_SHOW_DURATION,
  RES_PERMISSION_DENIED_CODE,
  RES_SUCCESS_DEFAULT_CODE,
  RES_UNAUTHORIZED_CODE,
} from './http-code'

// declare let BASE_URL: string;
// declare let MOCK: string;

export interface Token {
  accessToken?: string
  refreshToken?: string
  tokenExpireTime?: string
  tokenExpiresIn?: number
  userId?: number
  ispassword?: boolean
}

export default class HttpAdapterImp implements HttpAdapter {
  baseURL: string
  constructor() {
    this.baseURL = import.meta.env.VITE_BASE_URL || ''
  }

  /**
   * 拦截器
   */
  interceptRequestConfig(instance: AxiosInstance, axiosConfig = {}) {
    instance.interceptors.request.use(
      async (config: any) => {
        // const token = await this.getToken();
        // if (token) {
        //   // ['X-Access-Token'] is a custom headers key
        //   config.headers.token = token;
        // }
        // if (!config.headers['Content-Type']) {
        //   config.headers['Content-Type'] = 'application/json'
        // }

        return config
      },
      (error) => {
        this.handleErrorResponse(error, axiosConfig)
      },
    )

    // response interceptor
    instance.interceptors.response.use(
      async (response: AxiosResponse<any>) => this.handleResponse(response),
      (error) => {
        return this.handleErrorResponse(error, axiosConfig)
      },
    )
  }

  async handleLogout() {
    console.log('🚀 ~ HttpAdapterImp ~ handleLogout ~ signOut:')
  }

  async handleResponse(response: AxiosResponse<any> & { config: { silent?: boolean, responseType?: string } }) {
    const res = response.data || {}
    const { silent = false, responseType } = response.config

    // 对于blob响应，返回完整的响应对象以便访问headers
    if (responseType === 'blob') {
      return response
    }

    if (response.headers['content-type']?.includes('text/xml')) {
      return res
    }
    return res
    /*     if (res.code === 200 || res.code === RES_SUCCESS_DEFAULT_CODE) {
          // 成功
          return res
        } else if (res.code === RES_UNAUTHORIZED_CODE) {
          if (!silent) {
            message.error('您已经登出，您可以取消以停留在此页面，或再次登录', ERR_MESSAGE_SHOW_DURATION)
          }
          this.handleLogout()
        } else if (res.code === RES_PERMISSION_DENIED_CODE) {
          // token不存在,请重新登录账户
          this.handleLogout()
        } 

    return this.handleErrorResponse(res, { silent })*/
  }

  handleErrorResponse(
    error: AxiosError<Record<string, any>> & { desc?: string; msg?: string },
    axiosConfig: { silent?: any },
  ): Promise<any> {
    const errorCode = error.response ? error.response.data?.code : error.code
    if (errorCode === RES_UNAUTHORIZED_CODE) {
      // 10300 TOKEN 无效
      return this.handleLogout()
    }
    // 后端不统一处理
    const respDataMsg = error.response?.data?.['msg']
    const msg = respDataMsg || error.msg || error.message || error.desc
    if (!axiosConfig?.silent && msg) {
      message.error(msg, ERR_MESSAGE_SHOW_DURATION)
    }

    return Promise.reject(error.response?.data ?? error)
  }
}
