export default class CommonMethod {
  //进入全屏
  static fullScreen = (element: any) => {
    if ((window as any).ActiveXObject) {
      const WsShell = new (window as any).ActiveXObject('WScript.Shell')
      WsShell.SendKeys('{F11}')
    }
    //HTML W3C 提议
    else if (element.requestFullScreen) {
      element.requestFullScreen()
    }
    //IE11
    else if (element.msRequestFullscreen) {
      element.msRequestFullscreen()
    }
    // Webkit (works in Safari5.1 and Chrome 15)
    else if (element.webkitRequestFullScreen) {
      element.webkitRequestFullScreen()
      // setTimeout(()=>{
      //   console.log(isFullScreen());
      // },100)
    }
    // Firefox (works in nightly)
    else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen()
    }
  }
  //退出全屏
  static fullExit = (element: any) => {
    //IE ActiveXObject
    if ((window as any).ActiveXObject) {
      const WsShell = new (window as any).ActiveXObject('WScript.Shell')
      WsShell.SendKeys('{F11}')
    }
    //HTML5 W3C 提议
    else if (element.requestFullScreen) {
      document.exitFullscreen()
    }
    //IE 11
    else if (element.msRequestFullscreen) {
      ;(document as any).msExitFullscreen()
    }
    // Webkit (works in Safari5.1 and Chrome 15)
    else if (element.webkitRequestFullScreen) {
      ;(document as any).webkitCancelFullScreen()
    }
    // Firefox (works in nightly)
    else if (element.mozRequestFullScreen) {
      ;(document as any).mozCancelFullScreen()
    }
  }
  //当前是否全屏模式
  static isFullScreen = () => {
    return (
      (document as any).fullScreen ||
      (document as any).mozFullScreen ||
      (document as any).webkitIsFullScreen ||
      (document as any).msFullscreenElement
    )
  }
  // 获取放大缩小比例
  static getScale() {
    const w = window.innerWidth / 1920
    const h = window.innerHeight / 1080
    return w < h ? w : h
  }

  static setScale() {
    const bigMainDom = document.getElementById('bigMain')
    bigMainDom &&
      (bigMainDom.style.transform = 'scale(' + CommonMethod.getScale() + ') translate(0, 0)')
  }
}
