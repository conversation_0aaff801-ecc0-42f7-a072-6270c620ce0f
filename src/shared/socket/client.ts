/**
 * <AUTHOR>
 * @homepage http://giscafer.com
 * @created 2023-09-13 11:28:01
 * @description 封装 websocket 客户端
 */

import { ConnectionFactory } from './websocket'

export interface ConnectionType {
  open(): void
  close(): void
  send(data: string): void
  isOpen(): boolean
  onOpen(callback: (e?: any) => void): void
  onReceive(callback: (data: string) => void): void
  onClose(callback: (e?: any) => void): void
}

type OpenOptionType = {
  onCloseCallback?: (error?: any) => void
  onOpenCallback?: (connection: ConnectionType) => void
  onReceiveCallback?: (msg?: string) => void
  /**
   * 自定义心跳函数
   * @returns
   */
  heartbeatFn?: (connection: ConnectionType) => void
}

export const protocols = undefined

export const msgInputUnknown = '0'
export const msgInput = '1'
export const msgPing = '2'
// 心跳时间间隔
export const pingInterval = 10 * 1000

export const msgUnknownOutput = '0'
export const msgOutput = '1'
export const msgPong = '2'
export const msgSetReconnect = '5'

export class WebSocketClient {
  connectionFactory: ConnectionFactory
  args: string
  authToken: string
  reconnect: number

  constructor(connectionFactory: ConnectionFactory, args: string = '', authToken: string = '') {
    this.connectionFactory = connectionFactory
    this.args = args
    this.authToken = authToken
    this.reconnect = -1
  }

  open(option: OpenOptionType = {}) {
    const { onOpenCallback, onCloseCallback, onReceiveCallback, heartbeatFn } = option
    let connection = this.connectionFactory.create()
    let pingTimer: number
    let reconnectTimeout: number

    const setup = () => {
      connection.onOpen(() => {
        onOpenCallback?.(connection)

        pingTimer = window.setInterval(() => {
          if (typeof heartbeatFn === 'function') {
            heartbeatFn(connection)
          } else {
            connection.send(JSON.stringify({ type: 'register-heartbeat' }))
          }
        }, pingInterval)
      })

      connection.onReceive((data) => {
        onReceiveCallback?.(data)
        const d = JSON.parse(data)
        const type = d?.type
        switch (type) {
          case msgPong:
            break
          case msgSetReconnect:
            const autoReconnect = d.autoReconnect || 3
            console.log('Enabling reconnect: ' + autoReconnect + ' seconds')
            this.reconnect = autoReconnect
            break
        }
      })

      connection.onClose((error: any) => {
        clearInterval(pingTimer)
        onCloseCallback?.(error)
        console.log('ConnectionType error', error)
        if (this.reconnect > 0) {
          reconnectTimeout = window.setTimeout(() => {
            connection = this.connectionFactory.create()
            setup()
          }, this.reconnect * 1000)
        }
      })

      connection.open()
    }

    setup()
    return () => {
      clearTimeout(reconnectTimeout)
      connection.close()
    }
  }
}
