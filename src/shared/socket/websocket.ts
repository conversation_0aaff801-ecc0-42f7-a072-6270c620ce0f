/**
 * <AUTHOR>
 * @homepage http://giscafer.com
 * @created 2023-04-23 11:04:24
 * @description WebSocket Factory
 */

export class ConnectionFactory {
  url: string;
  protocols: string[];

  constructor(url: string, protocols: string[] = []) {
    this.url = url;
    this.protocols = protocols;
  }

  create(): Connection {
    return new Connection(this.url, this.protocols);
  }
}

export class Connection {
  bare: WebSocket;

  constructor(url: string, protocols: string[]) {
    this.bare = new WebSocket(url, protocols);
  }

  open() {
    // nothing todo for websocket
  }

  close() {
    this.bare.close();
  }

  send(data: string) {
    this.bare.send(data);
  }

  isOpen(): boolean {
    // eslint-disable-next-line eqeqeq
    if (this.bare.readyState == WebSocket.CONNECTING || this.bare.readyState == WebSocket.OPEN) {
      return true;
    }
    return false;
  }

  onOpen(callback: () => void) {
    this.bare.onopen = (event) => {
      callback?.();
    };
  }

  onReceive(callback: (data: string) => void) {
    this.bare.onmessage = (event) => {
      callback?.(event.data);
    };
  }

  onClose(callback: (e?: any) => void) {
    this.bare.onclose = (event) => {
      callback?.(event);
    };
  }
}
