/**
 * <AUTHOR>
 * @homepage http://giscafer.com
 * @created 2022-07-22 19:49:45
 * @description 通用请求
 */

import { useRequest } from 'ahooks'
import { template, uniqueId } from 'lodash'
import { useCallback } from 'react'

import httpClient from '@/shared/http'

export type DataSourceRequestType = {
  api?: string
  params?: any
  deps?: any[] // 之前 useSWR 遗留的传参，改造之后没用上，待观察
  apiMethod?: string
  apiParams?: Record<string, any>
  silent?: boolean
}

export default function useDataSource(
  { api, params, apiParams, apiMethod = 'GET', silent = false }: DataSourceRequestType,
  options: Record<string, any> = {},
) {
  const handleRequest = useCallback(async () => {
    let resp: any = null
    const queryParams = params ?? {}

    const path = template(api)(apiParams ?? {}) // 新写法 ${}

    const extraConfig: Record<string, any> = {
      headers: {
        'x-request-id': uniqueId('use_datasource_'),
      },
      silent,
    }

    if (apiMethod?.toUpperCase() === 'GET') {
      extraConfig.query = queryParams
    } else {
      extraConfig.body = queryParams
    }

    if (!api) {
      return { error: 'api不能为空' }
    }

    resp = await httpClient.request({
      path,
      method: apiMethod,
      ...extraConfig,
    })

    return resp?.data ?? resp?.payload
  }, [params, api, apiParams, silent, apiMethod])

  const reqOptions = {
    debounceWait: 300,
    refreshOnWindowFocus: false,
    retryCount: 3,
    refreshDeps: [JSON.stringify(params), JSON.stringify(apiParams || {}), api],
    ...options,
  }

  const resp = useRequest<any, any[]>(handleRequest, reqOptions)

  return { ...resp }
}
