import React from 'react';
import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import PageLoading from '@/components/PageLoading';
import { ErrorPage } from '@/pages/ErrorPage';
import { projectType, projectEnv } from './constants';

type Route = {
  label: string;
  path: string;
  element: Promise<React.FC>;
  children?: Route[];
};

const getHomePage = () => {
  switch (projectType) {
    case 'jiangxi':
      return import('./pages/Home/JiangXi').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'liyuan':
      return import('./pages/Home/LiYuan').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'hangzhou':
      return import('./pages/Home/HangZhou').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'lafangda':
      return import('./pages/Home/Lafangda').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'guiyang':
      return import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'jinan':
      return import('./pages/Home/JiNan').then(({ HomeScreenPage }) => HomeScreenPage);
    default:
      return import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage);
  }
};

export const routes: Route[] = [
  {
    label: '首页',
    path: '/',
    element: getHomePage(),
  },
  {
    label: '首页',
    path: '/test-home',
    element: import('./pages/Home/CustomHome').then(({ HomeScreenPage }) => HomeScreenPage),
  },
  {
    label: '设备状态',
    path: '/device-status',
    element: import('./pages/DeviceStatus').then(({ DeviceStatus }) => DeviceStatus),
  },
  {
    label: '告警',
    path: '/alarm',
    element: import('./pages/Alarm').then(({ Alarm }) => Alarm),
  },

  {
    label: '业务统计',
    path: '/business-statistics',
    element: import('./pages/BusinessStatistics').then(
      ({ BusinessStatistics }) => BusinessStatistics,
    ),
  },
  {
    label: '系统运维',
    path: '/system-maintenance',
    element: import('./pages/SystemMaintenance').then(({ SystemMaintenance }) => SystemMaintenance),
    children: [
      {
        label: 'PCS监控',
        path: '/system-maintenance/pcs',
        element: import('./pages/SystemMaintenance/PCSMonitoring').then(
          ({ PCSMonitoring }) => PCSMonitoring,
        ),
      },
      {
        label: 'BMS监控',
        path: '/system-maintenance/bms',
        element: import('./pages/SystemMaintenance/BMSMonitoring').then(
          ({ BMSMonitoring }) => BMSMonitoring,
        ),
      },
      {
        label: '充电桩',
        path: '/system-maintenance/charging-pile',
        element: import('./pages/SystemMaintenance/ChargeGun').then(
          ({ ChargeGunPage }) => ChargeGunPage,
        ),
      },
      {
        label: '水冷机组',
        path: '/system-maintenance/water-cooling',
        element: import('./pages/SystemMaintenance/WaterCooling').then(
          ({ WaterCoolingPage }) => WaterCoolingPage,
        ),
      },
      {
        label: '历史趋势',
        path: '/system-maintenance/historical-trend',
        element: import('./pages/SystemMaintenance/HistoricalTrend').then(
          ({ HistoricalTrend }) => HistoricalTrend,
        ),
      },
      {
        label: 'PCS日志',
        path: '/system-maintenance/pcs-logs',
        element: import('./pages/SystemMaintenance/PCSLogs').then(({ PCSLogs }) => PCSLogs),
      },
      {
        label: '系统升级',
        path: '/system-maintenance/system-upgrade',
        element: import('./pages/SystemMaintenance/SystemUpgrade').then(
          ({ SystemUpgrade }) => SystemUpgrade,
        ),
      },
      {
        label: '系统升级详情',
        path: '/system-maintenance/system-upgrade/detail',
        element: import('./pages/SystemMaintenance/SystemUpgrade/detail').then(
          ({ SystemUpgradeDetail }) => SystemUpgradeDetail,
        ),
      },
      {
        label: '指令日志',
        path: '/system-maintenance/command-logs',
        element: import('./pages/SystemMaintenance/CommandLogs').then(
          ({ CommandLogs }) => CommandLogs,
        ),
      },
      {
        label: '升级日志',
        path: '/system-maintenance/upgrade-logs',
        element: import('./pages/SystemMaintenance/UpgradeLogs').then(
          ({ UpgradeLogs }) => UpgradeLogs,
        ),
      },
      {
        label: '运行日志',
        path: '/system-maintenance/run-logs',
        element: import('./pages/SystemMaintenance/RunLogs').then(({ RunLogs }) => RunLogs),
      },
      {
        label: '数据转发',
        path: '/system-maintenance/data-forwarding',
        element: import('./pages/SystemMaintenance/DataForwarding').then(
          ({ DataForwarding }) => DataForwarding,
        ),
      },
      {
        label: '数据转发详情',
        path: '/system-maintenance/data-forwarding/detail',
        element: import('./pages/SystemMaintenance/DataForwarding/detail/info').then(
          ({ DataForwardingDetail }) => DataForwardingDetail,
        ),
      },
      {
        label: '数据转发编辑',
        path: '/system-maintenance/data-forwarding/edit',
        element: import('./pages/SystemMaintenance/DataForwarding/detail/edit').then(
          ({ DataForwardingDetailEdit }) => DataForwardingDetailEdit,
        ),
      },
      {
        label: '数据转发导入导出',
        path: '/system-maintenance/data-forwarding/import-export',
        element: import('./pages/SystemMaintenance/DataForwarding/detail/ImportAndExport').then(
          ({ ImportAndExport }) => ImportAndExport,
        ),
      },
      // {
      //   label: 'Test',
      //   path: '/system-maintenance/test',
      //   element: import('./pages/TestPage').then(({ TestPage }) => TestPage),
      // },
    ],
  },
  ...(projectEnv !== 'production'
    ? [
        {
          label: 'jiangxi',
          path: '/jx',
          element: import('./pages/Home/JiangXi').then(({ HomeScreenPage }) => HomeScreenPage),
        },
        {
          label: 'liyuan',
          path: '/ly',
          element: import('./pages/Home/LiYuan').then(({ HomeScreenPage }) => HomeScreenPage),
        },
        {
          label: 'hangzhou',
          path: '/hz',
          element: import('./pages/Home/HangZhou').then(({ HomeScreenPage }) => HomeScreenPage),
        },
        {
          label: 'lafangda',
          path: '/lfd',
          element: import('./pages/Home/Lafangda').then(({ HomeScreenPage }) => HomeScreenPage),
        },
        {
          label: 'guiyang',
          path: '/gy',
          element: import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage),
        },
        {
          label: 'jinan',
          path: '/jn',
          element: import('./pages/Home/JiNan').then(({ HomeScreenPage }) => HomeScreenPage),
        },
      ]
    : []),

  /* {
    label: 'About',
    path: '/about',
    element: import('./pages/AboutPage').then(({ AboutPage }) => AboutPage),
  },
  {
    label: 'Users',
    path: '/users',
    element: import('./pages/UsersPage').then(({ UsersPage }) => UsersPage),
  },*/
  {
    label: 'Test',
    path: '/test',
    element: import('./pages/TestPage/index').then(({ TestPage }) => TestPage),
  },
];

const router = createBrowserRouter([
  {
    path: '/login',
    lazy: () => import('./pages/Login').then(({ Login }) => ({ Component: Login })),
  },
  {
    path: '/',
    lazy: () => import('@/layout').then(({ Layout }) => ({ Component: Layout })),
    errorElement: <ErrorPage />,
    children: routes.map((route) => ({
      path: route.path,
      lazy: () => route.element.then((Component) => ({ Component })),
      children: route.children?.map((child) => ({
        path: child.path,
        lazy: () => child.element.then((Component) => ({ Component })),
      })),
    })),
  },
]);

export const Router = () => {
  return <RouterProvider router={router} fallbackElement={<PageLoading />} />;
};
