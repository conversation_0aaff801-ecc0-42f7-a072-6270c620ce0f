import React from 'react'
import { RouterProvider, createBrowserRouter } from 'react-router-dom'
import PageLoading from '@/components/PageLoading';
import { ErrorPage } from '@/pages/ErrorPage'
import { projectType } from './constants';

type Route = {
  label: string
  path: string
  element: Promise<React.FC>
  children?: Route[]
}

const getHomePage = () => {
  switch (projectType) {
    case 'jiangxi':
      return import('./pages/Home/JiangXi').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'liyuan':
      return import('./pages/Home/LiYuan').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'hangzhou':
      return import('./pages/Home/HangZhou').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'lafangda':
      return import('./pages/Home/Lafangda').then(({ HomeScreenPage }) => HomeScreenPage);
    case 'guiyang':
      return import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage);
    default:
      return import('./pages/Home/GuiYang').then(({ HomeScreenPage }) => HomeScreenPage);
  }
};

export const routes: Route[] = [
  {
    label: '首页',
    path: '/',
    element: getHomePage(),
  },
  {
    label: '设备状态',
    path: '/device-status',
    element: import('./pages/DeviceStatus').then(({ DeviceStatus }) => DeviceStatus),
  },
  {
    label: '告警',
    path: '/alarm',
    element: import('./pages/Alarm').then(({ Alarm }) => Alarm),
  },

  {
    label: '业务统计',
    path: '/business-statistics',
    element: import('./pages/BusinessStatistics').then(({ BusinessStatistics }) => BusinessStatistics),
  },
  {
    label: '系统运维',
    path: '/system-maintenance',
    element: import('./pages/SystemMaintenance').then(({ SystemMaintenance }) => SystemMaintenance),
    children: [
      {
        label: 'PCS监控',
        path: '/system-maintenance/pcs',
        element: import('./pages/SystemMaintenance/PCSMonitoring').then(({ PCSMonitoring }) => PCSMonitoring),
      },
      {
        label: 'BMS监控',
        path: '/system-maintenance/bms',
        element: import('./pages/SystemMaintenance/BMSMonitoring').then(({ BMSMonitoring }) => BMSMonitoring),
      },
      {
        label: '充电桩',
        path: '/system-maintenance/charging-pile',
        element: import('./pages/SystemMaintenance/ChargeGun').then(
          ({ ChargeGunPage }) => ChargeGunPage
        ),
      },
      {
        label: '水冷机组',
        path: '/system-maintenance/water-cooling',
        element: import('./pages/SystemMaintenance/WaterCooling').then(
          ({ WaterCoolingPage }) => WaterCoolingPage
        ),
      },
      {
        label: '指令日志',
        path: '/system-maintenance/command-logs',
        element: import('./pages/SystemMaintenance/CommandLogs').then(
          ({ CommandLogs }) => CommandLogs
        ),
      },
    ],
  },

  /* {
    label: 'About',
    path: '/about',
    element: import('./pages/AboutPage').then(({ AboutPage }) => AboutPage),
  },
  {
    label: 'Users',
    path: '/users',
    element: import('./pages/UsersPage').then(({ UsersPage }) => UsersPage),
  },
  {
    label: 'Test',
    path: '/test',
    element: import('./pages/TestPage').then(({ TestPage }) => TestPage),
  }, */
]

const router = createBrowserRouter([
  {
    path: '/login',
    lazy: () => import('./pages/Login').then(({ Login }) => ({ Component: Login })),
  },
  {
    path: '/',
    lazy: () => import('@/layout').then(({ Layout }) => ({ Component: Layout })),
    errorElement: <ErrorPage />,
    children: routes.map((route) => ({
      path: route.path,
      lazy: () => route.element.then((Component) => ({ Component })),
      children: route.children?.map((child) => ({
        path: child.path,
        lazy: () => child.element.then((Component) => ({ Component })),
      })),
    })),
  },
])

export const Router = () => {
  return <RouterProvider router={router} fallbackElement={<PageLoading />} />
}
