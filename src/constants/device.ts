export const PCS_RUN_STATE_MAP = {
    0: '关机',
    1: '母线AC软启',
    2: '母线DC软启',
    4: '逆变软启',
    8: '母线软件软启',
    16: '恒母线电压输出',
    32: '并网逆变(补能)',
    64: '并网逆变(放电)',
    128: '离网逆变',
    256: '待机状态',
    512: '开环逆变状态',
    1024: 'DCDC软启',
    2048: 'DCDC放电'
};

export const getPcsRunStateLabel = (state: keyof typeof PCS_RUN_STATE_MAP) => {
    return PCS_RUN_STATE_MAP[state] || '未知状态';
};

export const gunCCCSMap: Record<number, string> = {
    0: '未连接',
    1: '连接',
};
// AB补能枪在线状态
export const getGunCCCSText = (status: number): string => {
    return gunCCCSMap[status] || '--';
};

export const gunCCSGSMap: Record<number, string> = {
    0: '初始化状态',
    1: '空闲状态',
    2: '等待启动',
    3: '启动中',
    4: '供电中',
    5: '停止供电中',
    6: '供电结算中',
    7: '供电完成未拔枪',
    8: '启动失败',
    9: '故障状态'
};

// AB枪供电状态
export const getGunCCGSText = (status: number): string => {
    return gunCCSGSMap[status] || '--';
};
