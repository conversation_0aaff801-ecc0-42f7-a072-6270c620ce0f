export const ALARM_TYPES = [
    { value: 1, label: '未恢复告警', title: '' },
    { value: 2, label: '全部告警', title: '' },
];

export const getAlarmTypeLabel = (type: number) => {
    return ALARM_TYPES.find(item => item.value === type)?.label || '-';
};

export const ALARM_STATUS = [
    { value: 0, label: '激活', title: '' },
    { value: 1, label: '恢复', title: '' },
] as const;

export const getAlarmStatusLabel = (status: number) => {
    return ALARM_STATUS.find(item => item.value === status)?.label || '-';
};

export const POWER_TYPES = [
    { value: 'AC', label: '交流', title: '' },
    { value: 'DC', label: '直流', title: '' },
] as const;

export const getPowerTypeLabel = (type: string) => {
    return POWER_TYPES.find(item => item.value === type)?.label || '-';
};