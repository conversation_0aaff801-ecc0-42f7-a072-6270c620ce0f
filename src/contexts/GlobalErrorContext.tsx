import React, { createContext, useContext, useState, ReactNode, useEffect, useRef } from 'react';
import AlarmModal from '@/components/AlarmModal';
import moment from 'moment';
import { httpPost } from '@/shared/http';
import { getLocalUsername, getUserInfo } from '@/shared/auth';

interface ErrorInfo {
  // type?: string; //区分补能、供电 acpower交流供电 ac 交流补能 acGridOut并网放电 dc 直流补能
  // title?: string;
  // content?: ReactNode;
  [key: string]: string;
}

interface GlobalErrorContextProps {
  err: ErrorInfo | null;
  setError: (e: ErrorInfo) => void;
  progressAll: any;
  setProgressAll: (e: any) => void;
  alarm: ErrorInfo | null;
  setAlarm: (e: ErrorInfo | null) => void;
}

const GlobalErrorContext = createContext<GlobalErrorContextProps>({
  err: null,
  setError: () => {},
  alarm: null,
  setAlarm: () => {},
  progressAll: {},
  setProgressAll: () => {},
});

export const useGlobalError = () => useContext(GlobalErrorContext);
// 不需要进度条的状态
export const notProgressStatusMap = [0, 2, undefined];
//各个类型对应的状态字段
export const typeStatusMap = {
  acOut: 'ACOutStatus',
  dcInput: 'DCInputStatus',
  acInput: 'ACInputStatus',
  acGridOut: 'ACGridOutPowerStatus',
  DCDC: 'DCDCPowerstatus',
  dcOut: 'DCOutStatus',
  // Output:'ACDCOutStatus',//江西环境
};
export const typeMap = {
  ACOut: 'acOut',
  DCInput: 'dcInput',
  ACInput: 'acInput',
  ACGridOut: 'acGridOut',
  DCDC: 'DCDC',
  DCDCOut: 'DCDC',
  DCOut: 'DCOut',
};
export const errorTypeMap = {
  acOut: '交流供电',
  dcInput: '直流补能',
  acInput: '交流补能',
  acGridOut: '并网放电',
  DCDC: '直流供电',
  dcOut: '直流输出',
  // ACDCOut:'直流供电',//江西环境
};
export const GlobalErrorProvider: React.FC<{
  children: ReactNode;
  progress?: any;
  progressDCInput?: any;
  progressACInput?: any;
  progressGridOut?: any;
  progressDCDC?: any;
  progressDCOut?: any;
}> = ({
  children,
  progress,
  progressDCInput,
  progressACInput,
  progressGridOut,
  progressDCDC,
  progressDCOut,
}) => {
  const username = getLocalUsername();
  // 流程运行中的报错信息
  const [err, setError] = useState<ErrorInfo>({});
  // 流程运行中的告警信息（不影响运行）
  const [alarm, setAlarm] = useState<ErrorInfo | null>(null);
  // 轮询定时器 用于停止轮询
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [globalAlarm, setGlobalAlarm] = useState<ErrorInfo | null>(null);
  const [visible, setVisible] = useState<boolean>(false);
  const [progressAll, setProgressAll] = useState<any>({
    acInput: [],
    dcInput: [],
    acGridOut: [],
    acOut: [],
    DCDC: [],
    dcOut: [],
  });

  useEffect(() => {
    // 只在任一progress有值时才处理
    if (
      !progress &&
      !progressDCInput &&
      !progressACInput &&
      !progressGridOut &&
      !progressDCDC &&
      !progressDCOut
    ) {
      return;
    }

    // 创建更新对象，只包含有值的progress
    const updatedProgressAll: any = {};
    if (progress !== undefined) updatedProgressAll.acOut = progress;
    if (progressDCInput !== undefined) updatedProgressAll.dcInput = progressDCInput;
    if (progressACInput !== undefined) updatedProgressAll.acInput = progressACInput;
    if (progressGridOut !== undefined) updatedProgressAll.acGridOut = progressGridOut;
    if (progressDCDC !== undefined) updatedProgressAll.DCDC = progressDCDC;
    if (progressDCOut !== undefined) updatedProgressAll.dcOut = progressDCOut;

    // 更新progressAll状态
    setProgressAll((prev: any) => ({ ...prev, ...updatedProgressAll }));

    // 重置错误状态
    // setError(null);
    try {
      // 当前时间的毫秒表示
      const now = moment().valueOf();
      const diffValue = 5 * 1000;
      // 依次检查有值的progress是否有错误
      // 处理交流供电流程错误
      if (progress && progress.Status && (progress.Status.ErrorMsg || progress.Status.Failed)) {
        //如果错误时间与当前时间相差超过10s则认为时历史报错
        if (now - moment(progress.Status.Time).valueOf() > diffValue) {
          return;
        }
        setError((prev: any) => ({
          ...prev,
          acOut: progress.Status.ErrorMsg || progress.Status.Failed,
          content: progress.Status.ErrorMsg || progress.Status.Failed,
        }));
      }
      // 处理直流补能流程错误
      else if (
        progressDCInput &&
        progressDCInput.Status &&
        (progressDCInput.Status.ErrorMsg || progressDCInput.Status.Failed)
      ) {
        if (now - moment(progressDCInput.Status.Time).valueOf() > diffValue) {
          return;
        }
        setError((prev: any) => ({
          ...prev,
          dc: progressDCInput.Status.ErrorMsg || progressDCInput.Status.Failed,
          content: progressDCInput.Status.ErrorMsg || progressDCInput.Status.Failed,
        }));
      }
      // 处理交流补能流程错误
      else if (
        progressACInput &&
        progressACInput.Status &&
        (progressACInput.Status.ErrorMsg || progressACInput.Status.Failed)
      ) {
        if (now - moment(progressACInput.Status.Time).valueOf() > diffValue) {
          return;
        }
        setError((prev: any) => ({
          ...prev,
          ac: progressACInput.Status.ErrorMsg || progressACInput.Status.Failed,
          mergedACandACGrid: progressACInput.Status.ErrorMsg || progressACInput.Status.Failed,
          content: progressACInput.Status.ErrorMsg || progressACInput.Status.Failed,
        }));
      }
      // 处理并网放电流程错误
      else if (
        progressGridOut &&
        progressGridOut.Status &&
        (progressGridOut.Status.ErrorMsg || progressGridOut.Status.Failed)
      ) {
        if (now - moment(progressGridOut.Status.Time).valueOf() > diffValue) {
          return;
        }
        setError((prev: any) => ({
          ...prev,
          acGridOut: progressGridOut.Status.ErrorMsg || progressGridOut.Status.Failed,
          mergedACandACGrid: progressGridOut.Status.ErrorMsg || progressGridOut.Status.Failed,
          content: progressGridOut.Status.ErrorMsg || progressGridOut.Status.Failed,
        }));
      }
      // 处理DCDC流程错误
      else if (
        progressDCDC &&
        progressDCDC.Status &&
        (progressDCDC.Status.ErrorMsg || progressDCDC.Status.Failed)
      ) {
        if (now - moment(progressDCDC.Status.Time).valueOf() > diffValue) {
          return;
        }
        setError((prev: any) => ({
          ...prev,
          DCDC: progressDCDC.Status.ErrorMsg || progressDCDC.Status.Failed,
          content: progressDCDC.Status.ErrorMsg || progressDCDC.Status.Failed,
        }));
      }
      // 处理DCOut流程错误
      else if (
        progressDCOut &&
        progressDCOut.Status &&
        (progressDCOut.Status.ErrorMsg || progressDCOut.Status.Failed)
      ) {
        if (now - moment(progressDCOut.Status.Time).valueOf() > diffValue) {
          return;
        }
        setError((prev: any) => ({
          ...prev,
          DCOut: progressDCOut.Status.ErrorMsg || progressDCOut.Status.Failed,
          content: progressDCOut.Status.ErrorMsg || progressDCOut.Status.Failed,
        }));
      }
    } catch (error) {}

    // 告警错误处理
    // 处理交流供电流程告警
    if (progress && progress.Status && progress.Status.WarnMsg) {
      setAlarm((prev: any) => ({
        ...prev,
        acOut: progress.Status.WarnMsg,
      }));
    } else if (progressDCInput && progressDCInput.Status && progressDCInput.Status.WarnMsg) {
      setAlarm((prev: any) => ({
        ...prev,
        dc: progressDCInput.Status.WarnMsg,
      }));
    }
    // 处理交流补能流程告警
    else if (progressACInput && progressACInput.Status && progressACInput.Status.WarnMsg) {
      setAlarm((prev: any) => ({
        ...prev,
        ac: progressACInput.Status.WarnMsg,
      }));
    }
    // 处理并网放电流程告警
    else if (progressGridOut && progressGridOut.Status && progressGridOut.Status.WarnMsg) {
      setAlarm((prev: any) => ({
        ...prev,
        acGridOut: progressGridOut.Status.WarnMsg,
      }));
    }
    // 处理DCDC流程告警
    else if (progressDCDC && progressDCDC.Status && progressDCDC.Status.WarnMsg) {
      setAlarm((prev: any) => ({
        ...prev,
        DCDC: progressDCDC.Status.WarnMsg,
      }));
    }
    // 处理DCOut流程告警
    else if (progressDCOut && progressDCOut.Status && progressDCOut.Status.WarnMsg) {
      setAlarm((prev: any) => ({
        ...prev,
        DCOut: progressDCOut.Status.WarnMsg,
      }));
    }
  }, [progress, progressDCInput, progressACInput, progressGridOut, progressDCDC, progressDCOut]);

  useEffect(() => {
    if (!!err?.content) {
      setVisible(true);
    }
  }, [err]);
  const handleClose = () => {
    setVisible(false);
    setError((prev) => {
      return { ...prev, content: '' };
    });
  };

  // 轮询函数
  const fetchAlarm = async () => {
    const res = await httpPost('/api/history/alarm/list', {
      pageNum: 1,
      pageSize: 1,
      alarmType: 1,
      alarmLevel: '1',
    });
    if (res?.data?.list?.[0]) {
      setGlobalAlarm((prev) => {
        if (prev?.id === res?.data?.list?.[0]?.id) {
          return prev;
        }
        return { ...res?.data?.list?.[0] };
      });
    }
  };
  // 启动轮询
  const startPolling = () => {
    // 先清除已有定时器，避免重复创建
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    const fetchDataOnce = async () => {
      try {
        await fetchAlarm();
      } catch (error) {}
    };

    if (!!username) {
      fetchDataOnce();
      // 启动定时轮询
      intervalRef.current = setInterval(fetchDataOnce, 10 * 1000);
    }
  };

  // 停止轮询
  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null; // 重置定时器ID
    }
  };
  useEffect(() => {
    if (globalAlarm) {
      setError((prev: any) => ({
        ...prev,
        content: `设备${globalAlarm?.deviceId}告警：${globalAlarm.alarmContent}`,
      }));
    }
  }, [globalAlarm]);
  useEffect(() => {
    startPolling();
    // 停止轮询
    return () => {
      stopPolling();
    };
  }, [username]);

  return (
    <GlobalErrorContext.Provider
      value={{ err, setError, alarm, setAlarm, progressAll, setProgressAll }}
    >
      {children}
      <AlarmModal
        visible={visible && !!err?.content}
        content={err?.content}
        onClose={handleClose}
        onConfirm={handleClose}
      />
    </GlobalErrorContext.Provider>
  );
};
