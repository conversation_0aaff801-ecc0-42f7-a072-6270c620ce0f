/*滚动条样式*/
* {
    // 火狐浏览器
    /* 可将滚动条设置为窄样式 */
    scrollbar-color: #e0e5eb !important;
    scrollbar-width: 8px;
}

::-webkit-scrollbar {
    /*滚动条整体样式*/
    /*高宽分别对应横竖滚动条的尺寸*/
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 8px;
    background: #e0e5eb;
}

::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    border-radius: 0;
}

// 滚动条样式
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

html,
body,
#root {
    background: #031c5a;
    color: #fff;
    height: 800px;
    width: 1280px;
}

main {
    height: 700px !important;
    overflow-y: hidden;
}

.white {
    color: #fff !important;
}

.ant-layout-content {
    color: #fff !important;
}

.ant-card {
    background: transparent;
}

.ant-menu {
    .ant-menu-title-content {
        color: #6797D6 !important;
    }
}



.cst {
    .ant-form-item-label {
        color: #3C7DB6 !important;

        label {
            color: #3C7DB6 !important;
            font-size: 16px;
        }
    }

    .ant-tabs-tab {
        min-width: 114px !important;
        font-size: 20px !important;
        color: #6797D6 !important;
        padding: 8px 0 !important;
        justify-content: center;

        &::after {
            content: '';
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
            background-color: #909599;
        }

        &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
                color: #3BE354 !important;
            }

            &::after {
                background-color: #00FF51;
            }
        }
    }

    .ant-tabs-ink-bar {
        background: #3BE354 !important;
    }

    .ant-tabs-nav::before {
        border-bottom-color: #2255B0 !important;
    }

    // ant-design
    .ant-select-selector {
        // background-color: transparent !important;
        // border: 1px solid #83A6D1 !important;
        border-radius: 0 !important;
        border: 1px solid rgba(0, 243, 255, 0.3) !important;
        background: linear-gradient(174deg, rgba(0, 135, 255, 0.13) 25.25%, rgba(0, 135, 255, 0.26) 93.59%) !important;

        .ant-select-selection-search-input,
        .ant-select-selection-item {
            color: #fff !important;
        }
    }

    .ant-picker,
    .rh-search-input {
        border: 1px solid rgba(0, 243, 255, 0.3) !important;
        background: linear-gradient(174deg, rgba(0, 135, 255, 0.13) 25.25%, rgba(0, 135, 255, 0.26) 93.59%) !important;
        border-radius: 0 !important;

        .ant-input::placeholder {
            color: #A7BEEA !important;
        }
    }

    // 日期选择器样式
    .ant-picker {
        background-color: transparent;
        border-color: #1d4884;

        input {
            color: #fff;

            &::placeholder {
                color: #A7BEEA;
            }
        }

        .ant-picker-suffix {
            color: #A7BEEA;
        }

        .ant-picker-clear {
            color: #A7BEEA;
        }

        &:hover {
            .ant-picker-suffix {
                color: #A7BEEA !important;
            }
        }
    }

    // 日期选择器弹出层按钮样式
    .ant-picker-dropdown {
        .ant-picker-ok {
            .ant-btn {
                color: #fff;
            }
        }
    }

    .ant-btn-primary {
        color: #fff !important;
        border-radius: 0;
        height: 32px;
        width: 64px;
    }

    // Select 样式
    .ant-select {
        .ant-select-selection-placeholder {
            color: #A7BEEA !important;
        }
    }

    .ant-table {
        background-color: transparent;

        .ant-table-cell::before {
            display: none !important;
        }
    }

    .ant-table-thead>tr>th {
        background-color: #0a2664;
        color: #fff;
        border-bottom: 1px solid #1d4884;

        .ant-table-column-sorter-up,
        .ant-table-column-sorter-down,
        .anticon-filter {
            color: #a8bfea;
        }
    }

    .ant-table-tbody>tr {
        &:nth-child(odd)>td {
            background-color: #0a2664;
        }

        >td {
            background-color: transparent;
            border-bottom: 1px solid #1d4884;
            color: #fff;
        }
    }

    .ant-table-tbody>tr:hover>td {
        background-color: #0a2e5c !important;
    }

    .ant-table-empty {
        .ant-table-placeholder {
            background: transparent !important;

            .ant-table-cell {
                background: transparent !important;
                border-bottom: none !important;
            }

            .ant-empty-description {
                color: #fff !important;
            }
        }
    }

    .ant-table {

        // 表格内容单元格
        .ant-table-tbody {
            .ant-table-cell {
                color: #E3F8FF !important;
            }
        }
    }



    // 分页组件样式
    .ant-pagination {
        .ant-pagination-item-ellipsis {
            color: #E3F8FF !important;
        }

        .ant-pagination-prev,
        .ant-pagination-next,
        .ant-pagination-item {
            background: transparent;
            border-color: #1d4884;


            a {
                color: #fff;
            }

            &:hover {
                border-color: #1677ff;

                a {
                    color: #1677ff;
                }
            }
        }

        .ant-pagination-item-active {
            background: transparent;
            border-color: #1677ff;

            a {
                color: #1677ff;
            }
        }

        .ant-pagination-options {
            .ant-select-selector {
                background: transparent !important;
                border-color: #1d4884 !important;
            }

            .ant-select-selection-item {
                color: #fff;
            }
        }

        .ant-pagination-item a,
        .ant-pagination-prev .ant-pagination-item-link,
        .ant-pagination-next .ant-pagination-item-link,
        .ant-pagination-jump-prev .ant-pagination-item-link,
        .ant-pagination-jump-next .ant-pagination-item-link,
        .ant-pagination-options .ant-select-selection-item,
        .ant-pagination-options-quick-jumper input,
        .ant-pagination-options-quick-jumper {
            color: #E3F8FF !important;
        }
    }
}

.custom-keyboard {
    touch-action: manipulation; // 优化触摸行为
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

    .hg-button {
        color: #000000 !important;
        cursor: pointer;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;

        &:active {
            background: rgba(0, 0, 0, 0.1);
        }
    }

    .hg-candidate-box {
        .hg-candidate-box-list-item {
            color: #000000 !important;
        }
    }
}

.ant-select-arrow {
    color: #fff !important;
}

/* .ant-btn-primary {
    border-color: #2797FF !important;
    color: #2797FF !important;
    border-radius: 0;
    height: 32px;
    width: 64px;
} */

.ant-picker {
    input {
        text-align: center;
    }
}

.ant-input-clear-icon {
    color: #A7BEEA !important;
}

.cst-btn {
    background: linear-gradient(to bottom, rgba(21, 94, 216, 0.6), rgba(23, 63, 129, 0)) !important;

    &:hover {
        background: linear-gradient(to bottom, rgba(21, 94, 216, 0.6), rgba(23, 63, 129, 0)) !important;
    }
}