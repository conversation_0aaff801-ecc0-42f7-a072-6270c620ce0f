/*滚动条样式*/
* {
  // 火狐浏览器
  /* 可将滚动条设置为窄样式 */
  scrollbar-color: #3D5DAC !important;
  scrollbar-width: 8px;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  /*高宽分别对应横竖滚动条的尺寸*/
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 8px;
  background: #3D5DAC;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: #3D5DAC62;
  border-radius: 8px;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

html,
body,
#root {
  background: #031c5a;
  color: #fff;
  height: 800px;
  width: 1280px;
}

main {
  height: 700px !important;
  overflow-y: hidden;
}

.white {
  color: #fff !important;
}

.ant-layout-content {
  color: #fff !important;
}

.ant-card {
  background: transparent;
}

.ant-menu {
  .ant-menu-title-content {
    color: #6797d6 !important;
  }
}

.cst {
  .ant-form-item-label {
    color: #3c7db6 !important;

    label {
      color: #3c7db6 !important;
      font-size: 16px;
    }
  }

  .ant-tabs-tab {
    min-width: 114px !important;
    font-size: 20px !important;
    color: #6797d6 !important;
    padding: 1px 0 !important;
    justify-content: center;

    &::after {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-left: 8px;
      background-color: #909599;
    }

    &.ant-tabs-tab-active {
      .ant-tabs-tab-btn {
        color: #3be354 !important;
      }

      //   &::after {
      //     background-color: #00ff51;
      //   }
    }

    &[data-status='Connect'] {
      &::after {
        background-color: #00ff51 !important;
      }
    }
  }

  .ant-tabs-ink-bar {
    background: #3be354 !important;
  }

  .ant-tabs-nav::before {
    border-bottom-color: #2255b0 !important;
  }

  // ant-design
  // .ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input
  .ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
    background: linear-gradient(174deg,
        rgba(0, 135, 255, 0.13) 25.25%,
        rgba(0, 135, 255, 0.26) 93.59%);
    border: 1px solid rgba(0, 243, 255, 0.3);
  }

  .ant-select-selector {
    // background-color: transparent !important;
    // border: 1px solid #83A6D1 !important;
    // border-radius: 0 !important;
    font-size: 18px !important;
    border: 1px solid rgba(0, 243, 255, 0.3);
    background: linear-gradient(174deg,
        rgba(0, 135, 255, 0.13) 25.25%,
        rgba(0, 135, 255, 0.26) 93.59%);

    .ant-select-selection-search-input,
    .ant-select-selection-item {
      color: #fff !important;
    }
  }

  .ant-picker,
  .rh-search-input {
    border: 1px solid rgba(0, 243, 255, 0.3) !important;
    // background: linear-gradient(
    //   174deg,
    //   rgba(0, 135, 255, 0.13) 25.25%,
    //   rgba(0, 135, 255, 0.26) 93.59%
    // ) !important;
    background: transparent;
    border-radius: 6px !important;

    .ant-input {
      font-size: 20px;
    }

    .ant-input-suffix svg {
      width: 24px;
      height: 24px;
    }

    .ant-input::placeholder {
      color: #a7beea !important;
    }
  }

  .ant-input-outlined.ant-input-status-error:not(.ant-input-disabled):hover {
    background-color: transparent !important;
  }

  .ant-input::placeholder {
    color: #a7beea !important;
  }

  .ant-input-affix-wrapper .anticon.ant-input-password-icon {
    color: #fff;
  }

  // 日期选择器样式
  .ant-picker {
    background-color: transparent;
    border-color: #1d4884;

    input {
      color: #fff;

      &::placeholder {
        color: #a7beea;
      }
    }

    .ant-picker-suffix {
      color: #a7beea;
    }

    .ant-picker-clear {
      color: #a7beea;
    }

    &:hover {
      .ant-picker-suffix {
        color: #a7beea !important;
      }
    }
  }

  // 日期选择器弹出层按钮样式
  .ant-picker-dropdown {
    .ant-picker-ok {
      .ant-btn {
        color: #fff;
      }
    }
  }

  .ant-btn-primary {
    color: #fff !important;
    // border-radius: 0;
    // height: 32px;
    width: 64px;
  }

  // Select 样式
  .ant-select {
    .ant-select-selection-placeholder {
      color: #a7beea !important;
    }
  }

  &.ant-table-wrapper .ant-table.ant-table-middle {
    font-size: 18px;

    .ant-table-tbody>tr>td {
      padding: 11px 8px;
    }
  }

  .ant-table {
    background-color: transparent;

    .ant-table-cell::before {
      display: none !important;
    }
  }

  .ant-table-thead>tr>th {
    background-color: #447BC34D;
    color: #fff;
    border-bottom: 1px solid #1d4884;
    font-weight: normal;

    .ant-table-column-sorter-up,
    .ant-table-column-sorter-down,
    .anticon-filter {
      color: #a8bfea;
    }
  }

  .ant-table-tbody>tr {
    &:nth-child(even)>td {
      background-color: #447BC31A; //#0a2664;
    }

    >td {
      background-color: transparent;
      border-bottom: 1px solid #1d4884;
      color: #fff;
    }
  }

  .ant-table-tbody>tr:hover>td {
    background-color: #0a2e5c !important;
  }

  .ant-table-empty {
    .ant-table-placeholder {
      background: transparent !important;

      .ant-table-cell {
        background: transparent !important;
        border-bottom: none !important;
      }

      .ant-empty-description {
        color: #fff !important;
      }
    }
  }

  .ant-table {

    // 表格内容单元格
    .ant-table-tbody {
      .ant-table-cell {
        color: #e3f8ff !important;
      }
    }

    .ant-table-tbody>tr {
      &:nth-child(even)>td {
        background-color: #447BC31A; //#0a2664;
      }

      >td {
        background-color: transparent;
        border-bottom: 1px solid #1d4884;
        color: #fff;
      }
    }
  }

  // 分页组件样式
  .ant-pagination {
    .ant-pagination-item-ellipsis {
      color: #e3f8ff !important;
    }

    .ant-pagination-prev,
    .ant-pagination-next,
    .ant-pagination-item {
      background: transparent;
      border-color: #1d4884;
      min-width: 32px;
      height: 32px;
      margin: 0 4px;
      line-height: 32px;

      a {
        color: #fff;
        font-size: 24px;
      }

      &:hover {
        border-color: #1677ff;

        a {
          color: #1677ff;
        }
      }

      .anticon {
        font-size: 24px;
      }


      & .ant-pagination-item-link,
      & .ant-pagination-item-link {
        font-size: 24px;
      }

      .ant-pagination-item-link {
        line-height: 32px;
      }
    }

    .ant-pagination-item-active {
      background: transparent;
      border-color: #1677ff;

      a {
        color: #1677ff;
      }
    }

    .ant-pagination-options {
      .ant-select-selector {
        background: transparent !important;
        border-color: #1d4884 !important;
      }

      .ant-select-selection-item {
        color: #fff;
      }
    }

    .ant-pagination-item a,
    .ant-pagination-prev .ant-pagination-item-link,
    .ant-pagination-next .ant-pagination-item-link,
    .ant-pagination-jump-prev .ant-pagination-item-link,
    .ant-pagination-jump-next .ant-pagination-item-link,
    .ant-pagination-options .ant-select-selection-item,
    .ant-pagination-options-quick-jumper input,
    .ant-pagination-options-quick-jumper {
      color: #e3f8ff !important;
    }
  }

  // 步骤条
  &.ant-steps {

    .ant-steps-item-finish,
    .ant-steps-item-process {
      .ant-steps-item-icon .ant-steps-icon .ant-steps-icon-dot {
        background-color: #0eda2c !important;
        background: #0eda2c;
        border-color: #0eda2c !important;
      }
    }

    .ant-steps-item-alarm .ant-steps-item-icon .ant-steps-icon .ant-steps-icon-dot {
      color: #ffca1c;
      background-color: #ffca1c !important;
      background: #ffca1c;
      border-color: #ffca1c !important;
    }

    .ant-steps-item-process {
      .ant-steps-item-icon .ant-steps-icon .ant-steps-icon-dot {
        box-shadow: 0 0 0 9px #0eda2c4d;
      }
    }

    .ant-steps-item-wait .ant-steps-item-icon .ant-steps-icon .ant-steps-icon-dot {
      background-color: #297de6 !important;
      background: #297de6;
      border-color: #297de6 !important;
    }

    .ant-steps-item-tail:after {
      background-color: #297de6 !important;
    }

    .ant-steps-item>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title {
      color: #e3f8ff;
      font-size: 18px;
    }

    .ant-steps-item-finish,
    .ant-steps-item-alarm {
      .ant-steps-item-tail:after {
        background: #0eda2c !important;
      }
    }
  }

  // 弹窗
  &.ant-modal .ant-modal-content {
    background-color: #0a2664;
    color: #fff;
    border: none;
    border-radius: 0;
    padding: 0;
    box-shadow: none;

    .ant-modal-header {
      background-color: transparent;
      // border-bottom: 1px solid #4070c8;
      padding: 24px;
      margin: 0;

      .ant-modal-title {
        font-size: 22px;
        font-weight: 400;
        color: #e3f8ff;
      }
    }

    .ant-modal-body {
      padding: 24px;
      padding-top: 0;
      font-size: 18px;
    }

    .ant-modal-close {
      height: 82px;
      top: 0;
      width: 72px;
      right: 0;

      .modal-close-icon {
        font-size: 32px;
        color: #a5cfff;
        width: 24px;
        height: 24px;
        line-height: 24px;
      }
    }
  }

  &.custom-btn {

    &.ant-btn-variant-outlined,
    &.ant-btn-variant-solid {
      color: #81dc4a;
      border: 1px solid #39ABFF;
      border-radius: 6px;
      background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);

      &:hover,
      &:active,
      &:focus-visible {
        border-color: #39ABFF;
        outline: unset;
        background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);
      }
    }
  }

  &.custom-btn.ant-btn:disabled {
    border: 1px solid #1E7ABD !important;
    opacity: 0.7;
  }

  &.custom-btn.ant-btn-color-default.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):hover {
    background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);
  }

  .ant-input-outlined,
  .ant-input {
    color: #fff;
    background: transparent !important;
  }

  .ant-radio-wrapper,
  .ant-radio-wrapper .ant-radio,
  .ant-checkbox-wrapper {
    font-size: 16px;
    color: #fff;
  }
}

.custom-keyboard {
  touch-action: manipulation; // 优化触摸行为
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  pointer-events: auto;
  background-color: #1B53B7 !important;

  /* 允许接收点击 */
  &.hg-theme-default {
    padding: 0px 20px 26px;
  }

  &.hg-candidate-box {
    background: transparent;
  }

  &.hg-theme-default .hg-row:not(:last-child) {
    margin-bottom: 16px;
  }

  &.hg-theme-default {

    .hg-row .hg-button {
      // color: #ffffff !important;
      background-color: #2391FF;
      cursor: pointer;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      border-radius: 10px;
      border: none;
      padding: 0;
      height: 48px;
      flex: 1;
      font-size: 22px;
      align-items: center;
      color: #fff;

      &:not(:last-child) {
        margin-right: 16px;
      }

      &:active {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .hg-candidate-box {
    border-bottom: none;
    background: transparent;

    .hg-candidate-box-list-item {
      color: #fff !important;
      font-size: 22px;
    }
  }
}

.ant-select-arrow {
  color: #fff !important;
}

/* .ant-btn-primary {
    border-color: #2797FF !important;
    color: #2797FF !important;
    border-radius: 0;
    height: 32px;
    width: 64px;
} */

.ant-picker {
  input {
    text-align: center;
  }
}

.ant-picker-dropdown {
  .ant-picker-panel-layout {
    font-size: 18px;

    .ant-picker-content th,
    .ant-picker-content td {
      min-width: 32px;
    }

    .ant-picker-cell .ant-picker-cell-inner {
      min-width: 32px;
      height: 32px;
      line-height: 32px;
    }

    .ant-picker-footer {
      button {
        font-size: 18px;
        height: 32px;
        padding: 0 16px;
      }
    }
  }
}

.ant-input-clear-icon {
  color: #a7beea !important;
}

.cst-btn {
  background: linear-gradient(to bottom, rgba(21, 94, 216, 0.6), rgba(23, 63, 129, 0)) !important;

  &:hover {
    background: linear-gradient(to bottom, rgba(21, 94, 216, 0.6), rgba(23, 63, 129, 0)) !important;
  }
}

// 全局自定义Radio.Group样式
.custom-input,
.custom-radio {
  width: 160px;
  text-align: center;
}

.custom-radio-group {
  .ant-radio-button-wrapper-checked {
    color: #6de875 !important;
  }

  .ant-radio-button-wrapper-checked::before {
    height: 48px;
    background: linear-gradient(360deg,
        rgba(44, 223, 232, 0.3) -5.68%,
        #1a7bdb 28.94%,
        #1a9ad9 42.96%,
        #dbff00 100%) !important;
  }

  .custom-radio {
    height: 48px;
    font-size: 22px;
    color: #7d98ce;
    border: 0px solid;
    background: linear-gradient(176.46deg,
        rgba(0, 135, 255, 0.75) -1.23%,
        rgba(0, 135, 255, 0.12) 96.69%) !important;
    margin-right: -2px;

    &:first-child::before {
      border-radius: 6px 0px 0px 6px;
    }

    &:not(:first-child)::before {
      height: 44px;
      width: calc(100% - 4px);
    }

    &:last-child::before {
      border-radius: 0px 6px 6px 0px;
      margin-right: 0 !important;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 2px;
      background: linear-gradient(180deg,
          rgba(0, 243, 255, 0.4) 0%,
          rgba(0, 169, 255, 0.4) 62.38%,
          rgba(0, 171, 255, 0.4) 77.49%,
          rgba(53, 26, 217, 0.12) 105.68%);
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      z-index: 0;
    }

    .ant-radio-button-label {
      line-height: 48px;
    }
  }
}

// PcsDetailModal样式
.pcs-detail-modal {
  .ant-modal-content {
    background-color: #001e56;
    border: 1px solid #0060be;
    border-radius: 4px;
    box-shadow: 0px 0px 10px rgba(0, 174, 255, 0.3);
  }

  .ant-modal-header {
    background-color: #001e56;
    border-bottom: 1px solid #0060be;

    .ant-modal-title {
      color: #00c8ff;
      font-size: 18px;
      font-weight: 500;
      text-shadow: 0px 0px 4px rgba(0, 200, 255, 0.4);
    }
  }

  .ant-modal-close {
    color: #c7f3ff;

    &:hover {
      color: #00c8ff;
    }
  }

  .ant-table {
    background-color: transparent;
  }

  .ant-table-thead>tr>th {
    background-color: #447BC34D; //#003580;
    color: #c7f3ff;
    border-bottom: 1px solid #0060be;
  }

  .ant-table-tbody>tr>td {
    border-bottom: 1px solid #00284d;
  }

  .ant-spin {
    color: #00c8ff;

    .ant-spin-dot-item {
      background-color: #00c8ff;
    }
  }
}

/* 提升Ant Design消息提示层级 */
.ant-message {
  z-index: 10000000 !important;
}

.ant-select-dropdown {
  background-color: #2353A7;
  color: #fff;
  padding: 0;

  .ant-select-item {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
    border-radius: 8px;

    &.ant-select-item-option-active:not(.ant-select-item-option-disabled),
    &.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
      color: #fff;
      background-color: #0478EC;
      font-weight: 400;
    }

    &:not(:first-child),
    &:not(:last-child) {
      border-radius: 0;
    }

  }
}

.ant-picker-dropdown {
  .ant-picker-range-arrow::before {
    background-color: #3F63AA;
    display: none;
  }

  .ant-picker-panel-layout,
  .ant-picker-panel-container .ant-picker-panel {
    background-color: #3F63AA;
  }

  .ant-picker-cell {
    color: rgb(90 161 249 / 75%);

  }

  .ant-picker-header button,
  .ant-picker-header {
    color: #A7BEEA;
  }

  .ant-picker-header-view>button {
    color: #A5CFFF;
  }

  // #A7BEEA

  .ant-picker-content th,
  .ant-picker-cell-in-view,
  .ant-picker-time-panel-column>li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
    color: #fff;
  }

  .ant-picker-panel-layout .ant-picker-footer {

    button {
      color: #fff;
      background-color: #0478EC;
      border: none;
    }

    .ant-picker-ranges .ant-picker-ok {
      padding-block: 12px;
    }
  }

  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-disabled),
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-disabled),
  .ant-picker-time-panel-column>li.ant-picker-time-panel-cell-selected {
    &:before {
      background-color: transparent;
    }

    .ant-picker-time-panel-cell-inner,
    .ant-picker-cell-inner {
      background-color: #fff !important;
      color: #143F8C !important
    }
  }

  .ant-picker-cell-in-view.ant-picker-cell-in-range:not(.ant-picker-cell-disabled):before,
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-disabled):before,
  :where(.css-dev-only-do-not-override-pjilya).ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-disabled):before {
    background-color: #2A5CC1;
    height: 32px;

  }
}