@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body,
#root {
  margin: 0;
  width: 100%;
  height: 1080px;
  padding: 0;
  font-family:
    'Rubik',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
html {
  touch-action: pan-x pan-y;
  overscroll-behavior: contain;
}

body {
  @apply overflow-x-auto overflow-y-auto;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

body.dark::-webkit-scrollbar {
  color: rgb(229 231 235 / 100%);
}

.ant-select-item.ant-select-item-option {
    min-height: 48px;
    line-height: 48px;
}

.ant-btn-lg,.ant-select-lg {
    font-size: 20px !important;
    height: 48px !important;
}
.ant-select-lg  .ant-select-selection-wrap,.ant-select-lg  .ant-select-selection-wrap input {
  height: 100% !important;
}
.ant-table-wrapper .ant-table-tbody .ant-table-row >.ant-table-cell-row-hover {
    background: transparent;
}
/* .ant-btn:disabled{
  opacity: 0.5;
} */