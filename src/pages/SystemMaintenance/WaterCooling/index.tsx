import React, { useState, useEffect, useMemo } from 'react';
import { Tabs } from 'antd';
import styles from '../index.module.less';
import { useDeviceStore } from '@/store/deviceStore';
import { httpPost } from '@/shared/http';
import { FixedSizeList } from 'react-window';

let timer: any = null;

export const WaterCoolingPage: React.FC = () => {
  const [deviceValues, setDeviceValues] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('1');
  const { getDevicesByType } = useDeviceStore();
  const [waterCoolingInfo, setWaterCoolingInfo] = useState<any>({});

  // 获取水冷设备列表
  const waterCoolingDevices = getDevicesByType('TemperContr');

  const deviceList = useMemo(() => {
    const list = waterCoolingDevices?.map((item) => item.id);
    setActiveTab(list[0]);
    return list;
  }, [JSON.stringify(waterCoolingDevices)]);

  // 生成标签页
  const tabItems = waterCoolingDevices.map((device) => ({
    key: device.id || device.deviceID || device.name,
    label: device.name || device.deviceID,
  }));

  // 获取设备数据
  const fetchDeviceValues = async () => {
    try {
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', [
        {
          deviceList,
          items: [],
        },
      ]);

      if (res.code === 0) {
        setDeviceValues(res.data);
        const device: any = {};
        res.data?.forEach((element: any) => {
          device[element.device.deviceID] = element.device;
        });
        setWaterCoolingInfo(device);
      }
    } catch (error) {
      console.error('获取设备数据失败:', error);
    }
  };

  useEffect(() => {
    fetchDeviceValues();
    if (timer) {
      clearInterval(timer);
    }
    timer = setInterval(fetchDeviceValues, 5000);
    return () => {
      clearInterval(timer);
      timer = null;
    };
  }, []);

  // 获取当前设备的数据
  const currentDeviceData = deviceValues.find((item) => item.device.deviceID === activeTab);

  // 生成表单项
  const formItems =
    currentDeviceData?.itemList?.map?.((item: { description: any; name: any; value: any }) => ({
      label: item.description || item.name,
      value: `${item.value}`,
    })) || [];

  // 列表项渲染器
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const leftItem = formItems[index * 2];
    const rightItem = formItems[index * 2 + 1];
    const isLastRow = index === Math.floor(formItems.length / 2);
    const customStyle = isLastRow ? { borderBottom: 0 } : {};

    return (
      <div className={styles.gridItem} style={{ ...style, display: 'flex', gap: '20px' }}>
        {leftItem && (
          <div className={styles.itemWrapper} style={{ ...customStyle }}>
            <div className={styles.label}>{leftItem.label}</div>
            <div className={styles.value} style={{ ...customStyle }}>
              {leftItem.value}
            </div>
          </div>
        )}
        {rightItem && (
          <div className={styles.itemWrapper} style={{ ...customStyle }}>
            <div className={styles.label}>{rightItem.label}</div>
            <div className={styles.value} style={{ ...customStyle }}>
              {rightItem.value}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={styles.content}>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        className="cst"
        // waterCoolingInfo
        renderTabBar={(props, DefaultTabBar) => {
          return (
            <DefaultTabBar {...props}>
              {(node: any) => {
                let device:any=null;
                if(waterCoolingDevices && Array.isArray(waterCoolingDevices)){
                    device = waterCoolingDevices?.find(
                        (d: any) => (d.id || d.deviceID || d.name) === node.key,
                    );
                }
                if (device) {
                  return React.cloneElement(node, {
                    'data-status':
                      waterCoolingInfo[device?.id || device?.deviceID || device?.name]?.status,
                  });
                } else {
                  return node;
                }
              }}
            </DefaultTabBar>
          );
        }}
      />
      <div className={styles.gridContainer}>
        <FixedSizeList
          height={600}
          width="100%"
          itemCount={Math.floor(formItems.length / 2)}
          itemSize={50}
        >
          {Row}
        </FixedSizeList>
      </div>
    </div>
  );
};
