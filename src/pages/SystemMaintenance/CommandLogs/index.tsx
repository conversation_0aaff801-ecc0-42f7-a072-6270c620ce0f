import React, { useState, useEffect, useRef } from 'react';
import { Button, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import styles from '../index.module.less';
import clsx from 'clsx';
import { httpPost } from '@/shared/http';
import { formatDate } from '@/utils/formatDate';
import RhDatePicker from '@/components/RhDatePicker';
import RhSearchInput from '@/components/RhSearchInput';
import { generateUUID } from '@/utils/uuid';
import 'react-simple-keyboard/build/css/index.css';
import { RhKeyboard } from '@/components/RhKeyboard';
import { RhButton } from '@/components/ui/pagbtn';

interface CommandLogItem {
    id: string;
    deviceName: string;
    value: string;
    result: string;
    resultMsg: string;
    userName: string;
    createTime: string;
}

export const CommandLogs: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<CommandLogItem[]>([]);
    const [total, setTotal] = useState(0);
    const [showKeyboard, setShowKeyboard] = useState(false);
    const keyboardRef = useRef<any>(null);
    // 1. 添加一个状态来控制日期选择器的值
    const [dateValue, setDateValue] = useState<any>(null);
    const [queryParams, setQueryParams] = useState<any>({
        startTime: null,
        endTime: null,
        keyword: '',
        pageIndex: 1,
        pageSize: 10
    });

    const fetchCommandLogs = async (params?: any) => {
        const body = params || queryParams;
        setLoading(true);
        try {
            const res = await httpPost('/api/history/log/commandlog/list', body);
            if (res.code === 0) {
                // 为每条数据添加 uuid
                const listWithUuid = (res.data?.list || []).map((item: any) => ({
                    ...item,
                    uuid: generateUUID()
                }));
                setData(listWithUuid);
                setTotal(res.data?.total || 0);
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchCommandLogs();
    }, []);

    const columns: ColumnsType<CommandLogItem> = [
        {
            title: '时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 200,
            render: (time) => formatDate(new Date(time).getTime())
        },
        {
            title: '服务模块',
            dataIndex: 'souceName',
            key: 'souceName',
            width: 120,
            ellipsis: true,
        },
        {
            title: '功能模块',
            dataIndex: 'modelName',
            key: 'modelName',
            width: 120,
            ellipsis: true,
        },
        {
            title: '设备',
            dataIndex: 'deviceCode',
            key: 'deviceCode',
            width: 150,
            ellipsis: true,
        },
        {
            title: '属性',
            dataIndex: 'addressName',
            key: 'addressName',
            width: 180,
            ellipsis: true,
        },
        {
            title: '值',
            dataIndex: 'value',
            key: 'value',
            width: 100,
        },
        {
            title: '结果',
            dataIndex: 'result',
            key: 'result',
            width: 100,
            render: (text) => text === 'True' ? <span style={{ color: '#52c41a' }}>成功</span> : <span style={{ color: '#fe4545' }}>失败</span>
        },
        {
            title: '结果描述',
            dataIndex: 'resultMsg',
            key: 'resultMsg',
            width: 180,
            ellipsis: true,
        },
        {
            title: 'IP',
            dataIndex: 'ipAddress',
            key: 'ipAddress',
            width: 120,
        },
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            width: 200,
            ellipsis: true,
        }
    ];

    const handleSearch = () => {
        setQueryParams((prev: any) => ({ ...prev, pageIndex: 1 }));
        fetchCommandLogs();
    };

    // 2. 修改重置函数
    const handleReset = () => {
        const p = {
            startTime: '',
            endTime: '',
            keyword: '',
            pageIndex: 1,
            pageSize: 10
        }
        setDateValue(null); // 重置日期选择器的值
        setQueryParams(p);
        fetchCommandLogs(p);
        keyboardRef.current?.setInput?.("");
    };

    const handleDateChange = (dates: any) => {
        setQueryParams((prev: any) => ({
            ...prev,
            startTime: formatDate(dates?.[0]?.valueOf()) || '',
            endTime: formatDate(dates?.[1]?.valueOf()) || ''
        }));
    };

    const handleKeywordChange = (value: string) => {
        setQueryParams((prev: any) => ({ ...prev, keyword: value }));
        if (!value) {
            keyboardRef.current?.setInput?.("");
        }
    };

    const handleTableChange = (pagination: any) => {
        const params = {
            ...queryParams,
            pageIndex: pagination.current,
            pageSize: pagination.pageSize
        };
        setQueryParams(params);
        fetchCommandLogs(params);
    };

    const handleInputFocus = () => {
        setShowKeyboard(true);
    };

    const handleKeyboardInput = (input: string) => {
        setQueryParams((prev: any) => ({ ...prev, keyword: input }));
    };


    // 3. 修改日期选择器组件
    return (
        <div className={styles.container}>
            <div className={clsx('cst flex flex-start gap-3 mb-5', styles.filterBar)}>
                <RhDatePicker
                    value={dateValue}  // 使用受控值
                    allowClear={true}
                    onChange={(dates: any) => {
                        setDateValue(dates);  // 更新日期选择器的值
                        handleDateChange(dates);
                    }}
                />
                <RhSearchInput
                    value={queryParams.keyword}
                    placeholder='请输入日志ID、设备、属性模糊搜索'
                    onChange={handleKeywordChange}
                    onFocus={handleInputFocus}
                    style={{ width: "320px" }}
                />
                <RhKeyboard
                    init={(r: any) => (keyboardRef.current = r)}
                    show={showKeyboard}
                    onClose={() => setShowKeyboard(false)}
                    onChange={handleKeyboardInput}
                    layoutType={'chinese'}
                />
                <RhButton type="primary" onClick={handleSearch} style={{ width: 100 }}>查询</RhButton>
                <RhButton type="primary" ghost onClick={handleReset} style={{ width: 100 }}>重置</RhButton>
            </div>
            <Table
                loading={loading}
                columns={columns}
                dataSource={data}
                rowKey="uuid"
                className='cst'
                size='middle'
                pagination={{
                    total,
                    current: queryParams.pageIndex,
                    pageSize: queryParams.pageSize,
                    showSizeChanger: false,
                    showQuickJumper: false
                }}
                onChange={handleTableChange}
                scroll={{ x: 1000 }}
            />
        </div>
    );
};