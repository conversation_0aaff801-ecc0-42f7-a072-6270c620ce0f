// 根据当前设备获取对应的设备ID（PCS1对应BMS1，PCS2对应BMS2等）
export const getCorrespondingDeviceId = (deviceId: string): string | null => {
  const pcsMatch = deviceId.match(/PCS(\d+)/);
  const bmsMatch = deviceId.match(/BMS(\d+)/);
  if (pcsMatch) {
    const num = pcsMatch[1];
    const correspondingBmsId = `BMS${num}`;
    return correspondingBmsId;
  }
  if (bmsMatch) {
    const num = bmsMatch[1];
    const correspondingPcsId = `PCS${num}`;
    return correspondingPcsId;
  }
  return null;
};
