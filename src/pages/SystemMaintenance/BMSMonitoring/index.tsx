import React, { useState, useEffect, useMemo } from 'react';
import { Form, Select, Tabs } from 'antd';
import styles from '../index.module.less';
import { useDeviceStore } from '@/store/deviceStore';
import { httpPost } from '@/shared/http';
import { FixedSizeList } from 'react-window';

const { Option } = Select;

let timer: any = null;

export const BMSMonitoring: React.FC = () => {
    const [deviceValues, setDeviceValues] = useState<any[]>([]);
    const [activeTab, setActiveTab] = useState('BMS1');
    const { getDevicesByType } = useDeviceStore();
    const [selectedBat, setSelectedBat] = useState<string>('');

    // 获取BMS设备列表
    const bmsDevices = getDevicesByType('BMS');

    const deviceList = useMemo(() => {
        const list = bmsDevices?.map(item => item.id);
        setActiveTab(list[0]);
        return list
    }, [JSON.stringify(bmsDevices)])

    // 生成标签页
    const tabItems = bmsDevices.map((device) => ({
        key: device.id || device.deviceID || device.name,
        label: device.name || device.deviceID
    }));

    // 获取设备数据
    const fetchDeviceValues = async () => {
        try {
            const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', [{
                deviceList,
                items: []
            }]);

            if (res.code === 0) {
                setDeviceValues(res.data);
            }
        } catch (error) {
            console.error('获取设备数据失败:', error);
        }
    };

    useEffect(() => {
        if (activeTab) {
            fetchDeviceValues();
            if (timer) {
                clearInterval(timer);
            }
            timer = setInterval(fetchDeviceValues, 5000);
            return () => {
                clearInterval(timer);
                timer = null;
            }
        }
    }, [activeTab]);

    // 获取当前设备的数据
    const currentDeviceData = deviceValues.find(
        item => item.device.deviceID === activeTab
    );

    // 生成表单项
    /*     const formItems = currentDeviceData?.itemList?.map?.((item: { description: any; name: any; value: any; }) => ({
            label: item.description || item.name,
            value: `${item.value}`
        })) || []; */


    // 从设备数据中提取电池组选项
    const batOptions = useMemo(() => {
        const batSet = new Set<string>();
        deviceValues.forEach(device => {
            device.itemList?.forEach((item: { name: string }) => {
                const match = item.name.match(/Bat(\d+)/);
                if (match) {
                    batSet.add(match[0].replace?.('Bat', '电池簇#'));
                }
            });
        });
        return Array.from(batSet).sort();
    }, [deviceValues]);

    // 根据选择的电池组过滤数据
    const filteredFormItems = useMemo(() => {
        if (!selectedBat) {
            return currentDeviceData?.itemList?.map?.((item: { description: any; name: any; value: any; }) => ({
                label: item.description || item.name,
                value: `${item.value}`
            })) || [];
        }
        return currentDeviceData?.itemList?.filter((item: { name: string }) =>
            item.name?.includes?.(selectedBat)
        ).map((item: { description: any; name: any; value: any; }) => ({
            label: item.description || item.name,
            value: `${item.value}`
        })) || [];
    }, [currentDeviceData, selectedBat]);

    // 列表项渲染器
    const Row = ({ index, style }: { index: number, style: React.CSSProperties }) => {
        const leftItem = filteredFormItems[index * 2];
        const rightItem = filteredFormItems[index * 2 + 1];
        const isLastRow = index === Math.floor(filteredFormItems.length / 2);
        const customStyle = isLastRow ? { borderBottom: 0 } : {};

        return (
            <div className={styles.gridItem} style={{ ...style, display: 'flex', gap: '20px' }}>
                {leftItem && (
                    <div className={styles.itemWrapper} style={{ ...customStyle }}>
                        <div className={styles.label}>{leftItem.label}</div>
                        <div className={styles.value} style={{ ...customStyle }}>{leftItem.value}</div>
                    </div>
                )}
                {rightItem && (
                    <div className={styles.itemWrapper} style={{ ...customStyle }}>
                        <div className={styles.label}>{rightItem.label}</div>
                        <div className={styles.value} style={{ ...customStyle }}>{rightItem.value}</div>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="flex flex-col" style={{ maxHeight: 'calc(100% - 100px)' }}>
            <Form layout="horizontal">
                <Form.Item label="电池族" className='cst'>
                    <Select
                        value={selectedBat}
                        onChange={setSelectedBat}
                        style={{ width: 200 }}
                    >
                        <Option value="">全部</Option>
                        {batOptions.map(bat => (
                            <Option key={bat} value={bat.replace?.('电池簇#', 'Bat')}>{bat}</Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>

            <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                items={tabItems}
                className='cst'
            />

            <div className={`${styles.gridContainer} flex-1`}>
                <FixedSizeList
                    height={600}
                    width="100%"
                    itemCount={Math.floor(filteredFormItems.length / 2)}
                    itemSize={34}
                    style={{ paddingBottom: '60px' }}
                >
                    {Row}
                </FixedSizeList>
            </div>
        </div>
    );
};