import React, { useState, useEffect, useMemo } from 'react';
import { Form, Radio, Select, Tabs } from 'antd';
import styles from '../index.module.less';
import { useDeviceStore } from '@/store/deviceStore';
import { httpGet, httpPost } from '@/shared/http';
import { FixedSizeList } from 'react-window';
import { useContentDictionary } from '@/pages/DeviceStatus/hooks/useContentDictionary';
import { DeviceType } from '@/pages/DeviceStatus/types';
import RhPagination from '@/components/RhPagination';

const { Option } = Select;

let timer: any = null;

// 配置文件名 BMSAllDataView.cfg
export const BMSMonitoring: React.FC = () => {
  const [deviceValues, setDeviceValues] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('BMS1');
  const { getDevicesByType } = useDeviceStore();
  const [selectedBat, setSelectedBat] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // 每页10条数据
  const [bmsDeviceInfo, setBmsDeviceInfo] = useState<any>({});
  const [batGroup, setBatGroup] = useState<any>([]);
  const [batConfig, setBatConfig] = useState<any>({});

  // 获取BMS设备列表
  const bmsDevices = getDevicesByType('BMS');
  const { getContentValue } = useContentDictionary();

  // 电池组数据
  const getBMSConfig = async () => {
    const config = await httpGet('/api/configs/get?fileName=BMSAllDataView.cfg');
    if (config.DataView.MultiType === 'Tab') {
      // const batConfig: any = {};
      // const batGroup = config.DataView.Multi?.map((item: any) => {
      //   batConfig[item.Label] = item.Items;
      //   return {
      //     value: item.Label,
      //     label: item.Label,
      //   };
      // });
      // setBatConfig(batConfig);
      // setBatGroup(batGroup);
    }
  };

  const deviceList = useMemo(() => {
    const list = bmsDevices?.map((item) => item.id);
    setActiveTab(list[0]);
    return list;
  }, [JSON.stringify(bmsDevices)]);

  // 生成标签页
  const tabItems = bmsDevices.map((device) => ({
    key: device.id || device.deviceID || device.name,
    label: device.name || device.deviceID,
  }));

  // 获取设备数据
  const fetchDeviceValues = async () => {
    try {
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', [
        {
          deviceList: ['BMS1'],
          items: [],
        },
      ]);

      if (res.code === 0) {
        const batConfig: any = {};
        const batGroup = [
          {
            value: '1#电池支路',
            label: '1#电池支路',
          },
        ];

        batConfig['1#电池支路'] = res.data
          .find((d: any) => d.device.deviceID == 'BMS1')
          .itemList?.sort((s1: any, s2: any) => s1.description - s2.description)
          ?.map((item: any) => {
            return {
              ...item,
              Descript: item.description,
              Name: item.name,
              // ViewType:
            };
          });
        setBatConfig(batConfig);
        setBatGroup(batGroup);
        console.log('batConfig', batConfig, batGroup);

        setDeviceValues(res.data);
        const device: any = {};
        res.data?.forEach((element: any) => {
          device[element.device.deviceID] = element.device;
        });
        setBmsDeviceInfo(device);
        console.log('device', device);
      }
    } catch (error) {
      console.error('获取设备数据失败:', error);
    }
  };
  useEffect(() => {
    getBMSConfig();
  }, []);

  useEffect(() => {
    if (activeTab) {
      fetchDeviceValues();

      if (timer) {
        clearInterval(timer);
      }
      timer = setInterval(fetchDeviceValues, 5000);
      return () => {
        clearInterval(timer);
        timer = null;
      };
    }
  }, [activeTab]);

  // 获取当前设备的数据
  const currentDeviceData = deviceValues.find((item) => item.device.deviceID === activeTab);

  // 生成表单项
  /*     const formItems = currentDeviceData?.itemList?.map?.((item: { description: any; name: any; value: any; }) => ({
            label: item.description || item.name,
            value: `${item.value}`
        })) || []; */

  // 电池组数据改从配置文件中获取
  // 从设备数据中提取电池组选项
  /* const batOptions = useMemo(() => {
    const batSet = new Set<string>();
    deviceValues.forEach((device) => {
      device.itemList?.forEach((item: { name: string }) => {
        const match = item.name.match(/Bat(\d+)/);
        if (match) {
          batSet.add(`${match[0].replace?.('Bat', '')}#电池支路`);
        }
      });
    });
    const res = Array.from(batSet)
      .sort()
      .map((item) => ({
        label: item,
        value: 'Bat' + item.split('#')[0],
      }));
    return res;
  }, [deviceValues]); */

  useEffect(() => {
    if (!selectedBat) {
      setSelectedBat(batGroup?.[0]?.value || '');
    }
  }, [batGroup]);

  const handleChangeTab = (key: string) => {
    setActiveTab(key);
    setCurrentPage(1);
  };

  // 根据选择的电池组过滤数据
  const filteredFormItems = useMemo(() => {
    if (!selectedBat || !batConfig || Object.keys(batConfig).length === 0) {
      return [];
    }
    const batItems = batConfig?.[selectedBat];
    return (
      batItems?.map((item: any) => {
        const itemData = currentDeviceData?.itemList?.find((i: any) => i.name === item.Name);
        let value = itemData?.value;
        if (item.ViewType === 'Content') {
          value = getContentValue(DeviceType.BMS, item.Name, itemData?.value);
        }
        return {
          label: item.Descript || item.Name,
          value: value ? `${value || ''} ${item.Unit || ''}` : '',
        };
      }) || []
    );
    // if (!selectedBat) {
    //   return (
    //     currentDeviceData?.itemList?.map?.((item: { description: any; name: any; value: any }) => ({
    //       label: item.description || item.name,
    //       value: `${item.value}`,
    //     })) || []
    //   );
    // }
    // return currentDeviceData?.itemList
    //   ?.filter((item: { name: string }) => item.name?.includes?.(selectedBat))
    //   .map((item: { description: any; name: any; value: any }) => ({
    //     label: item.description || item.name,
    //     value: `${item.value}`,
    //   })) || []
  }, [currentDeviceData, batConfig, selectedBat]);
  // 根据当前数据类型生成表单项并排序
  const sortedFormItems = useMemo(() => {
    return filteredFormItems
      ? [...filteredFormItems].sort((a, b) => {
          // 按标签名排序，确保固定顺序
          return (a.label || '').localeCompare(b.label || '');
        })
      : [];
  }, [filteredFormItems]);

  // 计算分页数据
  const totalPages = Math.ceil(sortedFormItems.length / (pageSize * 2)); // 每行2个item，所以乘以2
  const paginatedItems = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize * 2;
    const endIndex = startIndex + pageSize * 2;
    return sortedFormItems.slice(startIndex, endIndex);
  }, [sortedFormItems, currentPage, pageSize]);
  // 翻页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };
  // 列表项渲染器
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const leftItem = paginatedItems[index * 2];
    const rightItem = paginatedItems[index * 2 + 1];
    const isLastRow = index === Math.floor(paginatedItems.length / 2);
    const customStyle = isLastRow ? { borderBottom: 0 } : {};

    return (
      <div className={`${styles.gridItem} cst`} style={{ ...style, display: 'flex' }}>
        {leftItem && (
          <div className={styles.itemWrapper} style={{ ...customStyle }}>
            <div className={styles.label}>{leftItem.label}</div>
            <div className={styles.value} style={{ ...customStyle }}>
              {leftItem.value}
            </div>
          </div>
        )}
        {rightItem && (
          <div className={styles.itemWrapper} style={{ ...customStyle }}>
            <div className={styles.label}>{rightItem.label}</div>
            <div className={styles.value} style={{ ...customStyle }}>
              {rightItem.value}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col" style={{ maxHeight: 'calc(100% - 100px)' }}>
      {/* <Form layout="horizontal">
          <Form.Item label="电池族" className="cst">
            <Select value={selectedBat} onChange={setSelectedBat} style={{ width: 200 }}>
              <Option value="">全部</Option>
              {batOptions.map((bat) => (
                <Option key={bat} value={bat.replace?.('电池簇#', 'Bat')}>
                  {bat}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form> */}

      <Tabs
        activeKey={activeTab}
        onChange={handleChangeTab}
        items={tabItems}
        className="cst"
        renderTabBar={(props, DefaultTabBar) => {
          return (
            <DefaultTabBar {...props}>
              {(node: any) => {
                const device = bmsDevices.find((d) => (d.id || d.deviceID || d.name) === node.key);
                if (device) {
                  return React.cloneElement(node, {
                    'data-status':
                      bmsDeviceInfo[device?.id || device?.deviceID || device?.name]?.status,
                  });
                } else {
                  return node;
                }
              }}
            </DefaultTabBar>
          );
        }}
      />
      <div className="relative flex">
        <Radio.Group
          // 切换电池组重置第一页
          onChange={(e) => {
            setSelectedBat(e.target.value);
            setCurrentPage(1);
          }}
          defaultValue={batGroup?.[0]?.value || ''}
          value={selectedBat}
          className={`custom-radio-group flex w-full justify-center`}
        >
          {(batGroup || [])?.map((bat: any) => (
            <Radio.Button key={bat.value} value={bat.value} className={'custom-radio'}>
              {bat.label}
            </Radio.Button>
          ))}
        </Radio.Group>
      </div>
      <div className={`${styles.gridContainer} flex-1`}>
        <FixedSizeList
          height={502}
          width="100%"
          itemCount={Math.min(10, Math.ceil(paginatedItems.length / 2))} // 当前页实际数据行数
          itemSize={50}
        >
          {Row}
        </FixedSizeList>
      </div>
      <RhPagination
        total={filteredFormItems.length}
        current={currentPage}
        pageSize={pageSize * 2}
        // totalPages={totalPages}
        onPageChange={handlePageChange} //handlePageChange
      />
    </div>
  );
};
