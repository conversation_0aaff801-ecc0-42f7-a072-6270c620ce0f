import React, { useState, useEffect, useMemo } from 'react';
import { Form, Select, Tabs, Card } from 'antd';
import styles from '../index.module.less';
import { useDeviceStore } from '@/store/deviceStore';
import { httpPost } from '@/shared/http';
import { FixedSizeList } from 'react-window';

const { Option } = Select;

let timer: any = null;

export const PCSMonitoring: React.FC = () => {
    const [deviceValues, setDeviceValues] = useState<any[]>([]);
    const [activeTab, setActiveTab] = useState('1');
    const { getDevicesByType } = useDeviceStore();

    // 获取PCS设备列表
    const pcsDevices = getDevicesByType('PCS');

    const deviceList = useMemo(() => {
        const list = pcsDevices?.map(item => item.id);
        setActiveTab(list[0]);
        return list
    }, [JSON.stringify(pcsDevices)])

    // 生成标签页
    const tabItems = pcsDevices.map((device) => ({
        key: device.id || device.deviceID || device.name,
        label: device.name || device.deviceID
        // label: `${index + 1}#PCS`
    }));

    // 获取设备数据
    const fetchDeviceValues = async () => {
        try {
            const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', [{
                deviceList,
                items: []
            }]);

            if (res.code === 0) {
                // console.log('PCS', res.data)
                setDeviceValues(res.data);
            }
        } catch (error) {
            console.error('获取设备数据失败:', error);
        }
    };

    useEffect(() => {
        if (activeTab) {
            fetchDeviceValues();
            // 定时刷新数据
            if (timer) {
                clearInterval(timer);
            }
            timer = setInterval(fetchDeviceValues, 5000);
            return () => {
                clearInterval(timer);
                timer = null;
            }
        }
    }, [activeTab]);

    // 获取当前设备的数据
    const currentDeviceData = deviceValues.find(
        item => item.device.deviceID === activeTab
    );

    // 生成表单项
    const formItems = currentDeviceData?.itemList?.map?.((item: { description: any; name: any; value: any; }) => ({
        label: item.description || item.name,
        value: `${item.value}`
    })) || [];

    // 列表项渲染器
    const Row = ({ index, style }: { index: number, style: React.CSSProperties }) => {
        // 获取当前行的两个项目
        const leftItem = formItems[index * 2];
        const rightItem = formItems[index * 2 + 1];
        // style={index !== formItems.length - 1 ? { borderBottom: 0 } : {}}
        const isLastRow = index === Math.floor(formItems.length / 2);
        const customStyle = isLastRow ? { borderBottom: 0 } : {};
        return (
            <div className={styles.gridItem} style={{ ...style, display: 'flex', gap: '20px' }}>
                {leftItem && (
                    <div className={styles.itemWrapper} style={{ ...customStyle }}>
                        <div className={styles.label}>{leftItem.label}</div>
                        <div className={styles.value} style={{ ...customStyle }}>{leftItem.value}</div>
                    </div>
                )}
                {rightItem && (
                    <div className={styles.itemWrapper} style={{ ...customStyle }}>
                        <div className={styles.label}>{rightItem.label}</div>
                        <div className={styles.value} style={{ ...customStyle }}>{rightItem.value}</div>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="flex flex-col" style={{ maxHeight: 'calc(100vh - 100px)' }}>
            {/*  <Form layout="horizontal">
                <Form.Item label="数据分组" className='cst'>
                    <Select defaultValue="" style={{ width: 200 }}>
                        <Option value="">请选择</Option>
                        <Option value="group1">实时数据</Option>
                        <Option value="group2">报警数据</Option>
                        <Option value="group2">读写数据</Option>
                    </Select>
                </Form.Item>
            </Form> */}

            <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                items={tabItems}
                className='cst'
            />

            <div className={`${styles.gridContainer} flex-1`}>
                <FixedSizeList
                    height={600} // 可视区域高度
                    width="100%"
                    itemCount={Math.floor(formItems.length / 2)}
                    itemSize={34} // 每项高度
                >
                    {Row}
                </FixedSizeList>
            </div>
        </div>
    );
};