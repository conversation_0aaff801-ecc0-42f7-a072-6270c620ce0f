import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Select, Tabs, Radio, Button, Input, Modal, message } from 'antd';
import styles from '../index.module.less';
import modalStyles from './index.module.less';
import { useDeviceStore } from '@/store/deviceStore';
import { httpGet, httpPost } from '@/shared/http';
import { FixedSizeList } from 'react-window';
import { RhKeyboard } from '@/components/RhKeyboard';
import RhNumberKeyboard from '@/components/RhNumberKeyboard/RhNumberKeyboard';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import { generateUUID } from '@/utils/uuid';
import { RhButton } from '@/components/ui/pagbtn';

let timer: any = null;

const dataType = [
  { value: 'read', label: '只读' },
  { value: 'alarm', label: '告警' },
  { value: 'writeRead', label: '读写' },
];

export const PCSMonitoring: React.FC = () => {
  const [deviceValues, setDeviceValues] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('1');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // 每页10条数据
  const { getDevicesByType } = useDeviceStore();
  const [type, setType] = useState(dataType[0].value);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [writeValuePassword, setWriteValuePassword] = useState('');
  const [writeData, setWriteData] = useState<any[]>([]);
  const [alarmData, setAlarmData] = useState<any[]>([]);
  // 获取PCS设备列表
  const pcsDevices = getDevicesByType('PCS');
  const keyboardRef = useRef<any>(null);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [showNumberKeyboard, setShowNumberKeyboard] = useState(false);
  const [isWrite, setIsWrite] = useState(false);
  const [currentEdit, setCurrentEdit] = useState<any>({});
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [pcsDeviceInfo, setPcsDeviceInfo] = useState<any>({});

  const deviceList = useMemo(() => {
    const list = pcsDevices?.map((item: { id: any }) => item.id);
    setActiveTab(list[0]);
    return list;
  }, [JSON.stringify(pcsDevices)]);

  // 生成标签页
  const tabItems = pcsDevices.map((device) => ({
    key: device.id || device.deviceID || device.name,
    label: device.name || device.deviceID,
    // label: `${index + 1}#PCS`
  }));
  //   获取PCS分类数据
  const getData = async () => {
    const config = await httpGet('/api/configs/get?fileName=PCSViewDetail.cfg');
    setWriteData(config?.Write || []);
    setAlarmData(config?.Alarm || []);
  };
  // 获取设备数据
  const fetchDeviceValues = async () => {
    try {
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', [
        {
          deviceList,
          items: [],
        },
      ]);

      if (res.code === 0) {
        // console.log('PCS', res.data)
        setDeviceValues(res.data);
        const device: any = {};
        res.data?.forEach((element: any) => {
          device[element.device.deviceID] = element.device;
        });
        setPcsDeviceInfo(device);
      }
    } catch (error) {
      console.error('获取设备数据失败:', error);
    }
  };

  useEffect(() => {
    if (activeTab) {
      setIsWrite(false);
      setCurrentPage(1); // 切换设备时重置到第一页
      fetchDeviceValues();
      getData();
      // 只在非读写模式下启动定时器
      if (type !== 'writeRead') {
        if (timer) {
          clearInterval(timer);
        }
        timer = setInterval(fetchDeviceValues, 5000);
      }
      return () => {
        clearInterval(timer);
        timer = null;
      };
    }
  }, [activeTab, type]);

  // 获取当前设备的数据
  const currentDeviceData = deviceValues.find((item) => item.device.deviceID === activeTab);
  // 分别获取读写 告警数据的name集合
  const writeDataNames = writeData?.map?.((item: { Name: any }) => item.Name);
  const alarmDataNames = alarmData?.map?.((item: { Name: any }) => item.Name);
  //   区分只读 告警 读写 数据
  const alarmDatas = currentDeviceData?.itemList?.filter(
    (item: { description: any; name: any; value: any }) => alarmDataNames?.includes(item.name),
  );
  const writeDatas = currentDeviceData?.itemList?.filter(
    (item: { description: any; name: any; value: any }) => writeDataNames?.includes(item.name),
  );
  // 不在读写和告警中的其他数据为只读数据
  const readDatas = currentDeviceData?.itemList?.filter(
    (item: { description: any; name: any; value: any }) =>
      !writeDataNames?.includes(item.name) && !alarmDataNames?.includes(item.name),
  );

  // 生成表单项 currentDeviceData
  const formItems = (data: any) => {
    return (
      data?.map?.((item: { description: any; name: any; value: any }) => ({
        label: item.description || item.name,
        value: `${item.value}`,
        name: item.name,
      })) || []
    );
  };

  // 根据当前数据类型生成表单项并排序
  const sortedFormItems = useMemo(() => {
    if (type === 'writeRead') {
      return [...formItems(writeDatas)].sort((a, b) => {
        return (a.label || '').localeCompare(b.label || '');
      });
    }
    if (type === 'alarm') {
      return [...formItems(alarmDatas)]?.sort((a, b) => {
        return (a.label || '').localeCompare(b.label || '');
      });
    }
    if (type === 'read') {
      return [...formItems(readDatas)]?.sort((a, b) => {
        return (a.label || '').localeCompare(b.label || '');
      });
    }
    return [];
    // return [...formItems(data)].sort((a, b) => {
    //   // 按标签名排序，确保固定顺序
    //   return (a.label || '').localeCompare(b.label || '');
    // });
  }, [formItems, type]);

  // 计算分页数据
  const totalPages = Math.ceil(sortedFormItems.length / (pageSize * 2)); // 每行2个item，所以乘以2
  const paginatedItems = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize * 2;
    const endIndex = startIndex + pageSize * 2;
    return sortedFormItems.slice(startIndex, endIndex);
  }, [sortedFormItems, currentPage, pageSize]);

  // 翻页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  // 列表项渲染器
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    // 获取当前行的两个项目
    const leftItem = paginatedItems[index * 2];
    const rightItem = paginatedItems[index * 2 + 1];
    const isLastRow = index === Math.min(9, Math.floor(paginatedItems.length / 2)); // 最后一行
    const customStyle = isLastRow ? { borderBottom: 0 } : {};
    return (
      <div className={`${styles.gridItem} cst`} style={{ ...style, display: 'flex' }}>
        {leftItem && (
          <div className={styles.itemWrapper} style={{ ...customStyle }}>
            <div className={styles.label}>{leftItem.label}</div>
            {type === 'writeRead' && isWrite ? (
              <Input
                value={currentEdit.name === leftItem.name ? currentEdit.value : leftItem.value}
                variant="borderless"
                className={styles.value}
                style={{ ...customStyle }}
                disabled={type !== 'writeRead' && isWrite}
                onFocus={() => {
                  handleInputFocusWithValue({ ...leftItem, index });
                }}
              />
            ) : (
              <div className={styles.value} style={{ ...customStyle }}>
                {leftItem.value}
              </div>
            )}
          </div>
        )}
        {rightItem && (
          <div className={styles.itemWrapper} style={{ ...customStyle }}>
            <div className={styles.label}>{rightItem.label}</div>
            {type === 'writeRead' && isWrite ? (
              <Input
                value={currentEdit.name === rightItem.name ? currentEdit.value : rightItem.value}
                variant="borderless"
                style={{
                  width: 'auto',
                  color: '#fff',
                  fontSize: '16px',
                  backgroundColor: '#0C1A3C',
                }}
                disabled={type !== 'writeRead' && isWrite}
                onFocus={() => {
                  handleInputFocusWithValue({ ...rightItem, index });
                }}
              />
            ) : (
              <div className={styles.value} style={{ ...customStyle }}>
                {rightItem.value}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  const handleTypeChange = (value: string) => {
    setType(value);
    // 切换类型回到第一页
    handlePageChange(1);
    // 切换为读写模式时暂停轮询，切换为其他模式时恢复轮询
    if (value === 'writeRead') {
      if (timer) {
        clearInterval(timer);
        timer = null;
      }
    } else {
      // 恢复轮询
      if (timer) {
        clearInterval(timer);
      }
      timer = setInterval(fetchDeviceValues, 5000);
    }
  };

  const handleWriteStatus = () => {
    setIsModalOpen(true);
  };

  const handleSubmit = () => {
    if (writeValuePassword === 'test') {
      setIsModalOpen(false);
      setIsWrite(true);
      setShowKeyboard(false);
      setWriteValuePassword('');
    } else {
      message.error('口令错误');
    }
  };

  const handleInputFocus = () => {
    setShowKeyboard(true);
  };
  const handleKeyboardInput = (input: string) => {
    setWriteValuePassword(input);
  };
  const handleInputFocusWithValue = (params: {
    label: any;
    name: string;
    value: string | number;
    index: number;
  }) => {
    setShowNumberKeyboard(false);
    setTimeout(() => {
      setShowNumberKeyboard(true);
      setCurrentEdit(params);
    }, 0);
  };
  const handleCurrentEdit = (input: string) => {
    setCurrentEdit((prev: any) => ({ ...prev, value: input }));
  };
  const handleCancel = () => {
    // 取消时关闭键盘并恢复输入框值
    // 清空当前编辑中的数据重置值
    setCurrentEdit({});
    setShowNumberKeyboard(false);
    setConfirmLoading(false);
    setConfirmVisible(false);
  };
  const handleConfirm = async () => {
    setConfirmLoading(true);
    try {
      const params = {
        devices: [
          {
            msgId: generateUUID(), // 消息ID
            deviceCode: activeTab, // 设备ID
            addresses: [
              {
                name: currentEdit.name, // 点位名称
                value: currentEdit.value, //点位值，输入为准，可以是字符串，数字，浮点型
              },
            ],
          },
        ],
      };
      const res = await httpPost('/api/RootInterface/WriteCommand', params);
      if (res.code === 0 && res.data.devices[0].addresses[0].isSuc) {
        message.success(`${currentEdit.label}写值成功`);
      } else {
        message.error(`${currentEdit.label}写值失败`);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setShowNumberKeyboard(false);
      setConfirmLoading(false);
      setConfirmVisible(false);
    }
  };

  const handleChangeTab = (key: string) => {
    setActiveTab(key);
    setCurrentPage(1);
  };
  return (
    <div className="flex flex-col" style={{ maxHeight: 'calc(100vh - 100px)' }}>
      <Tabs
        activeKey={activeTab}
        onChange={handleChangeTab}
        items={tabItems}
        className="cst"
        renderTabBar={(props, DefaultTabBar) => {
          return (
            <DefaultTabBar {...props}>
              {(node: any) => {
                const device = pcsDevices.find((d) => (d.id || d.deviceID || d.name) === node.key);
                if (device) {
                  return React.cloneElement(node, {
                    'data-status':
                      pcsDeviceInfo[device?.id || device?.deviceID || device?.name]?.status,
                  });
                } else {
                  return node;
                }
              }}
            </DefaultTabBar>
          );
        }}
      />

      <div className="relative flex">
        <Radio.Group
          onChange={(e) => handleTypeChange(e.target.value)}
          defaultValue={dataType?.[0]?.value || 'read'}
          value={type}
          className={`custom-radio-group flex w-full justify-center`}
        >
          {(dataType || [])?.map((item) => (
            <Radio.Button key={item?.value} value={item?.value} className={'custom-radio'}>
              {item?.label}
            </Radio.Button>
          ))}
        </Radio.Group>
        {type === 'writeRead' && !isWrite && (
          <div className="absolute right-0">
            <RhButton onClick={() => handleWriteStatus()} text={'开始写值'} />
          </div>
        )}
      </div>
      <div
        className={`${styles.gridContainer} flex-1 ${type === 'writeRead' && isWrite ? 'bg-[#0C1A3C]' : 'bg-[rgba(25,45,100)]'}`}
      >
        <FixedSizeList
          height={502} // 可视区域高度
          width="100%"
          itemCount={Math.min(10, Math.ceil(paginatedItems.length / 2))} // 当前页实际数据行数
          itemSize={50} // 每项高度
        >
          {Row}
        </FixedSizeList>
      </div>
      <div className="mt-4 flex w-full items-center justify-end gap-2">
        <span className="text-[20px] text-[#76AAD9]">共{sortedFormItems.length}条数据</span>
        <span className="text-[20px] text-[#76AAD9]">
          {currentPage}/{totalPages}页
        </span>
        <RhButton onClick={() => handlePageChange(1)} disabled={currentPage === 1} text={'首页'} />
        <RhButton
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          text={'上一页'}
        />
        <RhButton
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          text={'下一页'}
        />
        <RhButton
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
          text={'最后一页'}
        />
      </div>
      <Modal
        title="写值口令"
        open={isModalOpen}
        onCancel={() => {
          setIsModalOpen(false);
          setShowKeyboard(false);
        }}
        footer={[
          <Button
            className="h-[44px] rounded-[6px]"
            ghost
            key="cancel"
            onClick={() => {
              setIsModalOpen(false);
              setShowKeyboard(false);
            }}
          >
            取消
          </Button>,
          <Button
            className="h-[44px] rounded-[6px]"
            key="submit"
            type="primary"
            onClick={handleSubmit}
          >
            确定
          </Button>,
        ]}
        width={400}
        centered
        className={modalStyles['write-value-modal']}
      >
        <div className={modalStyles['modal-content']}>
          <Input
            placeholder="请输入写值口令"
            className={modalStyles['write-value-input']}
            value={writeValuePassword}
            onFocus={handleInputFocus}
          />
        </div>
      </Modal>
      <RhKeyboard
        init={(r: any) => (keyboardRef.current = r)}
        show={showKeyboard}
        onClose={() => setShowKeyboard(false)}
        onChange={handleKeyboardInput}
        layoutType={'chinese'}
      />
      <RhNumberKeyboard
        position={currentEdit.index % 10 < 3 ? 'bottom' : 'top'}
        visible={showNumberKeyboard}
        onChange={handleCurrentEdit}
        onKeyPress={(button) => {
          if (button === '{enter}') {
            setShowNumberKeyboard(false);
            setConfirmVisible(true);
          }
        }}
        onClose={() => setShowNumberKeyboard(false)}
      />
      {/* 操作确认 */}
      <ConfirmModal
        open={confirmVisible}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        confirmLoading={confirmLoading}
        content={`是否确认设置${currentEdit?.label}为 ${currentEdit?.value}？`}
      />
    </div>
  );
};
