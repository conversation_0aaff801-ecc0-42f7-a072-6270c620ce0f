.write-value-modal {
  :global {
    .ant-modal-content {
      background: #143F8C;
      border-radius: 8px;
      box-shadow: 0px 2px 8px 0px rgba(33, 37, 46, 0.15);
    }

    .ant-modal-header {
      background: #143F8C;
      border-bottom: none;
      padding: 16px 0px 0;
      margin-bottom: 0;
    }

    .ant-modal-title {
      color: #E3F8FF;
      font-family: 'PingFang SC';
      font-size: 22px;
      font-weight: 400;
      line-height: 20px;
    }

    .ant-modal-close {
      color: #A5CFFF;
      top: 24px;
      right: 24px;
    }

    .ant-modal-body {
      padding: 32px 0px;
    }

    .ant-modal-footer {
      background: #143F8C;
      border-top: none;
      padding: 0 ;
      display: flex;
      justify-content: space-between;
      gap: 12px;
      button{
        width: 50%;
      }
    }

    .ant-btn {
      border-radius: 6px;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-weight: 400;
      line-height: 20px;
      padding: 12px 16px;
      min-width: 80px;
    }

    .ant-btn-default {
      border: 1px solid #A5CFFF;
      color: #A5CFFF;
    }

    .ant-btn-primary {
      background: #0478EC;
      border: 1px solid #0478EC;
      color: #FFFFFF;
    }
  }
}

.modal-content {
  .write-value-input {
    width: 100%;
    height: 48px;
    background: transparent;
    border: 1px solid #39ABFF;
    border-radius: 6px;
    color: #FFFFFF;
    font-size: 20px;
    font-weight: 400;
    line-height: 20px;

    &::placeholder {
      color: #76AAD9;
    }

    &:focus {
      border-color: #39ABFF;
      box-shadow: 0 0 0 2px rgba(57, 171, 255, 0.2);
    }
  }
}