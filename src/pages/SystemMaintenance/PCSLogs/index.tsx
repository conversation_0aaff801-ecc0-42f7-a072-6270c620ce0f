import { Button, InputNumber, message, Modal, Radio } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import { httpPost } from '@/shared/http';
import { useDeviceStore } from '@/store/deviceStore';
import { generateUUID } from '@/utils/uuid';
import storage from '@/shared/storage';
import { usePrompt } from '@/hooks/prompt/usePrompt';
import Keyboard from 'react-simple-keyboard';
import { asyncDownloadFile } from '@/utils/download';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import { RhButton } from '@/components/ui/pagbtn';

interface pscItem {
  value: string;
  label: string;
}
const BmsDeviceFromPCS = {
  PCS1: 'BMS1',
  PCS2: 'BMS2',
};
const min = 20;
const max = 1000;
export const PCSLogs = () => {
  const { getDevicesByType } = useDeviceStore();
  const [data, setData] = useState<any[]>([]);
  const [pcsList, setPcsList] = useState<pscItem[]>([]);
  // 读取状态
  const [readStatus, setReadStatus] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<any>({
    keyword: '',
    pageIndex: 1,
    limit: 20,
  });
  const [inputValue, setInputValue] = useState(searchParams.limit);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);
  const [error, setError] = useState<string>('');
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const columns: ColumnsType<any> = [
    {
      title: '序号',
      dataIndex: 'SerialNumber',
      key: 'SerialNumber',
    },
    {
      title: '日志ID',
      dataIndex: 'LogID',
      key: 'LogID',
    },
    {
      title: '日志信息',
      dataIndex: 'Data',
      key: 'Data',
    },
    {
      title: '事件状态',
      dataIndex: 'Eventstate',
      key: 'Eventstate',
    },
    {
      title: '工作状态',
      dataIndex: 'State',
      key: 'State',
    },
    {
      title: '时间',
      dataIndex: 'Timestamp',
      key: 'Timestamp',
      width: 200,
      // render: (time) => formatDate(time),
    },
  ];
  const pcsDevices = getDevicesByType('PCS');
  // 拦截提示
  usePrompt(() => {
    if (readStatus) {
      message.warning('正在读取数据，禁止操作');
      return true;
    }
    return false;
  }, readStatus);

  // 设备启停操作
  const handleDevice = (deviceIDs: string[], opt: 0 | 1) => {
    const params = {
      deviceIDs: deviceIDs,
      operationType: opt,
    };
    if (deviceIDs?.filter((item) => !!item)?.length === 0) {
      message.error('未获取到设备信息');
      return;
    }
    return httpPost('/api/RootInterface/EMSDeviceStartStop', params);
  };
  const handleLogs = async (opt: 'start' | 'stop', id: string, limit = searchParams.limit) => {
    if (!id || !limit) return;

    if (opt === 'start') {
      try {
        setLoading(true);
        setReadStatus(true);
        const res = await httpPost(`/api/task/start/${id}/${limit}`,{},{timeout:360000});
        return res;
      } catch (error) {
        setReadStatus(false);
      } finally {
        setLoading(false);
      }
    } else {
      return httpPost(`/api/task/stop/${id}`).then(() => {
        setReadStatus(false);
      });
    }
    return [];
  };
  useEffect(() => {
    const sessionData = storage.get('pcs-logs') || [];
    setData(sessionData?.[searchParams.pcs]);
  }, [searchParams.pcs]);
  const handleConfirm = async (_params?: any) => {
    const readLog = async () => {
      const params = _params ? { ...searchParams, ..._params } : searchParams;
      let resStatus = '';
      setConfirmLoading(true);

      try {
        // 停止当前设备采集后读取
        // 确保 params.pcs 是 BmsDeviceFromPCS 的有效键
        const pcsKey = params.pcs as keyof typeof BmsDeviceFromPCS;
        resStatus = await handleDevice([params.pcs, BmsDeviceFromPCS[pcsKey]], 0);
        const res: any = (await handleLogs('start', params.pcs)) || [];
        // 读取日志前端保存一份
        // 为每条数据添加 uuid
        if (Array.isArray(res)) {
          const listWithUuid = (res || []).map((item: any) => ({
            ...item,
            uuid: generateUUID(),
          }));
          storage.set('pcs-logs', { [params.pcs]: listWithUuid });
          setData(listWithUuid);
          setReadStatus(false);
          setLoading(false);
        } else if (res?.error) {
          message.error(res?.error);
        }
      } catch (error) {
      } finally {
        // 不论成功或者失败最后恢复采集
        setReadStatus(false);
        // 停止成功才需要恢复
        if (resStatus === 'successful') {
          const pcsKey = params.pcs as keyof typeof BmsDeviceFromPCS;
          await handleDevice([params.pcs, BmsDeviceFromPCS[pcsKey]], 1);
        }
        setConfirmLoading(false);
        setConfirmVisible(false);
      }
    };

    // Modal.confirm({
    //   title: '读取日志确认',
    //   content: '确认要读取日志吗？',
    //   okText: '确认',
    //   cancelText: '取消',
    //   onOk: async () => {
    await readLog();
    //   },
    // });
  };
  const searchData = () => {
    if (error) return;
    setConfirmVisible(true);
  };
  const handleChange = (e: any) => {
    const currentValue = e.target.value;
    setData([]);
    setSearchParams({ ...searchParams, pcs: currentValue });
  };
  const handleRestore = async () => {
    // 停止读取并恢复采集
    try {
      const pcsKey = searchParams.pcs as keyof typeof BmsDeviceFromPCS;
      await handleLogs('stop', searchParams.pcs);
      await handleDevice([searchParams.pcs, BmsDeviceFromPCS[pcsKey]], 1);
      message.success('恢复成功');
    } catch (error) {}
  };
  const HandleClearingHistory = async () => {
    try {
      const params = {
        devices: [
          {
            msgId: generateUUID(), // 消息ID
            deviceCode: searchParams.pcs, // 设备ID
            addresses: [
              {
                name: 'Clr_Hist_Rec', // 点位名称
                value: 1, //点位值，输入为准，可以是字符串，数字，浮点型
              },
            ],
          },
        ],
      };
      const res = await httpPost('/api/RootInterface/WriteCommand', params);
      if (res.message === 'Success' && res?.data?.devices?.[0]?.addresses?.[0]?.isSuc) {
        message.success('清除成功');
      }
    } catch (error) {}
  };

  const handleExport = async () => {
    try {
      await asyncDownloadFile('/api/file/download/pcslog', {}, { defaultFilename: 'PCSLog.xlsx' });
    } catch (error) {
      message.error('导出失败');
    }
  };

  const handleStop = async () => {
    // 手动停止读取后恢复采集
    try {
      message.loading('停止读取中...');
      await handleLogs('stop', searchParams.pcs);
      const pcsKey = searchParams.pcs as keyof typeof BmsDeviceFromPCS;
      await handleDevice([searchParams.pcs, BmsDeviceFromPCS[pcsKey]], 0);
      message.success('已停止');
    } catch (error) {}
  };

  useEffect(() => {
    // 获取PCS设备列表
    setPcsList(pcsDevices?.map((item) => ({ value: item.id, label: item.name })));
    setSearchParams({ pcs: pcsDevices?.[0]?.id });
  }, [JSON.stringify(pcsDevices)]);

  const handleLimitChange = useCallback((value: string) => {
    const numValue = Number(value);
    if (isNaN(numValue)) {
      setError('请输入有效的数字');
      return;
    }
    if (numValue < min) {
      setError(`最小值不能小于 ${min}`);
      setInputValue(numValue);
      keyboardRef.current.setInput(numValue.toString());
      return;
    }
    if (numValue > max) {
      setError(`最大值不能超过 ${max}`);
      setInputValue(max);
      keyboardRef.current.setInput(max.toString());

      return;
    }
    if (numValue <= 0) {
      setInputValue(numValue);
      keyboardRef.current.setInput(numValue.toString());
      setError('读取条数必须大于0');
      return;
    }
    setInputValue(numValue);
    keyboardRef.current.setInput(numValue.toString());

    setError('');
  }, []);

  useEffect(() => {
    setSearchParams((prev: any) => ({ ...prev, limit: inputValue }));
  }, [inputValue]);

  return (
    <div className="gap-5">
      <div>
        <Radio.Group
          onChange={handleChange}
          defaultValue={pcsList?.[0]?.value || ''}
          value={searchParams.pcs}
          className={`${styles['custom-radio-group']} flex w-full justify-center`}
          disabled={readStatus}
        >
          {(pcsList || [])?.map((item) => (
            <Radio.Button key={item?.value} value={item?.value} className={styles['custom-radio']}>
              {item?.label}
            </Radio.Button>
          ))}
        </Radio.Group>
        <div className="mb-5 flex justify-between">
          <div className="relative flex gap-4">
            <div className="relative flex items-center gap-4 text-base">
              {/* 设置读取条数 */}
              <span>设置读取条数</span>
              <InputNumber
                min={1}
                defaultValue={20}
                value={inputValue}
                size={'large'}
                max={1000}
                className={styles['custom-input']}
                onChange={(value) => setSearchParams((prev: any) => ({ ...prev, limit: value }))}
                onFocus={() => setShowKeyboard(true)}
              />
            </div>
            {error && (
              <div className="z-1 absolute bottom-[-15px] left-0 text-sm text-red-500">{error}</div>
            )}

            <Button
              size={'large'}
              onClick={searchData}
              className={styles['custom-btn']}
              disabled={readStatus}
            >
              读取
            </Button>
            <RhButton
              // size={'large'}
              onClick={handleStop}
              // className={styles['custom-btn']}
              disabled={!readStatus}
              style={{ width: 'auto' }}
            >
              停止
            </RhButton>
            <Button
              size={'large'}
              onClick={handleExport}
              className={styles['custom-btn']}
              disabled={readStatus}
            >
              导出
            </Button>
            <Button
              size={'large'}
              onClick={HandleClearingHistory}
              className={styles['custom-btn']}
              disabled={readStatus}
            >
              清除历史
            </Button>
          </div>

          <Button
            size={'large'}
            onClick={handleRestore}
            className={styles['custom-btn']}
            disabled={readStatus}
          >
            恢复
          </Button>
        </div>
      </div>
      {showKeyboard && (
        <div
          className="fixed z-[9999] flex flex-col items-center justify-start"
          onTouchStart={(e) => e.stopPropagation()}
          onTouchMove={(e) => e.preventDefault()}
        >
          {/* <div className="fixed bottom-0 left-0 z-[9999] w-[600px] rounded-[10px] bg-[#1B53B7] shadow-[0_4px_12px_rgba(0,0,0,0.25)]"> */}
          <div className="w-[600px] rounded-[10px] bg-[#1B53B7] shadow-[0_4px_12px_rgba(0,0,0,0.25)]">
            <div className="flex justify-end p-2">
              <Button
                type="text"
                size="large"
                // icon={<CloseOutlined />}
                onClick={() => setShowKeyboard(false)}
                style={{ color: '#fff', fontSize: '22px' }}
              >
                取消
              </Button>
            </div>

            <Keyboard
              keyboardRef={(r) => (keyboardRef.current = r)}
              input={inputValue}
              onChange={handleLimitChange}
              onKeyPress={(button) => {
                if (button === '{bksp}') {
                  const strValue = inputValue?.toString() || '';
                  const newVal = strValue.slice(0, -1);
                  handleLimitChange(newVal);
                }
              }}
              // useTouchEvents={true} // 启用触摸事件支持
              useMouseEvents={true} //useTouchEvents
              theme="hg-theme-default custom-keyboard"
              layout={{
                default: ['1 2 3', '4 5 6', '7 8 9', '. 0 {bksp}', '{enter}'],
              }}
              display={{
                '{bksp}': '删除',
                '{enter}': '确认',
              }}
            />
          </div>
        </div>
      )}
      <Table
        rowKey="uuid"
        size="middle"
        columns={columns}
        dataSource={data}
        pagination={false}
        className="cst"
        loading={loading}
        scroll={{ y: 450 }}
      />
      {/* 操作确认弹窗 */}
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => {
          setConfirmVisible(false);
        }}
        onConfirm={handleConfirm}
        confirmLoading={confirmLoading}
        content={
          <div className="leading-7">
            确认要执行读取操作吗?
            <br />
            当前设备将停止采集进入调试状态
          </div>
        }
      />
    </div>
  );
};
