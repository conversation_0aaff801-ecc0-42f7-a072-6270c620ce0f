.custom-input,
.custom-input:focus,
.custom-input:focus-within,
.custom-btn,
.custom-btn:hover {
  border-radius: 6px;
  border-width: 1px;
  border: 1px solid #39abff;
  background: linear-gradient(
    180deg,
    rgba(0, 135, 255, 0.65) -3.41%,
    rgba(0, 135, 255, 0.08) 96.68%
  ) !important;

  :global {
    input {
      color: #fff !important;
    }
    .ant-input-number-input-wrap .ant-input-number-input{
      height: 48px;
    }
    .ant-input-number-handler-wrap{
      display: none;
    }
  }
}
.custom-input{
  height: 48px;
}
.custom-input,
.custom-radio {
  width: 160px;
  text-align: center;
}

.custom-btn {
  color: #6de875 !important;

  &:disabled {
    cursor: not-allowed;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.25);
    background: rgba(0, 0, 0, 0.04);
    box-shadow: none;
    opacity: 0.5;
  }
}

.custom-radio-group {
  margin-bottom: 20px;

  :global {
    .ant-radio-button-wrapper-checked {
      color: #6de875 !important;
    }

    .ant-radio-button-wrapper-checked::before {
      height: 48px;
      background: linear-gradient(
        360deg,
        rgba(44, 223, 232, 0.3) -5.68%,
        #1a7bdb 28.94%,
        #1a9ad9 42.96%,
        #dbff00 100%
      ) !important;
    }
  }

  .custom-radio {
    height: 48px;
    font-size: 22px;
    color: #7d98ce;
    border: 0px solid;
    background: linear-gradient(
      176.46deg,
      rgba(0, 135, 255, 0.75) -1.23%,
      rgba(0, 135, 255, 0.12) 96.69%
    ) !important;
    margin-right: -2px;

    &:first-child::before {
      border-radius: 6px 0px 0px 6px;
    }
    &:not(:first-child)::before {
      height: 44px;
      width: calc(100% - 4px);
    }
    &:last-child::before {
      border-radius: 0px 6px 6px 0px;
      margin-right: 0 !important;
    }
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 2px;
      background: linear-gradient(
        180deg,
        rgba(0, 243, 255, 0.4) 0%,
        rgba(0, 169, 255, 0.4) 62.38%,
        rgba(0, 171, 255, 0.4) 77.49%,
        rgba(53, 26, 217, 0.12) 105.68%
      );
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      z-index: 0;
    }
    :global {
      .ant-radio-button-label {
        line-height: 48px;
      }
    }
  }
}
