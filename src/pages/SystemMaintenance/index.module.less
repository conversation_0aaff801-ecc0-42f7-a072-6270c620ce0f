.formContainer {
    :global {
        .ant-form-item {
            margin-bottom: 0;
            border-bottom: 1px solid #e8e8e8;

            &:last-child {
                border-bottom: none;
            }
        }
    }
}

.gridContainer {
    display: flex;
    flex-wrap: wrap;
    // background-color: rgba(25, 45, 100);
    // width: 1000px;
    margin-top: 12px;
}


.formItem {
    display: flex;
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.02);

    :global {
        .ant-form-item-label {
            width: 180px;
        }

        .ant-form-item-control {
            color: #1890ff;
        }
    }
}

.gridItem {
    // padding: 12px 24px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    background-color: transparent;
    border: 1px solid #084888;
    height: 36px;
    font-size: 18px;
}

.itemWrapper {
    display: flex;
    align-items: center;
    flex: 1;
    height: calc(100% - 2px);
}

.label {
    // font-size: 16px;
    width: 300px;
    height: 100%;
    color: #76AAD9;
    padding-left: 16px;
    padding-right: 16px;
    background-color: #447BC31A;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block; // 改为 block 布局
    line-height: 50px;
    text-align: right;
}

.value {
    flex: 1;
    // font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 172px;
    height: 100%;
    background-color: transparent;
    padding-left: 12px;
    color: #fff;
}

.sider {
    position: relative;
    background: transparent;
    padding-top: 8px;
    margin-left: 16px;
    margin-top: 12px;
    margin-bottom: 16px;
    padding: 10px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('@/assets/sidebar_bg_new.png') no-repeat;
        background-size: 100% 100%;
        opacity: 0.6;
        pointer-events: none;
    }

    :global {
        .ant-menu {
            background: transparent;

            .ant-menu-submenu-title:active{
                background-color: transparent;
            }
            .ant-menu-submenu-title {
                position: relative;
                color: #5A85D8;
                display: flex;
                // padding: 21px 44px;
                justify-content: center;
                gap: 8px;
                color: #5A85D8;
                height: 60px;
                text-align: center;
                font-size: 20px;
                font-style: normal;
                font-weight: 600;
                border-radius: 0;
                margin: 0;
                // background: linear-gradient(90deg, rgba(1, 75, 165, 0.33) 0%, #002367 101.7%);
                .ant-menu-title-content {
                    text-align: left;
                }
                &::after {
                    position: absolute;
                    right: 10px;
                    content: '';
                    width: 16px;
                    height: 16px;
                    background: url('@/assets/expand.png') no-repeat center;
                    background-size: contain;
                }
            }

            // /.ant-menu-submenu-selected,
            .ant-menu-submenu-open {
                >.ant-menu-submenu-title {
                    color: #3BE354;
                    // background: linear-gradient(90deg, rgba(1, 75, 165, 0.33) 0%, #002367 101.7%);

                    &::after {
                        background-image: url('@/assets/retract.png');
                    }
                }
            }

            .ant-menu-item {
                color: #6797D6;
                margin: 0;
                height: 60px;
                line-height: 60px;
                border-radius: 0;
                font-size: 20px;
                font-style: normal;
                font-weight: 600;
                margin-top: 1px;
                // background: linear-gradient(90deg, rgba(13, 100, 171, 0.90) 0%, rgba(0, 35, 103, 0.90) 100%);

                &:hover {
                    color: #fff !important;
                    background: transparent;
                }

                &.ant-menu-item-selected {
                    .ant-menu-title-content {
                        color: #fff !important;
                    }
                    background: #0478EC;
                    border-radius: 6px;

                    // background: linear-gradient(90deg, rgba(13, 100, 171, 0.90) 0%, rgba(0, 35, 103, 0.90) 100%);
                    // background: rgba(59, 227, 84, 0.1);

                    &::after {
                        display: none;
                    }
                }
            }

            .ant-menu-submenu-arrow {
                display: none;
            }
        }
    }
}

.content {
    :global {
        .title {
            font-size: 24px;
        }
    }
}