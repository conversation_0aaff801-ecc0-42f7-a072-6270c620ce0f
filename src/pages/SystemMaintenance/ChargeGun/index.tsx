import React, { useState, useEffect, useMemo } from 'react';
import styles from '../index.module.less';
import { useDeviceStore } from '@/store/deviceStore';
import { httpGet, httpPost } from '@/shared/http';
import { FixedSizeList } from 'react-window';
import { projectType } from '@/constants';
import { Form, Select, Tabs } from 'antd';
const { Option } = Select;

let timer: any = null;

const getType = () => {
  if (projectType === 'hangzhou') {
    return 'ChargeGun';
  }
  return 'DCChargingStation';
};

// 添加枪型选项
const gunOptions = ['A', 'B'];

export const ChargeGunPage: React.FC = () => {
  const [deviceValues, setDeviceValues] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('1');
  const [selectedGun, setSelectedGun] = useState<string>('');
  const { getDevicesByType } = useDeviceStore();
  const [chargeGunConfig, setChargeGunConfig] = useState<any>({});
  const [gunGroup, setGunGroup] = useState<any>([]);
  // 获取充电桩设备列表
  const chargingPileDevices = getDevicesByType(getType());

  const deviceList = useMemo(() => {
    const list = chargingPileDevices?.map((item) => item.id);
    setActiveTab(list[0]);
    return list;
  }, [JSON.stringify(chargingPileDevices)]);

  const getChargeGunConfig = async () => {
    const config = await httpGet('/api/configs/get?fileName=ChargingStationAllDataView.cfg');
    const gunConfig: any = {};
    const guns = config?.DataView?.Multi?.map((item: any) => {
      gunConfig[item.Label] = item.Items;
      return {
        value: item.Label,
        label: item.Label,
      };
    });
    setGunGroup(guns);
    setChargeGunConfig(gunConfig);
  };
  // 生成标签页
  const tabItems = chargingPileDevices.map((device) => ({
    key: device.id || device.deviceID || device.name,
    label: device.name || device.deviceID,
  }));

  // 获取设备数据
  const fetchDeviceValues = async () => {
    try {
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', [
        {
          deviceList,
          items: [],
        },
      ]);

      if (res.code === 0) {
        setDeviceValues(res.data);
      }
    } catch (error) {
      console.error('获取设备数据失败:', error);
    }
  };

  useEffect(() => {
    getChargeGunConfig();
    if (activeTab) {
      fetchDeviceValues();
      if (timer) {
        clearInterval(timer);
      }
      // timer = setInterval(fetchDeviceValues, 5000);
      return () => {
        clearInterval(timer);
        timer = null;
      };
    }
  }, [activeTab]);

  // 获取当前设备的数据
  const currentDeviceData = deviceValues.find((item) => item.device.deviceID === activeTab);

  // 生成表单项
  // 列表项渲染器
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const leftItem = formItems[index * 2];
    const rightItem = formItems[index * 2 + 1];
    const isLastRow = index === Math.floor(formItems.length / 2);
    const customStyle = isLastRow ? { borderBottom: 0 } : {};
    return (
      <div
        className={`${styles.gridItem} cst`}
        style={{ ...style, display: 'flex', gap: '20px', border: 'none' }}
      >
        {leftItem && (
          <div
            className={`${styles.itemWrapper} border border-[#084888]`}
            style={{ ...customStyle,height:'100%'  }}
          >
            <div className={styles.label}>{leftItem.label}</div>
            <div className={styles.value} style={{ ...customStyle }}>
              {leftItem.value}
            </div>
          </div>
        )}
        {rightItem && (
          <div
            className={`${styles.itemWrapper} border border-[#084888]`}
            style={{ ...customStyle,height:'100%' }}
          >
            <div className={styles.label}>{rightItem.label}</div>
            <div className={styles.value} style={{ ...customStyle }}>
              {rightItem.value}
            </div>
          </div>
        )}
      </div>
    );
  };

  // 修改表单项生成逻辑，添加枪型过滤
  const formItems = useMemo(() => {
    const gunGroupItems = gunGroup?.map((item: any) => {
      const currentItems = chargeGunConfig?.[item.value];
      return currentItems?.map((gunPoint: any) => {
        const itemData = currentDeviceData?.itemList?.find((i: any) => i.name === item.Name);
        return {
          label: gunPoint.Descript || gunPoint.Name,
          value: itemData?.value ? `${itemData?.value || ''} ${gunPoint.Unit || ''}` : '',
        };
      });
    });
    function crossMerge(arr1: any[], arr2: any[]) {
      if (!arr1?.length && !arr2?.length) {
        return [];
      }

      const maxLength = Math.max(arr1?.length, arr2?.length);
      const merged = [];
      for (let i = 0; i < maxLength; i++) {
        // 先添加arr1的元素，若不存在则用空对象
        merged.push(arr1[i] || { label: '', value: '' });
        // 再添加arr2的元素，若不存在则用空对象
        merged.push(arr2[i] || { label: '', value: '' });
      }
      return merged;
    }
    if (gunGroupItems?.[0]?.length !== gunGroupItems?.[1]?.length) {
      //取长度更短的数组
      const shorterArray =
        gunGroupItems?.[0]?.length < gunGroupItems?.[1]?.length ? gunGroupItems?.[0] : gunGroupItems?.[1];
      const longerArray =
        gunGroupItems?.[0]?.length > gunGroupItems?.[1]?.length ? gunGroupItems?.[0] : gunGroupItems?.[1];
      while (shorterArray.length < longerArray.length) {
        shorterArray.push({ label: '', value: '' });
      }
    }
    return crossMerge(gunGroupItems?.[0], gunGroupItems?.[1]);

    // if (!selectedGun) {
    //   return (
    //     currentDeviceData?.itemList?.map?.((item: { description: any; name: any; value: any }) => ({
    //       label: item.description || item.name,
    //       value: `${item.value}`,
    //     })) || []
    //   );
    // }
    // return (
    //   currentDeviceData?.itemList
    //     ?.filter((item: { description: string }) =>
    //       item.description?.includes?.(`${selectedGun}枪`),
    //     )
    //     .map((item: { description: any; name: any; value: any }) => ({
    //       label: item.description || item.name,
    //       value: `${item.value}`,
    //     })) || []
    // );
  }, [currentDeviceData, chargeGunConfig, gunGroup]);

  return (
    <div className={styles.content}>
      {/* 添加枪型选择表单 */}
      {/* <Form layout="horizontal">
        <Form.Item label="充电枪" className="cst">
          <Select allowClear value={selectedGun} onChange={setSelectedGun} style={{ width: 200 }}>
            <Option value="" title="">
              全部
            </Option>
            {gunOptions.map((gun) => (
              <Option key={gun} value={gun} title="">
                {gun}枪
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Form> */}

      <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} className="cst" />

      <div className={`${styles.gridContainer} flex-1`}>
        <div className={`cst flex w-full items-center justify-start gap-5`}>
          {gunGroup?.map((item: any) => (
            <span key={item.value} className="w-1/2 flex-1">
              {item.label}
            </span>
          ))}
        </div>

        <FixedSizeList
          height={600}
          width="100%"
          itemCount={Math.floor(formItems.length / 2)}
          itemSize={50}
        >
          {Row}
        </FixedSizeList>
      </div>
    </div>
  );
};
