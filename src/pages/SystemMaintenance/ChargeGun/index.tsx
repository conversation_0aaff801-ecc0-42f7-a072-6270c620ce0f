import React, { useState, useEffect, useMemo } from 'react';
import styles from '../index.module.less';
import { useDeviceStore } from '@/store/deviceStore';
import { httpPost } from '@/shared/http';
import { FixedSizeList } from 'react-window';
import { projectType } from '@/constants';
import { Form, Select, Tabs } from 'antd';
const { Option } = Select;

let timer: any = null;

const getType = () => {
  if (projectType === 'hangzhou') {
    return 'ChargeGun';
  }
  return 'DCChargingStation'
}

// 添加枪型选项
const gunOptions = ['A', 'B'];

export const ChargeGunPage: React.FC = () => {
  const [deviceValues, setDeviceValues] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('1');
  const [selectedGun, setSelectedGun] = useState<string>('');
  const { getDevicesByType } = useDeviceStore();

  // 获取充电桩设备列表
  const chargingPileDevices = getDevicesByType(getType());

  const deviceList = useMemo(() => {
    const list = chargingPileDevices?.map(item => item.id);
    setActiveTab(list[0]);
    return list
  }, [JSON.stringify(chargingPileDevices)])

  // 生成标签页
  const tabItems = chargingPileDevices.map((device) => ({
    key: device.id || device.deviceID || device.name,
    label: device.name || device.deviceID
  }));

  // 获取设备数据
  const fetchDeviceValues = async () => {
    try {
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', [{
        deviceList,
        items: []
      }]);

      if (res.code === 0) {
        setDeviceValues(res.data);
      }
    } catch (error) {
      console.error('获取设备数据失败:', error);
    }
  };

  useEffect(() => {
    if (activeTab) {
      fetchDeviceValues();
      if (timer) {
        clearInterval(timer);
      }
      timer = setInterval(fetchDeviceValues, 5000);
      return () => {
        clearInterval(timer);
        timer = null;
      }
    }
  }, [activeTab]);

  // 获取当前设备的数据
  const currentDeviceData = deviceValues.find(
    item => item.device.deviceID === activeTab
  );

  // 生成表单项


  // 列表项渲染器
  const Row = ({ index, style }: { index: number, style: React.CSSProperties }) => {
    const leftItem = formItems[index * 2];
    const rightItem = formItems[index * 2 + 1];
    const isLastRow = index === Math.floor(formItems.length / 2);
    const customStyle = isLastRow ? { borderBottom: 0 } : {};

    return (
      <div className={styles.gridItem} style={{ ...style, display: 'flex', gap: '20px' }}>
        {leftItem && (
          <div className={styles.itemWrapper} style={{ ...customStyle }}>
            <div className={styles.label}>{leftItem.label}</div>
            <div className={styles.value} style={{ ...customStyle }}>{leftItem.value}</div>
          </div>
        )}
        {rightItem && (
          <div className={styles.itemWrapper} style={{ ...customStyle }}>
            <div className={styles.label}>{rightItem.label}</div>
            <div className={styles.value} style={{ ...customStyle }}>{rightItem.value}</div>
          </div>
        )}
      </div>
    );
  };


  // 修改表单项生成逻辑，添加枪型过滤
  const formItems = useMemo(() => {
    if (!selectedGun) {
      return currentDeviceData?.itemList?.map?.((item: { description: any; name: any; value: any; }) => ({
        label: item.description || item.name,
        value: `${item.value}`
      })) || [];
    }
    return currentDeviceData?.itemList?.filter((item: { description: string }) =>
      item.description?.includes?.(`${selectedGun}枪`)
    ).map((item: { description: any; name: any; value: any; }) => ({
      label: item.description || item.name,
      value: `${item.value}`
    })) || [];
  }, [currentDeviceData, selectedGun]);

  return (
    <div className={styles.content}>
      {/* 添加枪型选择表单 */}
      <Form layout="horizontal">
        <Form.Item label="充电枪" className='cst'>
          <Select
            allowClear
            value={selectedGun}
            onChange={setSelectedGun}
            style={{ width: 200 }}
          >
            <Option value="" title="">全部</Option>
            {gunOptions.map(gun => (
              <Option key={gun} value={gun} title="">
                {gun}枪
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Form>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        className='cst'
      />

      <div className={styles.gridContainer}>
        <FixedSizeList
          height={600}
          width="100%"
          itemCount={Math.floor(formItems.length / 2)}
          itemSize={34}
          style={{ paddingBottom: '60px' }}
        >
          {Row}
        </FixedSizeList>
      </div>
    </div>
  );
};
