import { Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import { RhButton } from '@/components/ui/pagbtn';
import RhPagination from '@/components/RhPagination';
import TableTitle from '@/components/TableTitle';
import RhDatePicker from '@/components/RhDatePicker';
import { formatDate } from '@/utils/formatDate';
import { generateUUID } from '@/utils/uuid';
import { httpGet, httpPost } from '@/shared/http';

const DEFAULTPAGESIZE = 10;
export const UpgradeLogs = () => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<any>({ startTime: '', endTime: '' });
  const [dateValue, setDateValue] = useState<any>(null);
  const [deviceTypeOptions, setDeviceTypeOptions] = useState<any[]>([]);
  const [deviceidOptions, setDeviceidOptions] = useState<any[]>([]);

  const [pagination, setPagination] = useState({
    total: 0,
    currentPage: 1,
    pageSize: DEFAULTPAGESIZE,
  });
  const [queryParams, setQueryParams] = useState<any>({
    startTime: '',
    endTime: '',
    pageIndex: 1,
    pageSize: DEFAULTPAGESIZE,
    deviceType: '',
    deviceId: '',
    result: '',
  });

  const onFilterChange = ({ column, value }: any) => {
    setQueryParams((prev: any) => ({
      ...prev,
      [column]: value,
      pageIndex: 1,
    }));
    setPagination((prev: any) => ({
      ...prev,
      currentPage: 1,
    }));
    const params = { ...queryParams, [column]: value, pageIndex: 1 };
    fetchLogsList(1, params);
  };
  const getFilters = async () => {
    const res = await httpGet('/api/history/log/upgradelog/filters');
    if (res.data?.deviceList) {
      const deviceList = res.data?.deviceList.map((item: any) => ({
        key: item.key,
        label: item.text,
      }));
      setDeviceidOptions(deviceList);
    }
    if (res?.data.deviceTypeList) {
      const deviceTypeList = res.data?.deviceTypeList.map((item: any) => ({
        key: item.key,
        label: item.text,
      }));
      setDeviceTypeOptions(deviceTypeList);
    }
  };

  const columns: ColumnsType<any> = [
    {
      title: '发生时间',
      dataIndex: 'beginTime',
      key: 'beginTime',
      width: 190,
      render: (time) => formatDate(time),
    },
    {
      title: (
        <TableTitle
          title="类别"
          column="deviceType"
          options={deviceTypeOptions}
          value={queryParams.deviceType || 'all'}
          onChange={onFilterChange}
        />
      ),
      dataIndex: 'deviceType',
      key: 'deviceType',
      width: 150,
      render: (type) => {
        if (typeof type === 'string') {
          const item = deviceTypeOptions.find((item: any) => item.key === type);
          return item?.label || '-';
        } else if (Array.isArray(type)) {
          const newType = type.map((item: any) => {
            const newitem = deviceTypeOptions.find((i: any) => i.key === item);
            return newitem?.label || '-';
          });
          return newType.join(',');
        }
        return '';
      },
    },
    {
      title: (
        <TableTitle
          title="对象"
          column="deviceId"
          options={deviceidOptions}
          value={queryParams.deviceId || 'all'}
          onChange={onFilterChange}
        />
      ),
      dataIndex: 'deviceId',
      key: 'deviceId',
      width: 150,
      render: (id) => {
        if (typeof id === 'string') {
          const item = deviceidOptions.find((item: any) => item.key === id);
          return item?.label || '-';
        } else if (Array.isArray(id)) {
          const newid = id.map((item: any) => {
            const newitem = deviceidOptions.find((i: any) => i.key === item);
            return newitem?.label || '-';
          });
          return newid.join(',');
        }
        return '';
      },
    },
    {
      title: (
        <TableTitle
          title="结束"
          column="result"
          options={[
            { key: '1', label: '成功' },
            { key: '2', label: '失败' },
          ]}
          value={queryParams.result || 'all'}
          onChange={onFilterChange}
        />
      ),
      dataIndex: 'result',
      key: 'result',
      width: 200,
      ellipsis: true,
      render: (result, row) => {
        if (`${result}` === '1') {
          return '成功';
        }
        if (`${result}` === '2') {
          return `失败:${row?.error || '-'}`;
        }
        return '';
      },
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 180,
      render: (time) => formatDate(time),
    },
  ];

  // const handleTimeChange = (value: TimeRangeValue | null) => {
  //   setTimeValue(value);
  //   console.log('Time range:', value);
  // };
  //   获取日志列表
  const fetchLogsList = async (current: number = 1, params?: any) => {
    let body = params || queryParams;
    body = {
      ...body,
      pageIndex: current || pagination.currentPage,
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
    };

    if (body.result) {
      body.result = Number(body.result);
    }
    setLoading(true);
    try {
      const res: any = await httpPost('/api/history/log/upgradelog/list', body);

      if (res.code === 0) {
        // 为每条数据添加 uuid
        const listWithUuid = (res.data?.list || []).map((item: any) => ({
          ...item,
          uuid: generateUUID(),
        }));
        setData(listWithUuid);
        setPagination((prev: any) => ({
          ...prev,
          total: res.data?.total || 0,
        }));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmSearch = () => {
    // if (!timeRange.startTime && !timeRange.endTime) {
    //   return;
    // }
    const params = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
    };
    setQueryParams((prev: any) => ({ ...prev, ...params }));
    setPagination({
      ...pagination,
      currentPage: 1,
    });
    fetchLogsList();
  };

  const handlePageChange = (page: number) => {
    setPagination((prev: any) => ({
      ...prev,
      currentPage: page,
    }));
    setQueryParams((prev: any) => ({
      ...prev,
      pageIndex: page,
    }));
    fetchLogsList(page);
  };
  // 处理日期变化
  const handleDateChange = (dates: any) => {
    setTimeRange({
      startTime: dates?.[0]?.valueOf() ? formatDate(dates?.[0]?.valueOf()) : '',
      endTime: dates?.[1]?.valueOf() ? formatDate(dates?.[1]?.valueOf()) : '',
    });
  };

  useEffect(() => {
    getFilters();
    fetchLogsList();
  }, []);

  return (
    <div className="flex flex-col">
      <div className="mb-4 flex items-center gap-3">
        <div className="relative flex items-center gap-4 text-[1.125rem]">
          {/* <CustomTimeRangePicker
            value={timeValue}
            onChange={handleTimeChange}
            defaultTimeUnit="hour"
            defaultNumber={2}
            timeUnitOptions={['hour', 'minute', 'second']}
            showTime={true}
            // placeholder="请选择开始时间"
          /> */}
          <span>开始时间</span>
          <RhDatePicker
            allowClear={true}
            value={dateValue} // 使用受控值
            onChange={(dates: any) => {
              setDateValue(dates); // 更新日期选择器的值
              handleDateChange(dates);
            }}
            className="cst"
          />
        </div>
        <RhButton text={'查询'} onClick={handleConfirmSearch} />
      </div>
      <Table
        rowKey="uuid"
        size="middle"
        columns={columns}
        dataSource={data}
        pagination={false}
        className="cst"
        loading={loading}
        scroll={{ y: 503 }}
      />
      {/* 翻页区域 超出当前页面显示才需要翻页*/}
      {pagination.total > 10 && (
        <div>
          <RhPagination
            total={pagination.total}
            current={pagination.currentPage}
            pageSize={pagination.pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};
