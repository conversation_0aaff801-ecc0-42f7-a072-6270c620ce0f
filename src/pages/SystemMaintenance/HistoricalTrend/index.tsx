import EchartsLineChart from '@/components/EchartsLineChart';
import { RhButton } from '@/components/ui/pagbtn';
import { message, Radio, Spin, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect, useMemo, useState } from 'react';
import AddPointModal from './components/AddPointModal';
import CustomTimeRangePicker, {
  calculateEndTime,
  TimeRangeValue,
} from '@/components/CustomTimeRangePicker';
import dayjs from 'dayjs';
import { httpGet, httpPost } from '@/shared/http';
import { formatDate } from '@/utils/formatDate';
import RhPagination from '@/components/RhPagination';
import clsx from 'clsx';
import styles from './index.module.less';

const dataType = [
  { value: 'historicalTrend', label: '历史趋势' },
  { value: 'historicalConditions', label: '历史工况' },
];

interface DataPoint {
  time: string;
  [key: string]: any;
}
//   曲线图配置
const defaultLineConfig = {
  name: '',
  dataKey: '',
  type: 'line' as const,
  smooth: true,
  showSymbol: false,
};
const DEFAULTPAGESIZE = 8;
const MAXTIMERANGE = 24; //小时为单位i
export const HistoricalTrend = () => {
  // 初始时间为当前时间减5分钟
  const initTime = [dayjs().subtract(5, 'minute'), dayjs()];

  const [type, setType] = useState(dataType[0].value);
  const [data, setData] = useState<DataPoint[]>([]);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [timeValue, setTimeValue] = useState<TimeRangeValue | null>({
    startTime: initTime[0],
    unit: 'minute',
    number: 5,
    calcType: 'after',
  });
  const [addedPoints, setAddedPoints] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [tableData, setTableData] = useState<DataPoint[]>([]);
  const [queryParams, setQueryParams] = useState<any>({
    startTime: initTime[0],
    endTime: initTime[1],
    pageIndex: 1,
    total: 0,
    pageSize: DEFAULTPAGESIZE,
  });

  const handleTypeChange = (value: string) => {
    setType(value);
  };
  // 时间范围改变
  const handleTimeChange = (value: TimeRangeValue | null) => {
    setTimeValue(value);
    const endTime = calculateEndTime({
      startTime: value?.startTime,
      calcNumber: value?.number,
      unit: value?.unit || 'hour',
      calcType: value?.calcType || 'after',
    });
    // 往前则开始时间为结束时间
    if (value?.calcType === 'before') {
      setQueryParams({
        ...queryParams,
        startTime: endTime,
        endTime: value?.startTime,
      });
      return;
    }

    setQueryParams({
      ...queryParams,
      startTime: value?.startTime,
      endTime: endTime,
    });
  };

  const getTextLength = (text: string) => {
    return new TextEncoder().encode(text).length;
  };
  //   表格配置
  const dynamicColumns = Object?.keys(addedPoints)
    ?.map((key) => {
      if (addedPoints[key]?.length > 0) {
        const res = addedPoints[key].map((item: any) => {
          return {
            title: (
              <span className="flex items-center text-center leading-[18px]">
                {key}
                <br />
                {item.descript}
              </span>
            ),
            dataIndex: `${key}-${item.name}`,
            key: `${key}-${item.name}`,
            width: Math.max(Math.max(getTextLength(key), getTextLength(item.descript)) * 6, 180),
          };
        });
        return res;
      }
      return null;
    })
    .filter((item: any) => !!item)
    .flatMap((item: any) => item);

  // 表格列定义
  const columns: ColumnsType<DataPoint> = useMemo(
    () => [
      {
        title: '序号',
        dataIndex: 'Serial',
        key: 'Serial',
        width: 80,
        render: (text: string, record: DataPoint, index: number) => {
          // 手动计算序号
          return index + 1 + (queryParams.pageIndex - 1) * queryParams.pageSize;
        },
      },
      {
        title: '时间',
        dataIndex: 'time',
        key: 'time',
        width: 200,
        render: (text: string) => {
          return formatDate(text);
        },
      },
      ...dynamicColumns,
    ],
    [dynamicColumns, queryParams],
  );
  // 图表配置
  const seriesConfig = useMemo(() => {
    return Object?.keys(addedPoints)
      ?.map((key) => {
        if (addedPoints[key]?.length > 0) {
          const res = addedPoints[key].map((item: any) => {
            const curConfig = {
              ...defaultLineConfig,
              name: `${key}(${item.descript})`,
              dataKey: `${key}-${item.name}`,
            };
            return curConfig;
          });
          return res;
        }
        return null;
      })
      .filter((item: any) => !!item)
      .flatMap((item: any) => item);
  }, [addedPoints]);

  //  历史曲线点位配置查询
  const getPointConfig = async () => {
    try {
      const res = await httpGet('/api/configs/dynamics/get?key=historicalTrendConfig');
      if (res.code === 0) {
        const points = JSON.parse(res.data.text);
        setAddedPoints(points);
        handleConfirmSearch(points);
      }
    } catch (error) {}
  };
  //  历史曲线点位配置初始化
  useEffect(() => {
    // 获取初始化点位数据并查询曲线和表格数据
    getPointConfig();
  }, []);

  //   添加点位确认
  const handleConfirm = async (values: any[]) => {
    setConfirmLoading(true);
    try {
      setAddedPoints(values);
      //  历史曲线点位配置保存
      await httpPost('/api/configs/dynamics/save', {
        key: 'historicalTrendConfig',
        text: values ? JSON.stringify(values) : '',
      });
      handleConfirmSearch(values);
      setConfirmVisible(false);
    } catch (error) {
    } finally {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    setConfirmVisible(false);
  };
  //  确认查询
  const handleConfirmSearch = async (points = addedPoints) => {
    if (!queryParams.startTime || !queryParams.endTime) {
      // 时间范围不能为空
      message.info({ content: `时间范围不能超过${MAXTIMERANGE}小时`, key: 'time' });
      return;
    }
    // 如果开始结束时间相差超过24小时不查询
    if (
      new Date(queryParams.endTime).getTime() - new Date(queryParams.startTime).getTime() >
      MAXTIMERANGE * 60 * 60 * 1000
    ) {
      message.info({ content: `时间范围不能超过${MAXTIMERANGE}小时`, key: 'time' });
      return;
    }
    if (Object.keys(points).length === 0) {
      message.info({ content: '请选择点位', key: 'point' });
      return;
    }
    try {
      handleEchartData(points);
      // 重新查询第一页
      handleTableData(1, points);
    } catch (error) {}
  };
  //   历史曲线批量查询
  const handleEchartData = async (points = addedPoints) => {
    try {
      setLoading(true);
      const deviceParams = Object.keys(points)
        .map((key) => ({
          deviceId: key,
          address: points?.[key]?.map((item: any) => item.name),
          NeedPaged: false,
        }))
        .filter((item: any) => item.address.length > 0);

      if (deviceParams.length === 0) {
        if (type === dataType[0].value) {
          message.info({ content: '请选择点位', key: 'point' });
        }
        setData(data || []);
        return;
      }
      // 如果开始时间和结束时间相差大于24小时不支持查询
      if (
        new Date(queryParams.endTime).getTime() - new Date(queryParams.startTime).getTime() >
        MAXTIMERANGE * 60 * 60 * 1000
      ) {
        if (type === dataType[0].value) {
          message.info({ content: `时间范围不能超过${MAXTIMERANGE}小时`, key: 'time' });
        }
        return;
      }

      // 批量查询历史工况
      const res = await httpPost('/api/device/address-values/chart', {
        deviceParams,
        // 时间格式化为当地时间
        startTime: formatDate(queryParams.startTime),
        endTime: formatDate(queryParams.endTime),
      });
      if (res.message === 'successed') {
        setData(res.data || []);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  // 表格数据分页查询
  const handleTableData = async (current: number = 1, points = addedPoints) => {
    if (!queryParams.startTime || !queryParams.endTime) {
      // 时间范围不能为空
      message.info({ content: '请选择时间范围', key: 'time' });
      return;
    }
    // 如果开始时间和结束时间相差大于24小时不支持查询
    if (
      new Date(queryParams.endTime).getTime() - new Date(queryParams.startTime).getTime() >
      MAXTIMERANGE * 60 * 60 * 1000
    ) {
      message.info({ content: `时间范围不能超过${MAXTIMERANGE}小时`, key: 'time' });
      return;
    }
    try {
      setTableLoading(true);
      const deviceParams = Object.keys(points)
        .map((key) => ({
          deviceId: key,
          address: points?.[key]?.map((item: any) => item.name),
          NeedPaged: true,
        }))
        .filter((item: any) => item.address.length > 0);
      if (deviceParams.length === 0) {
        if (type === dataType[1].value) {
          message.info({ content: '请选择点位', key: 'point' });
        }
        setTableData([]);
        setQueryParams((prev: any) => ({
          ...prev,
          total: 0,
        }));
        return;
      }

      const res = await httpPost('/api/device/address-values/table', {
        deviceParams,
        ...queryParams,
        pageIndex: current || queryParams.pageIndex,
        // 时间格式化为当地时间
        startTime: formatDate(queryParams.startTime),
        endTime: formatDate(queryParams.endTime),
      });
      if (res.message === 'successed') {
        let data: DataPoint[] = [];
        res.data?.list?.forEach((item: any) => {
          data.push(item);
        });
        setTableData(data);
        setQueryParams((prev: any) => ({
          ...prev,
          total: res.data?.total,
        }));
      }
    } catch (error) {
    } finally {
      setTableLoading(false);
    }
  };

  // 处理分页变化
  const handleTableChange = (current: any) => {
    setQueryParams((prev: any) => ({
      ...prev,
      pageIndex: current,
    }));
    //  表格数据查询页码更新
    handleTableData(current);
  };

  return (
    <div className="flex flex-col">
      <div className="flex items-center justify-between">
        <div>
          <CustomTimeRangePicker
            value={timeValue}
            onChange={handleTimeChange}
            defaultTimeUnit="hour"
            defaultNumber={5}
            timeUnitOptions={['hour', 'minute', ]}//'second'
            showTime={true}
            max={MAXTIMERANGE} // 最大时间范围——小时
          />
        </div>
        <div className="flex gap-3">
          <RhButton text={'确认设置'} onClick={() => handleConfirmSearch(addedPoints)} />
          <RhButton text={'添加点位'} onClick={() => setConfirmVisible(true)} />
        </div>
      </div>
      <div className="relative my-5 flex">
        <Radio.Group
          onChange={(e) => handleTypeChange(e.target.value)}
          defaultValue={dataType?.[0]?.value || 'read'}
          value={type}
          className={`custom-radio-group flex w-full justify-center`}
        >
          {(dataType || [])?.map((item) => (
            <Radio.Button key={item?.value} value={item?.value} className={'custom-radio'}>
              {item?.label}
            </Radio.Button>
          ))}
        </Radio.Group>
      </div>
      <div>
        {type === 'historicalTrend' && (
          <Spin spinning={loading}>
            <EchartsLineChart
              data={data}
              xAxisKey="time"
              series={seriesConfig}
              height={547}
              // onDataClick={handleDataClick}
            />
          </Spin>
        )}
        {type === 'historicalConditions' && (
          <>
            <Table
              columns={columns}
              dataSource={tableData || []} //|| tableData
              className={clsx('cst', styles['historicalConditions-table'])}
              scroll={{ y: 425}} //400
              pagination={false}
              loading={tableLoading}
              rowKey={(record) => record.time}
              size="middle"
            />
            <div className="mt-[-3px] flex justify-end">
              <RhPagination
                total={queryParams.total}
                current={queryParams.pageIndex}
                pageSize={queryParams.pageSize}
                onPageChange={handleTableChange}
              />
            </div>
          </>
        )}
        <AddPointModal
          points={addedPoints}
          open={confirmVisible}
          onCancel={handleCancel}
          onConfirm={handleConfirm}
          confirmLoading={confirmLoading}
        />
      </div>
    </div>
  );
};
