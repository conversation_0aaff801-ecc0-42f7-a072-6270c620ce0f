import { Menu, Modal, Layout, Card, List, Checkbox, message } from 'antd';
import './index.less';
import { CloseOutlined } from '@ant-design/icons';
import { useCallback, useEffect, useRef, useState } from 'react';
import RhSearchInput from '@/components/RhSearchInput';
import { RhKeyboard } from '@/components/RhKeyboard';
import { httpGet } from '@/shared/http';

const { Sider, Content } = Layout;

interface AddPointModalProps {
  points?: any; //已经选择的点位
  open: boolean;
  confirmLoading?: boolean;
  onCancel: () => void;
  onConfirm: (selectedPoints: any) => void;
}

function transformDeviceData(originalData: any): any[] {
  if (!originalData || typeof originalData !== 'object') {
    return [];
  }
  const result: any[] = [];
  // 遍历每个设备
  Object.keys(originalData).forEach((deviceName) => {
    const deviceData = originalData[deviceName];

    if (Array.isArray(deviceData)) {
      // 为每个数据点添加设备名称
      deviceData.forEach((item) => {
        result.push({
          ...item,
          device: deviceName,
        });
      });
    }
  });

  return result;
}
const MAXCOUNT = 20; //最多支持的点位数量

const AddPointModal = ({
  points = {},
  open,
  confirmLoading,
  onCancel,
  onConfirm,
}: AddPointModalProps) => {
  const [allPoints, setAllPoints] = useState<any>([]);
  const [selectedKey, setSelectedKey] = useState<string>('');
  const [selectedPoints, setSelectedPoints] = useState<any>(points || {}); // 包含设备和点位信息
  const [searchValue, setSearchValue] = useState('');
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [searchPoints, setSearchPoints] = useState<any>([]);
  const [loading, setLoading] = useState(false);

  const keyboardRef = useRef<any>(null);
  const [deviceItems, setDeviceItems] = useState<any>([]);

  useEffect(() => {
    if (open) {
      setSelectedPoints(points);
    }
  }, [points, open]);

  const getDeviceItems = async () => {
    const res = await httpGet('/api/device/all');
    if (res.code === 0) {
      const items = res.data.map((item: any) => {
        return {
          ...item,
          key: item.id,
          label: item.name,
        };
      });
      setDeviceItems(items || []);
      setSelectedKey(items[0].key);
    }
  };
  const getDeviceAllPoints = useCallback(async () => {
    //获取指定设备的点位列表  /api/device/address/list
    if (!selectedKey) return;
    setLoading(true);
    try {
      const res = await httpGet('/api/device/address/list', {
        deviceID: selectedKey,
        pageIndex: 0,
        pageSize: 0,
      });
      if (res.code === 0) {
        const list = res.data?.list?.map((item: any) => {
          return {
            ...item,
            key: item.id,
          };
        });
        setAllPoints(list);
        setLoading(false);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  }, [selectedKey]);

  useEffect(() => {
    getDeviceAllPoints();
    if (searchValue) {
      handleSearch(searchValue);
    }
  }, [selectedKey, searchValue]);

  useEffect(() => {
    // 获取所有设备列表
    getDeviceItems();
  }, []);

  const handleConfirm = () => {
    onConfirm(selectedPoints);
  };

  const handleCancel = () => {
    setSelectedPoints([]);
    setSearchValue('');
    setSearchPoints([]);
    onCancel?.();
  };

  const handleMenuClick = (key: string) => {
    setSelectedKey(key);
  };
  //   删除点位
  const handleRemovePoints = (delKey: string, deviceKey: string) => {
    setSelectedPoints((prev: any) => {
      return {
        ...prev,
        [deviceKey]: prev[deviceKey].filter((item: { key: string }) => item.key !== delKey),
      };
    });
  };

  const handleSearch = useCallback(
    (value: string) => {
      // 在allPoints中搜索全部包含Value的项，并按照allPoints的格式显示 不区分大小写
      const searchPoints = allPoints.filter((item: { name: string }) =>
        item.name.toLowerCase().includes(value.toLowerCase()),
      );
      setSearchPoints(searchPoints);
    },
    [allPoints],
  );
  //   点位选中/取消
  const handleChangeSelectedPoints = (checked: any, item: any) => {
    if (checked) {
      // 所有设备点位数量累计限制最多20个
      const totalCount = Object.keys(selectedPoints).reduce((acc, key) => {
        return acc + selectedPoints[key].length;
      }, 0);

      if (totalCount >= MAXCOUNT) {
        message.error(`最多添加${MAXCOUNT}个点位`);
        return;
      }
      setSelectedPoints((prev: any) => {
        if (prev[selectedKey]) {
          return {
            ...prev,
            [selectedKey]: [...prev[selectedKey], item],
          };
        } else {
          return {
            ...prev,
            [selectedKey]: [item],
          };
        }
      });
    } else {
      setSelectedPoints((prev: any) => {
        if (prev[selectedKey]) {
          return {
            ...prev,
            [selectedKey]: prev[selectedKey].filter((p: any) => p.name !== item.name),
          };
        } else {
          return {
            ...prev,
          };
        }
      });
    }
  };
  useEffect(() => {
    handleSearch(searchValue);
  }, [searchValue]);

  return (
    <Modal
      open={open}
      title={'添加点位'}
      onCancel={handleCancel}
      footer={null}
      width={1042}
      centered
      closable={true}
      closeIcon={<CloseOutlined className="modal-close-icon" />}
      className="historical-trend-modal"
      maskClosable={false}
      zIndex={9999999} //高于Echarts中tooltip的z-index
    >
      <div className="modal-content">
        <Card title="设备点位" className="equipment-point">
          <Layout style={{ height: '100%', background: 'transparent' }}>
            <Sider
              width={180}
              style={{ background: 'transparent', overflowY: 'auto' }}
              className="border-r border-[#4070C8]"
            >
              <Menu
                mode="inline"
                items={deviceItems}
                defaultOpenKeys={[deviceItems?.[0]?.key]}
                defaultSelectedKeys={[deviceItems?.[0]?.key]}
                selectedKeys={[selectedKey]}
                onClick={({ key }) => handleMenuClick(key as any)}
                style={{ background: 'transparent' }}
              />
            </Sider>
            <Content className="device-content cst p-6">
              <div>
                <RhSearchInput
                  allowClear={false}
                  value={searchValue}
                  placeholder="请输入关键词搜索"
                  onFocus={() => setShowKeyboard(true)}
                  className="mb-4 h-[48px] w-full !rounded-lg"
                  style={{ borderRadius: '8px !important' }}
                />
              </div>
              <List
                size="large"
                dataSource={searchValue ? searchPoints : allPoints}
                loading={loading}
                renderItem={(item: any) => {
                  return (
                    <List.Item>
                      <span className="item-name">{item.name}：{item.descript}</span>
                      <div className="option-icon">
                        <Checkbox
                          checked={selectedPoints?.[selectedKey]?.some(
                            (p: any) => p.name === item.name,
                          )}
                          onChange={(e) => handleChangeSelectedPoints(e.target.checked, item)}
                        />
                      </div>
                    </List.Item>
                  );
                }}
              />
            </Content>
          </Layout>
        </Card>
        <Card title="已选点位" className="selected-point">
          <div className="p-6">
            <List
              size="large"
              dataSource={transformDeviceData(selectedPoints) || []}
              renderItem={(item: any) => {
                return (
                  <List.Item>
                    <span className="item-name">
                      {item.descript} ({item.device})
                    </span>

                    <div
                      onClick={() => handleRemovePoints(item.key, item.device)}
                      className="close-icon"
                    >
                      <CloseOutlined />
                    </div>
                  </List.Item>
                );
              }}
            />
          </div>
        </Card>
      </div>
      <div className="flex w-full justify-center border-t border-[#4070C8] p-6">
        <div className="confirm-footer">
          <button className="cancel-button" onClick={handleCancel}>
            {'取消'}
          </button>
          <button className="confirm-button" onClick={handleConfirm} disabled={confirmLoading}>
            {confirmLoading ? '处理中...' : '确认'}
          </button>
        </div>
      </div>
      <RhKeyboard
        init={(r: any) => (keyboardRef.current = r)}
        show={showKeyboard}
        // layoutType={'chinese'}
        onClose={() => setShowKeyboard(false)}
        onChange={(value) => setSearchValue(value)}
        onKeyPress={(key) => {
          if (key === '{enter}') {
            setShowKeyboard(false);
            handleSearch(searchValue);
          }
        }}
        customDisplay={{
          '{bksp}': '删除',
          '{enter}': '搜索',
          '{shift}': 'Shift',
          '{space}': '空格',
          '{tab}': 'Tab',
          '{lock}': 'Lock',
        }}
      />
    </Modal>
  );
};

export default AddPointModal;
