.historical-trend-modal {
  .ant-modal-close {
    height: 82px;
    top: 0;
    width: 72px;
    right: 0;
    .modal-close-icon {
      font-size: 32px;
      color: #a5cfff;
      width: 24px;
      height: 24px;
      line-height: 24px;
    }
  }
  .ant-modal-header {
    background-color: transparent;
    border-bottom: 1px solid #4070c8;
    padding: 24px;
    margin: 0;
    .ant-modal-title {
      font-size: 22px;
      color: #e3f8ff;
    }
  }
  // .ant-modal-body {
  //   padding: 24px;
  // }
  .ant-modal-content {
    background-color: #143f8c;
    border: none;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
  }

  .modal-content {
    font-size: 20px;
    font-weight: 400;
    line-height: 1em;
    padding: 24px;
    color: #e3f8ff;
    display: flex;
    gap: 36px;
    .equipment-point {
      flex: 2;
    }
    .selected-point {
      flex: 1;
    }
    .ant-card {
      border: none;
    }
    .ant-card {
      .ant-card-head {
        font-weight: 500;
        font-size: 22px;
        line-height: unset;
        color: #e3f8ff;
        padding: 0;
        border-bottom: none;
      }
      .ant-card-body {
        border: 1px solid #4070c8;
        border-radius: 8px;
        padding: 0;
        height: 470px;
      }
    }
    .ant-menu {
      font-size: 20px;
      color: #6797d6;
      .ant-menu-item {
        border-radius: 0;
        width: 100%;
        margin-inline: 0;
        margin-block: 0;
        height: 70px;
        line-height: 70px;
        .ant-menu-title-content {
          text-align: center;
          border-bottom: 2px solid #4070c8;
        }
      }
      .ant-menu-item-selected {
        color: #e3f8ff;
        background-color: #0478ec;
        .ant-menu-title-content {
          color: #e3f8ff !important;
        }
      }
      > .ant-menu-item:first-child {
        border-top-left-radius: 8px;
      }
      // > .ant-menu-item:last-child {
      //   border-bottom-left-radius: 8px;
      // }
    }
    .device-content,
    .selected-point {
      height: 100% !important;
      overflow-y: auto;
      .ant-list-item {
        color: #e3f8ff;
        font-size: 20px;
        position: relative;
        padding: 16px;
      }
      .ant-list-split .ant-list-item {
        border-block-end: none;
        span.item-name{
          max-width: calc(100% - 48px);
        }
      }
      li.ant-list-item:nth-child(odd) {
        background: #447bc31a;
      }
      .close-icon {
        height: 100%;
        position: absolute;
        right: 0;
        padding: 16px;
        display: flex;
        align-items: center;
        .anticon svg {
          width: 32px;
          height: 32px;
          color: #579cdb;
        }
      }
      .option-icon {
        .ant-checkbox .ant-checkbox-inner {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border-width: 2px;
          background-color: transparent;
          border-color: #579cdb;
        }

        .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #6de875;
          border-color: #6de875;
        }
        .ant-checkbox .ant-checkbox-inner:after {
          width: 13px;
          height: 20px;
          border-width: 4px;
          border-color: #013d8e;
        }
      }
    }
    .device-content .ant-list{
      overflow-y: auto;
      height: calc(100% - 64px);
    }
    .selected-point .ant-card-body {
      overflow-y: auto;
    }

    .selected-point .ant-list-item span.item-name {
      width: 200px;
    }

    .ant-empty-normal .ant-empty-description {
      color: #e3f8ff;
    }
  }

  .confirm-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    // padding: 12px 0 24px;
    width: 50%;

    button {
      flex: 1;
      width: 25%;
      padding: 12px 16px;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-weight: 400;
      border-radius: 6px;
      line-height: 1.25em;
      cursor: pointer;
      min-width: 80px;
      text-align: center;

      &.cancel-button {
        background-color: transparent;
        border: 1px solid #a5cfff;
        color: #a5cfff;

        &:hover {
          color: #e3f8ff;
          border-color: #e3f8ff;
        }
      }

      &.confirm-button {
        background-color: #0478ec;
        border: 1px solid #0478ec;
        color: #ffffff;

        &:hover {
          opacity: 0.9;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
  .custom-input,
  .custom-input:focus,
  .custom-input:focus-within,
  .custom-btn,
  .custom-btn:hover {
    border-radius: 6px;
    border-width: 1px;
    border: 1px solid #39abff;
    background: linear-gradient(
      180deg,
      rgba(0, 135, 255, 0.65) -3.41%,
      rgba(0, 135, 255, 0.08) 96.68%
    ) !important;

    input {
      color: #fff !important;
    }
  }
}
