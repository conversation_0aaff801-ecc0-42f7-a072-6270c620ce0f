import RhPagination from '@/components/RhPagination';
import { RhButton } from '@/components/ui/pagbtn';
import { httpGet, httpPost, httpPut } from '@/shared/http';
import { Switch, Table } from 'antd';
import pagination from 'antd/es/pagination';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const DEFAULTPAGESIZE = 10;
export const DataForwarding = () => {
  const navigate = useNavigate();
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
    currentPage: 1,
    pageSize: DEFAULTPAGESIZE,
  });

  //   启停操作
  const handleSwitchChange = async (uuid: string, checked: boolean) => {
    setLoading(true);
    try {
      await httpPut(`/api/configs/transmit/start?id=${uuid}&&isStart=${checked}`);
      fetchData();
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const columns: ColumnsType<any> = [
    {
      title: '序号',
      dataIndex: 'No',
      key: 'No',
      // 根据index计算当前序号
      render: (text: string, row: any, index: number) => {
        return (
          <div>
            {pagination.currentPage * pagination.pageSize - pagination.pageSize + index + 1}
          </div>
        );
      },
    },
    {
      title: '连接名称',
      dataIndex: 'appletName',
      key: 'appletName',
      width: 200,
    },
    {
      title: '目标平台',
      dataIndex: 'appletModelText',
      key: 'appletModelText',
      width: 180,
    },
    {
      title: '启停',
      dataIndex: 'isStartUp',
      key: 'isStartUp',
      width: 180,
      //   自定义启停显示为Switch按钮
      render: (status: string, row: { id: string }) => {
        return (
          <Switch
            className="param-switch"
            value={status === 'true'}
            onChange={(checked) => handleSwitchChange(row.id, checked)}
          />
        );
      },
    },
    {
      title: '连接状态',
      dataIndex: 'connStatus',
      key: 'connStatus',
      width: 180,
      //   自定义连接状态显示为文字
      render: (status: string) => <span>{status === 'Disconnect' ? '断开' : '连接'}</span>,
    },
    {
      title: '断点续传',
      dataIndex: 'uploadHistoryData',
      key: 'uploadHistoryData',
      width: 100,
      render: (text: string) => <span>{!text ? '关闭' : '开启'}</span>,
    },
    {
      title: '操作',
      dataIndex: '',
      key: 'x',
      width: 80,
      render: (_: any, item: any) => (
        <div
          className="h-full w-full"
          onClick={() => {
            navigate(`/system-maintenance/data-forwarding/detail?id=${item.id}`);
          }}
        >
          <a className="text-[#6DE875]">详情</a>
        </div>
      ),
    },
  ];
  const handleCreate = () => {
    navigate('/system-maintenance/data-forwarding/edit?id=-1');
  };
  const handlePageChange = (page: number) => {
    setPagination({
      ...pagination,
      currentPage: Math.max(1, Math.min(page, Math.ceil(pagination.total / pagination.pageSize))),
    });
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await httpGet(
        '/api/configs/transmit/list?pageIndex=' +
          pagination.currentPage +
          '&pageSize=' +
          pagination.pageSize,
      );
      setData(res.data?.list);
      setPagination({
        ...pagination,
        total: res.data?.total || 0,
      });
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [pagination.currentPage, pagination.pageSize]);

  return (
    <div>
      <div className="mb-[22px] flex">
        <RhButton text={'创建'} onClick={handleCreate} />
      </div>
      <Table
        rowKey="uuid"
        size="middle"
        columns={columns}
        dataSource={data}
        pagination={false}
        className="cst"
        loading={loading}
        scroll={{ y: 450 }}
      />
      {/* 翻页区域 超出当前页面显示才需要翻页*/}
      {pagination.total > 10 && (
        <div>
          <RhPagination
            total={pagination.total}
            current={pagination.currentPage}
            pageSize={pagination.pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};
