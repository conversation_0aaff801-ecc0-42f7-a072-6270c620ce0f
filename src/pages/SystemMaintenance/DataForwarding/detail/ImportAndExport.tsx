import { RhButton } from '@/components/ui/pagbtn';
import { useEffect, useState } from 'react';
import { DataImportPanel } from './DataImportPanel';
import UploadModal from '../../../../components/UploadModal/uploadModal';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import {
  useStartStop,
  useImportHandler,
  useUploadHandler,
  useExportHandler,
  handleConfirmExport,
  handleCancelConfirm,
  useCancelWithParamsHandler,
  useGetDetail,
} from './util';

export const ImportAndExport = () => {
  const id = new URLSearchParams(window.location.search).get('id');

  // 导入相关状态
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalParams, setModalParams] = useState({
    title: '导入',
    tips: '请上传配置文件',
  });

  // 二次确认状态
  const [confirmVisible, setConfirmVisible] = useState<boolean>(false);
  const [confirmFuncParams, setConfirmFuncParams] = useState<any>(null);

  const [detail, setDetail] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [optLoading, setOptLoading] = useState<boolean>(false);

  // 使用公共方法
  const getDetail = useGetDetail(id, setDetail);
  const handleUpload = useUploadHandler(id, setIsModalOpen, getDetail);
  const handleCancel = useCancelWithParamsHandler(id);
  const handleImport = useImportHandler<{ title: string }>(setModalParams, setIsModalOpen);
  const handleExport = useExportHandler(setConfirmVisible, setConfirmFuncParams, id);
  const handleStart = useStartStop(
    id,
    detail.isStartUp === 'true' ? false : true,
    setOptLoading,
    getDetail,
  );

  const handleSubmit = () => {
    console.log('提交');
  };

  useEffect(() => {
    // 获取详情
    getDetail();
  }, []);

  return (
    <div className="h-full w-full p-4">
      {/* 顶部操作按钮 */}
      <div className={'mb-5 flex justify-between'}>
        <RhButton ghost text={'返回上级'} style={{ width: 110 }} onClick={handleCancel}></RhButton>
        <div className={'flex flex-row gap-[20px]'}>
          <RhButton ghost text={'导入'} style={{ width: 110 }} disabled={detail?.isStartUp === 'true'} onClick={handleImport}></RhButton>
          <RhButton ghost text={'导出'} style={{ width: 110 }} onClick={handleExport}></RhButton>
          <RhButton
            // ghost
            text={detail?.isStartUp === 'true' ? '停止' : '启动'}
            style={{ width: 110 }}
            onClick={handleStart}
            loading={optLoading}
          ></RhButton>
        </div>
      </div>

      {/* 导入数据展示 */}
      <DataImportPanel onSubmit={handleSubmit}  detail={detail}/>
      <UploadModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onUpload={handleUpload}
        accept=".xlsx"
        fileSize={50}
        {...modalParams}
      />
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => handleCancelConfirm(setConfirmVisible)}
        onConfirm={() => handleConfirmExport(confirmFuncParams, setConfirmVisible)}
        content={<div className="leading-7">确认导出配置吗？</div>}
      />
    </div>
  );
};

export default ImportAndExport;
