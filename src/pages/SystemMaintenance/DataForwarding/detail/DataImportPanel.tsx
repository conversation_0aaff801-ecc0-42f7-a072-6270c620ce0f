import React, { useCallback, useEffect, useState } from 'react';
import { Tree } from 'antd';
import type { DataNode } from 'antd/es/tree';
import './index.less';
import { RhButton } from '@/components/ui/pagbtn';
import { useDeviceStore } from '@/store/deviceStore';
import { formatDeviceData } from './util';
import { httpGet } from '@/shared/http';

// 定义组件的属性接口
export interface DataImportPanelProps {
  detail?: any;
  onSubmit?: () => void;
}

const TreeIcon = ({ className }: { className: string }) => {
  return (
    <svg
      className={className}
      width="20"
      height="18"
      viewBox="0 0 20 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M11.5 13.1H16.5V14.5H11.5V13.1Z" />
      <path d="M8.83945 0L10.8394 2.5H20V18H0L0 0H8.83945ZM18.6 16.6V3.9H10.0902L8.09019 1.4H1.4V16.6H18.6Z" />
    </svg>
  );
};

/**
 * 导入数据展示面板组件
 * 用于展示设备类型、设备名称、点位名称和触发方式的树形结构和列表
 */
export const DataImportPanel: React.FC<DataImportPanelProps> = ({ onSubmit, detail }) => {
  const [selectedDeviceType, setSelectedDeviceType] = useState<string | null>(null);
  const [selectedDevice, setSelectedDevice] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [devicesPerPage] = useState(7);
  const [deviceTypeData, setDeviceTypeData] = useState<any[]>([]);
  const { getTypeById } = useDeviceStore();
  const [deviceData, setDeviceData] = useState<Record<string, { id: string; name: string }[]>>({});
  const [pointData, setPointData] = useState<
    Record<string, { id: string; name: string; triggerMode: string }[]>
  >({});

  // 筛选当前选中设备类型下的设备
  const filteredDevices = selectedDeviceType ? deviceData[selectedDeviceType] || [] : [];
  const totalPages = Math.ceil(filteredDevices.length / devicesPerPage);
  const startIndex = (currentPage - 1) * devicesPerPage;
  const currentDevices = filteredDevices.slice(startIndex, startIndex + devicesPerPage);

  // 筛选当前选中设备下的点位
  const filteredPoints = selectedDevice ? pointData[selectedDevice] || [] : [];

  const handleSubmitInternal = useCallback(() => {
    if (onSubmit) {
      onSubmit();
    } else {
      console.log('提交');
    }
  }, [onSubmit]);

  const handleDeviceTypeSelect = useCallback((selectedKeys: React.Key[]) => {
    const selectedKey = selectedKeys[0] as string | undefined;
    setSelectedDeviceType(selectedKey || null);
    setSelectedDevice(null); // 重置选中的设备
    setCurrentPage(1); // 重置页码
  }, []);

  const handleDeviceSelect = useCallback((deviceId: string) => {
    setSelectedDevice(deviceId);
  }, []);

  const handlePrevPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  }, [currentPage]);

  const handleNextPage = useCallback(() => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  }, [currentPage, totalPages]);

  const getDeviceTypeData = async () => {
    // 读取设备类型配置文件 DeviceType.cfg
    const deviceTypes = await httpGet('/api/configs/get?fileName=DeviceType.cfg');
    const formatData = formatDeviceData(deviceTypes);
    setDeviceTypeData(formatData);
  };

  useEffect(() => {
    if (!detail?.eventList || !detail?.eventList?.length) return;
    /**
     *
     * deviceData={[类型]：{id,name}[]}
     */
    const deviceData: any = {};

    const pointData: any = {};

    detail?.eventList.forEach((event: any) => {
      const { eventName = '', pushMsgContent = [] } = event;
      pushMsgContent?.forEach((item: any) => {
        const { deviceName = '', itemList = [] } = item;
        const currentDeviceList = deviceName.split(',').map((device: string) => {
          const [name, id] = device.split('|');
          const type = getTypeById(id);
          return { id, name, type };
        });
        const currentPointList = (itemList || []).map((point: string) => {
          const [id, name] = point?.split('|');
          return { id, name, triggerMode: eventName };
        });
        currentDeviceList.forEach((device: any) => {
          const { id, name, type } = device;
          if (!deviceData[type]) {
            deviceData[type] = [];
          }
          deviceData[type].push({ id, name });
          if (!pointData[id]) {
            pointData[id] = [];
          }
          pointData[id].push(...currentPointList);
        });
      });
    });
    setDeviceData(deviceData);
    setPointData(pointData);
  }, [detail?.eventList]);

  useEffect(() => {
    getDeviceTypeData();
  }, []);

  return (
    <div className={'flex justify-between'}>
      <div className="grid h-full w-full grid-cols-2 rounded-lg border border-[#39ABFF]">
        <div className="col-span-1 flex h-full w-full flex-col border-r-[1px] border-[#39ABFF]">
          <div className="col-span-1 grid w-full flex-1 grid-cols-2">
            {/* 设备类型 */}
            <div className="flex h-full flex-col border-r-[1px] border-[#39ABFF]">
              <h3 className="border-b-[1px] border-[#39ABFF] p-4 text-xl font-bold text-white">
                设备类型
              </h3>
              <div className="col-span-1">
                <Tree
                  showIcon={true}
                  icon={<TreeIcon className="device-icon h-[18px] w-[20px]" />}
                  className="custom-device-tree h-[420px] max-h-[420px] overflow-y-auto bg-transparent text-[18px] text-[#E3F8FF]"
                  defaultExpandAll
                  onSelect={handleDeviceTypeSelect}
                  treeData={deviceTypeData}
                  selectedKeys={selectedDeviceType ? [selectedDeviceType] : []}
                />
              </div>
            </div>

            {/* 设备名称 */}
            <div className="col-span-1 border-[#39ABFF]">
              <h3 className="border-b-[1px] border-[#39ABFF] p-4 text-xl font-bold text-white">
                设备名称
              </h3>
              <div className="flex h-[calc(100%-62px)] flex-col">
                {currentDevices.length > 0 ? (
                  currentDevices.map((device) => (
                    <div
                      key={device.id}
                      className={`cursor-pointer border-b-[1px] border-[#39ABFF] p-4 text-[18px] ${selectedDevice === device.id ? 'bg-[#39ABFF] text-white' : 'bg-transparent text-[#E3F8FF] hover:bg-[#2C5DB2]'}`}
                      onClick={() => handleDeviceSelect(device.id)}
                    >
                      {device.name}
                      {/* |{device.id} */}
                    </div>
                  ))
                ) : (
                  <div className="flex h-full w-full items-center justify-center">
                    <div className="py-4 text-center text-xl text-gray-400">暂无数据</div>
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* 翻页按钮 */}
          <div className="flex flex-row items-center justify-end gap-2 border-t-[1px] border-[#39ABFF] p-4">
            <span className="text-center text-xl text-[#76AAD9]">
              共{filteredDevices.length}条数据
            </span>
            {filteredDevices.length > 0 && (
              <div className="text-center text-xl text-[#76AAD9]">
                {currentPage}/{totalPages}页
              </div>
            )}
            <RhButton
              text={'上一页'}
              style={{ width: 115, height: 48 }}
              disabled={currentPage === 1}
              onClick={handlePrevPage}
            ></RhButton>
            <RhButton
              text={'下一页'}
              style={{ width: 115, height: 48 }}
              disabled={currentPage === totalPages || totalPages === 0}
              onClick={handleNextPage}
            ></RhButton>
          </div>
        </div>

        <div className="col-span-1 flex h-full w-full flex-col">
          <div className="col-span-1 grid w-full grid-cols-2">
            <h3 className="border-b-[1px] border-[#39ABFF] p-4 text-xl font-bold text-white">
              点位名称
            </h3>
            <h3 className="border-b-[1px] border-[#39ABFF] p-4 text-xl font-bold text-white">
              触发方式
            </h3>
          </div>
          <div className="col-span-1 grid h-[455px] max-h-[455px] min-h-[455px] w-full flex-1 grid-cols-2 overflow-y-auto">
            {/* 点位名称 */}
            <div className="col-span-2 h-full">
              {/* <h3 className="border-b-[1px] border-[#39ABFF] p-4 text-xl font-bold text-white">
                点位名称
              </h3> */}
              <div className="flex h-full flex-col">
                {filteredPoints.length > 0 &&
                  filteredPoints.map((point) => (
                    <div
                      key={point.id}
                      className="flex flex-row border-b-[1px] border-[#39ABFF] bg-transparent"
                    >
                      <div className="flex-1 border-r-[1px] border-[#39ABFF] bg-transparent p-4 text-[18px] text-[#E3F8FF]">
                        {point.name}
                      </div>
                      <div className="flex-1 bg-transparent p-4 text-[18px] text-[#E3F8FF]">
                        {point.triggerMode}
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            {/* 触发方式 */}
            {/* <div className="col-span-1 h-full">
              <h3 className="border-b-[1px] border-[#39ABFF] p-4 text-xl font-bold text-white">
                触发方式
              </h3>
              <div className="flex h-full flex-col">
                {filteredPoints.length > 0 &&
                  filteredPoints.map((point) => (
                    <div
                      key={point.id}
                      className="border-b-[1px] border-[#39ABFF] bg-transparent p-4 text-[18px] text-[#E3F8FF]"
                    >
                      {point.triggerMode}
                    </div>
                  ))}
              </div>
            </div> */}
            {!filteredPoints ||
              (filteredPoints.length <= 0 && (
                <div className="col-span-2 flex w-full justify-center">
                  <div className="py-4 text-center text-xl text-gray-400">暂无数据</div>
                </div>
              ))}
          </div>
          <div className="flex flex-row items-center justify-end gap-2 border-t-[1px] border-[#39ABFF] p-4">
            {/* TODO 当前版本暂时不需要，后续添加编辑放开。div占位 */}
            <div style={{ width: 115, height: 48 }}></div>
            {/* <RhButton
              text={'确定'}
              style={{ width: 115, height: 48 }}
              onClick={handleSubmitInternal}
            ></RhButton> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataImportPanel;
