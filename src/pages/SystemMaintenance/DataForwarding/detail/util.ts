import { httpGet, httpPostFile, httpPut } from '@/shared/http';
import { useCallback } from 'react';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { asyncDownloadFile } from '@/utils/download';

// 处理启动停止操作
export const useStartStop = (
  uuid: string | null,
  checked: boolean,
  setLoading: React.Dispatch<React.SetStateAction<boolean>>,
  refreshData?: () => Promise<void>,
) => {
  return useCallback(async () => {
    try {
      if (!uuid) {
        return;
      }
      setLoading?.(true);
      await httpPut(`/api/configs/transmit/start?id=${uuid}&&isStart=${checked}`);
      message.success(checked ? '启动成功' : '停止成功');
    } catch (error) {
      message.error(checked ? '启动失败' : '停止失败');
    } finally {
      setLoading?.(false);
      // 操作完成后刷新数据
      if (refreshData) {
        await refreshData();
      }
    }
  }, [uuid, checked, setLoading, refreshData]);
};

// 生成唯一ID
export const generateId = () => `trigger_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// 获取光标位置信息
export const getCursorInfo = () => {
  // 1. 获取光标所在元素
  let activeElement = document.activeElement;

  // 2. 获取光标坐标
  let pagePosition = null;
  if (activeElement) {
    const rect = activeElement.getBoundingClientRect();
    if (rect) {
      pagePosition = {
        top: rect.top + window.pageYOffset || document.documentElement.scrollTop,
        left: rect.left + window.pageXOffset || document.documentElement.scrollLeft,
      };
    }
  }

  return pagePosition;
};

// 过滤不包含trigger的键
export const filterNonTriggerValues = (obj: any) => {
  const result: Record<string, any> = {};

  // 遍历对象所有键
  Object.keys(obj).forEach((key) => {
    // 检查键中是否不包含"trigger"
    if (!key.includes('trigger')) {
      result[key] = obj[key];
    }
  });

  return result;
};

// 处理导入操作
export const useImportHandler = <T extends { title: string }>(
  setModalParams: any,
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  return useCallback(() => {
    setModalParams({
      title: '导入配置文件',
      tips: '导入后将对当前的配置进行全覆盖',
      type: 'import',
    });
    setIsModalOpen(true);
  }, [setModalParams, setIsModalOpen]);
};

// 处理文件上传
export const useUploadHandler = (
  id: string | null,
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>,
  refreshData?: () => Promise<void>,
) => {
  return useCallback(
    async (file: File) => {
      if (!id) {
        message.info('未找到当前转发配置');
        return;
      }
      const formData = new FormData();
      formData.append('file', file);
      formData.append('id', id);
      // 上传文件
      try {
        const uploadResponse = await httpPostFile('/api/configs/transmit/import', formData, {
          timeout: 5 * 60 * 1000, // 5分钟超时
        });
        if (uploadResponse.code === 0) {
          message.success('导入成功');
          // 导入成功后刷新数据
          if (refreshData) {
            await refreshData();
          }
        } else {
          throw new Error(uploadResponse.msg || '导入失败，请稍后重试');
        }
      } catch (error: any) {
        throw new Error(error?.message || '导入失败，请稍后重试');
      } finally {
        setIsModalOpen(false);
      }
    },
    [id, setIsModalOpen, refreshData],
  );
};

// 处理导出操作
export const useExportHandler = (
  setConfirmVisible: React.Dispatch<React.SetStateAction<boolean>>,
  setConfirmFuncParams: React.Dispatch<React.SetStateAction<any>>,
  id: string | null,
) => {
  return useCallback(() => {
    setConfirmVisible(true);
    setConfirmFuncParams({
      title: '导出',
      onConfirm: async () => {
        if (!id) {
          message.info('没有可以导出的内容');
          return;
        }
        return await asyncDownloadFile(`/api/configs/transmit/export?id=${id}`, {}, {}, true);
      },
    });
  }, [id, setConfirmVisible, setConfirmFuncParams]);
};

// 处理确认导出
export const handleConfirmExport = async (
  params: any,
  setConfirmVisible: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  setConfirmVisible(false);
  try {
    // get /api/configs/transmit/export
    await params.onConfirm?.();
  } catch (error) {
    console.error('导出失败:', error);
  }
};

// 处理取消确认
export const handleCancelConfirm = (
  setConfirmVisible: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  setConfirmVisible(false);
};

// 带参数返回上一页
export const useCancelWithParamsHandler = (id: string | null) => {
  const navigate = useNavigate();
  return useCallback(
    (idParam?: string) => {
      try {
        const targetId = idParam || id;
        if (!targetId) {
          navigate(-1);
          return;
        }

        // 1. 获取上一页的历史记录（浏览器历史栈中前一个地址）
        const historyState = window.history.state;
        const previousHistory = historyState?.back || historyState;

        const previousUrl = previousHistory?.url || document.referrer;

        if (!previousUrl) {
          // 如果获取不到上一页，直接返回
          navigate(-1);
          return;
        }

        // 2. 向上一页 URL 添加新参数
        const newParams = new URLSearchParams();
        newParams.append('id', targetId);

        // 解析上一页 URL，处理已有参数
        const urlObj = new URL(previousUrl, window.location.origin);

        // 合并参数
        newParams.forEach((value, key) => {
          urlObj.searchParams.set(key, value);
        });

        const updatedUrl = urlObj.pathname + urlObj.search;
        // 3. 替换历史记录中当前页的前一页
        navigate(location.pathname + location.search, { replace: true });
        window.history.replaceState(null, '', updatedUrl);

        // 4. 返回上一页
        navigate(-1);
      } catch (error) {
        console.error('返回上一页失败:', error);
        navigate(-1);
      }
    },
    [id, navigate],
  );
};

// 跳转到编辑页
export const useGoDetailHandler = (detail: any) => {
  const navigate = useNavigate();
  return useCallback(() => {
    if (!detail.id) {
      return;
    }
    if (detail?.isStartUp === 'true') {
      message.info('当前配置已启动，不允许编辑');
      return;
    }
    navigate(`/system-maintenance/data-forwarding/edit/?id=${detail.id}`);
  }, [detail, navigate]);
};

export const useGetDetail = (
  id: string | null,
  setDetail: React.Dispatch<React.SetStateAction<any>>,
) => {
  return useCallback(async () => {
    try {
      if (!id || id === '-1') {
        return;
      }
      const res = await httpGet(`/api/configs/transmit/details/${id}`);
      setDetail(res.data);
    } catch (error: any) {
      message.error('获取详情失败，请稍后重试');
    }
  }, [id, setDetail]);
};

// 将设备数据按类型整理
export const formatDeviceData = (data: any[]) => {
  const deviceData = (data || []).map((item) => ({
    ...item,
    title: item.Name,
    key: item.Type,
  }));
  return deviceData;
};

/**
 * 模拟进度更新
 * @param setProgressPercent 设置进度百分比的函数
 * @param setProgressVisible 设置进度条可见性的函数
 * @param setUpgradeInfo 设置升级信息的函数
 * @param setConfirmVisible 设置确认对话框可见性的函数
 * @param type 操作类型
 * @param uploadResponse 上传响应数据
 */
export const simulateProgressUpdate = (
  setProgressPercent: React.Dispatch<React.SetStateAction<number>>,
  setProgressVisible: React.Dispatch<React.SetStateAction<boolean>>,
  setUpgradeInfo: React.Dispatch<
    React.SetStateAction<{
      status: string;
      message: string;
      title: string;
      content: string;
    }>
  >,
  setConfirmVisible: React.Dispatch<React.SetStateAction<boolean>>,
  params: { type: string; uploadResponse?: any; title?: string; content?: string },
) => {
  const { type, uploadResponse, title, content } = params;
  // 模拟进度更新
  const interval = setInterval(() => {
    setProgressPercent((prev) => {
      if (prev >= 100) {
        clearInterval(interval);
        setTimeout(() => {
          setProgressVisible(false);
          setUpgradeInfo({
            status: 'success',
            message: uploadResponse?.data?.message || '',
            title:
              title ||
              (() => {
                if (type.includes('backup')) {
                  return '备份完成';
                } else if (type.includes('recovery')) {
                  return '恢复成功';
                } else {
                  return '升级成功';
                }
              })(),
            content: content || uploadResponse?.data?.message,
          });
          setConfirmVisible(true);
        }, 1000);
        return 100;
      }
      return Math.floor(prev + Math.random() * 15 + 5);
    });
  }, 500);
};
