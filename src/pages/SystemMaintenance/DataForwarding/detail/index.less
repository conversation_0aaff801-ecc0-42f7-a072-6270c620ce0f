.cst {
    .ant-select.ant-select-outlined.ant-select-single.param-select {
        background: transparent;
        // border-radius: 6px;
        color: #E3F8FF !important;
        height: 48px;
        font-size: 20px;
        line-height: 1em;
        width: 100%;
        text-align: left;

        .ant-select-selector {
            background: transparent !important;
            border: 1px solid #39ABFF !important;

        }
    }
}

// Switch 按钮样式
.param-switch {
    &.ant-switch {
        // 基础样式设置
        width: 90px;
        height: 36px;
        background-color: #1D4486;
        border: none;
        border-radius: 90px;
        padding: 0;
        transition: all 0.3s ease;

        &:hover:not(.ant-switch-disabled) {
            background-color: #1D4486;
        }

        // 选中状态
        &-checked {
            background-color: #2071FC;

            &:hover:not(.ant-switch-disabled) {
                background-color: #2071FC;
            }
        }

        // 滑块样式
        .ant-switch-handle {
            position: absolute;
            top: 3.5px;
            left: 4px;
            width: 28px;
            height: 28px;
            background-color: #FFFFFF;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;

            &::before {
                border-radius: 50%;
            }
        }

        // 选中状态下滑块位置
        &-checked .ant-switch-handle {
            transform: translateX(54px);
        }

        // 禁用状态
        &-disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #6596EA;
        }

        // 文本样式
        .ant-switch-inner {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 100%;
            padding: 0 22px;
            color: #FFFFFF;
            font-size: 14px;
            line-height: 36px;
        }
    }
}

// 设备类型树结构
.custom-device-tree {
    .device-icon {
        fill: #6596EA;
    }

    &.ant-tree {
        .ant-tree-treenode {
            padding-bottom: 16px;
            padding-top: 16px;
            width: 100%;
        }

        .ant-tree-switcher {
            // display: none;
        }

        .ant-tree-node-content-wrapper.ant-tree-node-selected,
        .ant-tree-treenode:not(.ant-tree-treenode-disabled) .ant-tree-node-content-wrapper:hover {
            color: #fff;
            background-color: transparent;

            .device-icon {
                fill: #fff;
            }
        }

        .ant-tree-treenode.ant-tree-treenode-switcher-open.ant-tree-treenode-selected,
        .ant-tree-treenode.ant-tree-treenode-switcher-close.ant-tree-treenode-selected {
            background-color: #0478ec;
        }

        .ant-tree-node-content-wrapper .ant-tree-iconEle {
            display: inline-flex;
            flex-direction: row;
            align-items: center;
            margin-right: 8px
        }
    }

}