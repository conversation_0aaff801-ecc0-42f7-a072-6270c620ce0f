import { RhKeyboard } from '@/components/RhKeyboard';
import { SectionTitle } from '@/pages/DeviceStatus/components/SectionTitle';
import { Button, Form, Input, message, Select, Switch } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import './index.less';
import { RhButton } from '@/components/ui/pagbtn';
import CronEditor from '@/components/CronEditor';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { httpGet, httpPost, httpPut } from '@/shared/http';
import { generateId, getCursorInfo, filterNonTriggerValues, useGetDetail } from './util';

interface InputFormItemProps {
  /** 表单字段名称 */
  name: string;
  /** 字段标签文本 */
  label: string;
  /** 字段当前值 */
  value?: any;
  /** 聚焦事件处理函数 */
  onFocus: (fieldName: string) => void;
  /** 输入框占位符文本 */
  placeholder?: string;
  /** 是否禁用输入框 */
  disabled?: boolean;
  inputType?: string;
}

const InputFormItem: React.FC<InputFormItemProps> = ({
  name,
  label,
  value,
  onFocus,
  placeholder = '请输入',
  disabled = false,
  inputType = 'text',
}) => {
  const InputCompent = inputType === 'pwd' ? Input.Password : Input;
  return (
    <div className="flex w-[calc(50%-20px)] items-center justify-end">
      <span className="label-text whitespace-nowrap text-left">{label}</span>
      <Form.Item name={name} noStyle rules={[{ required: true, message: `请输入${label}` }]}>
        <InputCompent
          className="param-input"
          onFocus={() => onFocus(name)}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          style={{ width: '330px', maxWidth: '330px', padding: '10px 12px' }}
        />
      </Form.Item>
    </div>
  );
};

export const DataForwardingDetailEdit = () => {
  const navigate = useNavigate();

  // 获取地址栏中的id参数 -1表示创建
  const id = new URLSearchParams(window.location.search).get('id');
  const [form] = Form.useForm();
  const [keyboardType, setKeyboardType] = useState<string>('chinese');

  // 使用从util导入的generateId函数
  const triggerModesDefault = [
    {
      id: generateId(),
      triggerName: '',
      triggerValue: '',
    },
  ];

  const [triggerModes, setTriggerModes] = useState<any[]>(triggerModesDefault);
  // 键盘相关状态
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [currentEditField, setCurrentEditField] = useState<string>('');
  const [keyboardPosition, setKeyboardPosition] = useState<'top' | 'bottom'>('bottom');
  const keyboardRef = useRef<any>(null);
  const [detail, setDetail] = useState<any>({});
  const [appletModelOptions, setAppletModelOptions] = useState<any[]>([]);
  const [platformParams, setPlatformParams] = useState<any>({});
  const [currentAppletModel, setCurrentAppletModel] = useState<string>('');
  const [triggerMap, setTriggerMap] = useState<any>({});
  // 断点续传开关状态，用于立即响应UI变化
  const [uploadHistoryDataEnabled, setUploadHistoryDataEnabled] = useState(true);

  // 处理输入框获取焦点事件
  const handleInputFocus = useCallback(
    (fieldName: string, Keyboard: boolean = true) => {
      // 设置当前编辑字段
      setCurrentEditField(fieldName);

      // 动态计算键盘位置：检查字段是否在屏幕下半部分
      const cursorInfo = getCursorInfo();
      if (cursorInfo && cursorInfo.top > 380) {
        setKeyboardPosition('top');
      } else {
        setKeyboardPosition('bottom');
      }
      // 显示键盘
      setShowKeyboard(Keyboard);
      setTimeout(() => {
        // 设置键盘输入的初始值
        if (keyboardRef.current) {
          const currentValue = form.getFieldValue(fieldName)?.toString() || '';
          keyboardRef.current.setInput(currentValue);
        }
      }, 0);
    },
    [form],
  );

  const handleKeyboardInput = useCallback(
    (input: string) => {
      if (currentEditField) {
        // 处理触发模式的特殊逻辑 - 按照name={[name, index, 'triggerName']}格式解析
        if (currentEditField.includes('triggerMode')) {
          // 从格式如 "triggerMode.0.triggerName" 中解析出各部分
          const parts = currentEditField.split('.');
          if (parts.length >= 3) {
            const name = parts[0]; // "triggerMode"
            const index = parts[1]; // id
            const fieldName = parts[2]; // 字段名，如 "triggerName"

            const currentTriggerMode = form.getFieldValue(name) || [];

            // 创建深拷贝以避免直接修改状态
            const updatedTriggerMode = JSON.parse(JSON.stringify(currentTriggerMode));

            // 确保索引有效
            if (!updatedTriggerMode[index]) {
              // 如果索引不存在，使用triggerModes中的对应项作为基础
              const modeItem = triggerModes.find((item) => item.id === index);
              if (modeItem) {
                updatedTriggerMode[index] = JSON.parse(JSON.stringify(modeItem));
              } else {
                // 创建新的空对象
                updatedTriggerMode[index] = { id: index, triggerName: '', triggerValue: '' };
              }
            }
            let processedInput = input;
            // 触发模式字段特殊处理：长度限制在16个字以内
            if (input.length > 16) {
              processedInput = input.slice(0, 16);
              message.info('长度不能超过16个字符');
            }
            // 更新特定字段值
            updatedTriggerMode[index] = {
              ...updatedTriggerMode[index],
              [fieldName]: processedInput,
            };

            // 更新整个triggerMode数组 - 确保是数组格式
            form.setFieldsValue({ [name]: { ...updatedTriggerMode } });
            // 同步更新triggerModes状态
            setTriggerModes((prev) => {
              const newModes = [...prev];
              const targetIndex = newModes.findIndex((item) => item.id === index);
              if (targetIndex !== -1) {
                newModes[targetIndex] = {
                  ...newModes[targetIndex],
                  [fieldName]: processedInput,
                };
              }
              return newModes;
            });
          }
        } else {
          // 非触发模式的普通字段更新
          let processedInput = input;

          if (input.length > 16) {
            processedInput = input.slice(0, 16);
            message.info('长度不能超过16个字符');
          }

          form.setFieldsValue({ [currentEditField]: processedInput });
        }

        // 强制表单项重新渲染，确保立即更新显示
        form.validateFields().then(
          () => {},
          () => {},
        );
      }
    },
    [currentEditField, form, triggerModes],
  );

  const formatterTrigger = (triggerModes: any[], formData: any, needId: boolean = false) => {
    const result = triggerModes.map((item: any) => {
      const newItem: any = {
        ...(triggerMap[formData.triggerMode?.[item.id].triggerName] || {}),
        eventMode: 'CronEvent',
        eventName: formData.triggerMode?.[item.id].triggerName,
        eventTimeInterval: formData?.[item.id].triggerValue,
      };
      if (needId) {
        newItem.appletId = id;
      }
      return newItem;
    });
    return result;
  };
  const formatterParamsList = useCallback(
    (formData: any) => {
      const paramListInfo = platformParams[currentAppletModel];
      return (paramListInfo || []).map((item: any) => {
        const newItem = {
          cnName: item.label,
          paraItemName: item.key,
          paraItemValue: formData?.[item.key] || item.value,
        };
        return newItem;
      });
    },
    [platformParams, currentAppletModel],
  );

  const handleConfirm = useCallback(() => {
    form.validateFields().then(async (values) => {
      try {
        if (!id) return;
        let res: any = null;
        // 平台相关参数要放在 paramList 中
        const paramsList = formatterParamsList(values);
        let params: { paramList: any[]; eventList: any[]; id?: string | null; QOS: number } = {
          // 筛选出values中key值不含trigger的项
          ...filterNonTriggerValues(values),
          QOS: Number(values.QOS),
          paramList: paramsList,
          eventList: [],
        };
        // if(!params.uploadHistoryData){
        //   params.uploadHistoryData = false;
        // }
        if (id === '-1') {
          params.eventList = formatterTrigger(triggerModes, values);
          // 新增
          res = await httpPost('/api/configs/transmit/add', params);
          if (res.code !== 0) {
            message.error(res.message || '');
            return;
          }
        } else {
          // 编辑
          params = {
            ...params,
            eventList: formatterTrigger(triggerModes, values, true),
            id: id,
          };
          res = await httpPut('/api/configs/transmit/edit', params);
          if (res.code !== 0) {
            message.error(res.message || '');
            return;
          }
        }
        // 保存成功跳转进入新页面
        //   进入导入导出详情页
        let currentId = id;
        if (id === '-1') {
          currentId = res.data;
        }
        navigate(`/system-maintenance/data-forwarding/detail?id=${currentId}`, { replace: true });
      } catch (error: any) {
        message.error(error.message);
      }
    });
  }, [form, id, triggerModes]);

  // 添加新的触发方式行
  const addTriggerMode = () => {
    const newItem: any = {
      id: generateId(),
      triggerName: '',
      triggerValue: '',
    };
    setTriggerModes((prev) => {
      // 创建深拷贝的新数组
      const updatedValues = JSON.parse(JSON.stringify(prev));
      // 添加新项到数组
      updatedValues.push(newItem);
      return updatedValues;
    });
  };

  // 删除指定的触发方式行
  const removeTriggerMode = (index: number) => {
    setTriggerModes((prev) => {
      // 创建深拷贝的新数组
      const updatedValues = JSON.parse(JSON.stringify(prev));
      // 删除指定索引的项
      updatedValues.splice(index, 1);
      return updatedValues;
    });
  };

  const handleCancel = useCallback(() => {
    setCurrentEditField('');
    setShowKeyboard(false);
    setKeyboardPosition('bottom');
    navigate(-1);
  }, [navigate]);

  // 获取全部可选目标平台
  const getAppletModelOptions = async () => {
    try {
      const res = await httpGet('/api/configs/transmit/platforms');
      if (res.code === 0) {
        const platformParams: any = {};
        const platformList = res.data?.map((item: any) => {
          platformParams[item.key] = item.options.map((item: any) => ({
            label: item.cnName,
            key: item.paraItemName,
            value: item.paraItemValue,
            inputType: item.inputType,
          }));
          return {
            label: item.text,
            value: item.key,
          };
        });

        setPlatformParams(platformParams);
        setAppletModelOptions(platformList);
      }
    } catch (error: any) {
      //   message.error(error.message);
    }
  };
  const getDetail = useGetDetail(id, setDetail);

  useEffect(() => {
    //   根据Detail初始化页面 - 只有在编辑模式下(id!=='-1')才使用detail初始化
    if (!detail || !form || id === '-1') {
      return;
    }
    const formTriggerValues: any = {};
    const triggerModes = (detail.eventList || triggerModesDefault)?.map((item: any) => {
      const newItem = {
        id: generateId(),
        triggerName: item.eventName,
        triggerValue: item.eventTimeInterval,
      };
      formTriggerValues[newItem.id] = {
        ...newItem,
      };

      return newItem;
    });
    const _triggerMap: any = {};
    (detail.eventList || []).forEach((item: any) => {
      if (item.pushMsgContent) {
        // 删除pushMsgContent数组中每个对象中的itemList
        item.pushMsgContent.forEach((item: any) => {
          item.itemList = item.itemList?.map((item: any) => item.split('|')?.[0]);
        });
      }
      _triggerMap[item.eventName] = item;
    });
    setTriggerMap(_triggerMap);
    const paramsMap: any = {};
    detail.paramList?.forEach((item: any) => {
      paramsMap[item.paraItemName] = item.paraItemValue;
    });
    const formatterData = {
      ...detail,
      triggerMode: {
        ...formTriggerValues,
      },
      ...formTriggerValues,
      ...paramsMap,
      QOS: detail.QOS?.toString() || '0',
    };
    setTriggerModes(triggerModes);
    setCurrentAppletModel(formatterData.appletModel);
    // 初始化时同步设置断点续传开关状态
    setUploadHistoryDataEnabled(
      detail.uploadHistoryData !== undefined ? detail.uploadHistoryData : true,
    );
    form.setFieldsValue(formatterData);
  }, [detail, form, id]);

  useEffect(() => {
    //  获取全部可选目标平台
    getAppletModelOptions();
    // 只有在编辑模式下才获取详情
    if (id !== '-1') {
      getDetail();
    } else {
      // 创建模式下设置默认值
      form.setFieldsValue({
        topic: 'v4/p/post/thing/live/json/1.1',
        historyTopic: 'v4/p/post/thing/history/json/1.1'
      });
    }
  }, [id]);

  useEffect(() => {
    const res = platformParams[currentAppletModel];
    if (res) {
      // 构建新的数据对象
      const newData = {
        ...form.getFieldsValue(),
      };

      // 如果是创建模式(id===-1)或者切换了平台，使用平台默认参数
      if (id === '-1') {
        // 创建模式下，完全使用平台默认参数
        res.forEach((item: { key: string | number; value: any }) => {
          newData[item.key] = item.value;
        });
      } else {
        // 编辑模式下，只设置未定义的参数
        res.forEach((item: { key: string | number; value: any }) => {
          if (newData[item.key] === undefined) {
            newData[item.key] = item.value;
          }
        });
      }

      form.setFieldsValue(newData);
    }
  }, [platformParams, form, currentAppletModel, id]);

  const handleChangeAppletModel = (value: any) => {
    if (!value) {
      form.setFieldValue('appletModel', '');
      platformParams[currentAppletModel].forEach((item: { key: string | number; value: any }) => {
        form.setFieldValue(item.key, '');
      });
    } else {
      platformParams[value].forEach((item: { key: string | number; value: any }) => {
        form.setFieldValue(item.key, item.value);
      });
    }
    setCurrentAppletModel(value);
  };
  return (
    <div className="relative h-full w-full">
      <Form
        form={form}
        layout="horizontal"
        className={`cst param-config-section max-h-[calc(100%-70px)] w-full overflow-auto pb-8`}
      >
        <SectionTitle title="基本信息" />
        <div className="flex flex-row flex-wrap gap-[20px]">
          <div className="flex w-[calc(50%-20px)] items-center justify-end">
            <span className="label-text whitespace-nowrap text-left">连接名称</span>
            <Form.Item
              name={'appletName'}
              noStyle
              rules={[{ required: true, message: '请输入连接名称' }]}
            >
              <Input
                className="param-input"
                type="text" // 移动端不使用原生number类型，统一使用text类型配合自定义键盘
                onFocus={() => handleInputFocus('appletName')}
                value={form.getFieldValue('appletName')}
                placeholder={`请输入`}
                style={{ width: '330px', maxWidth: '330px' }}
              />
            </Form.Item>
          </div>
          <div className="flex w-[calc(50%-20px)] items-center justify-end">
            <span className="label-text whitespace-nowrap text-left">目标平台</span>
            <Form.Item
              name={'appletModel'}
              noStyle
              rules={[{ required: true, message: '请选择目标平台' }]}
            >
              <Select
                allowClear
                style={{ width: 330, height: 48 }}
                options={appletModelOptions}
                className="param-select"
                placeholder="请选择"
                onChange={handleChangeAppletModel}
              />
            </Form.Item>
          </div>
          <div className="flex w-[calc(50%-20px)] items-center justify-end">
            <span className="label-text whitespace-nowrap text-left">QOS</span>
            <Form.Item name={'QOS'} noStyle rules={[{ required: true, message: '请选择QOS' }]}>
              <Select
                allowClear
                style={{ width: 330, height: 48 }}
                options={[
                  { value: '0', label: 0 },
                  { value: '1', label: 1 },
                  { value: '2', label: 2 },
                ]}
                placeholder="请选择"
                className="param-select"
              />
            </Form.Item>
          </div>
        </div>
        <>
          <SectionTitle title="实时数据配置" />
          <div className="flex flex-col gap-[20px]">
            <InputFormItem
              name="topic"
              label="实时数据主题"
              onFocus={handleInputFocus}
            />
            <div className="flex flex-row items-baseline justify-end">
              <span className="label-text whitespace-nowrap text-left">触发方式</span>
              {/* <Form.Item noStyle> */}
              <div className="flex flex-col gap-[20px]">
                {triggerModes.map((item, index) => (
                  <div key={item.id} className="flex items-center gap-[116px]">
                    <div className="flex items-center gap-[20px]">
                      <div className="flex w-[calc(50%-20px)] items-center justify-end">
                        <Form.Item
                          name={['triggerMode', item.id, 'triggerName']}
                          noStyle
                          rules={[{ required: true, message: '请输入触发名称' }]}
                        >
                          <Input
                            className="param-input"
                            type="text"
                            value={item.triggerName}
                            onFocus={() => handleInputFocus(`triggerMode.${item.id}.triggerName`)}
                            placeholder="请输入触发名称"
                            style={{ width: '330px', maxWidth: '330px' }}
                          />
                        </Form.Item>
                      </div>
                      <div className="flex items-center gap-[10px]">
                        <Form.Item
                          name={[item.id, 'triggerValue']}
                          initialValue={item.triggerValue || '0 0/30 * * * ?'}
                          noStyle
                          rules={[{ required: true, message: '请输入触发表达式' }]}
                        >
                          <CronEditor
                            /* value={item.triggerValue}
                              onFocus={() =>
                                handleInputFocus(`triggerMode.${item.id}.triggerValue`, false)
                              }
                              onChange={(value) => {
                                // 更新triggerModes状态
                                const updatedModes = [...triggerModes];
                                updatedModes[index] = {
                                  ...updatedModes[index],
                                  triggerValue: value,
                                };
                                setTriggerModes(updatedModes);
                              }} */
                            disabled={false}
                          />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="mr-[16px]">
                      {index > 0 && (
                        <Button
                          type="text"
                          icon={<MinusOutlined style={{ color: '#39ABFF' }} />}
                          onClick={() => removeTriggerMode(index)}
                          size="small"
                          style={{
                            border: '1px solid #579CDB',
                            borderRadius: '50%',
                            width: '46px',
                            height: '46px',
                          }}
                        />
                      )}
                      {index === 0 && (
                        <Button
                          type="text"
                          icon={<PlusOutlined style={{ color: '#579CDB' }} />}
                          onClick={addTriggerMode}
                          size="small"
                          style={{
                            border: '1px solid #579CDB',
                            borderRadius: '50%',
                            width: '46px',
                            height: '46px',
                          }}
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
              {/* </Form.Item> */}
            </div>
            <div className="flex flex-row flex-wrap gap-[20px]">
              {/* 根据表单中appletModel的值更新动态渲染平台参数 */}
              {(platformParams[currentAppletModel] || [])?.map((item: any) => {
                return (
                  <InputFormItem
                    inputType={item.inputType}
                    name={item.key}
                    label={item.label}
                    // value={form.getFieldValue(item.key) || item.value}
                    onFocus={handleInputFocus}
                  />
                );
              })}
            </div>
          </div>
        </>

        <SectionTitle title="历史数据配置" />
        <div className="flex flex-col gap-[20px]">
          <div className="ml-[70px] flex w-[calc(50%-20px)] items-center">
            <span className="label-text whitespace-nowrap text-left">断点续传</span>
            <Form.Item
              name={'uploadHistoryData'}
              noStyle
              valuePropName="checked"
              initialValue={true}
            >
              <Switch
                className="param-switch"
                onChange={(checked) => {
                  // 更新状态以立即反映在UI上
                  setUploadHistoryDataEnabled(checked);
                  // 当开关关闭时，清空离线数据主题和缓存时长
                  if (!checked) {
                    form.setFieldsValue({
                      historyTopic: '',
                      historyDataSavedDays: '',
                    });
                  }
                }}
              />
            </Form.Item>
          </div>

          {/* 仅当断点续传开启时显示离线数据配置 */}
          {uploadHistoryDataEnabled && (
            <div className="flex flex-row flex-wrap gap-[20px]">
              {/* 离线数据主题 */}
              <InputFormItem
                name="historyTopic"
                label="离线数据主题"
                value={form.getFieldValue('historyTopic')}
                onFocus={handleInputFocus}
              />
              {/* 缓存时长（天） */}
              <InputFormItem
                name="historyDataSavedDays"
                label="缓存时长（天）"
                value={form.getFieldValue('historyDataSavedDays')}
                onFocus={handleInputFocus}
              />
            </div>
          )}
        </div>
      </Form>
      {/* 确认按钮 */}
      <div className="absolute bottom-[56px] w-full">
        <div className="h-[1px] bg-[linear-gradient(90deg,rgba(57,171,255,0)_0%,#2A81C1_51.05%,rgba(57,171,255,0)_100%)]"></div>
        <div className="absolute flex w-full flex-row justify-center gap-[15px] bg-[#031c5a] pt-4">
          <RhButton type="primary" style={{ width: 200 }} onClick={handleConfirm}>
            确认
          </RhButton>
          <RhButton
            ghost
            text={'返回上级'}
            style={{ width: 200 }}
            onClick={handleCancel}
          ></RhButton>
        </div>
      </div>
      <RhKeyboard
        init={(r: any) => (keyboardRef.current = r)}
        show={showKeyboard}
        position={keyboardPosition}
        onClose={() => {
          setShowKeyboard(false);
        }}
        onChange={handleKeyboardInput}
        layoutType={keyboardType}
      />
    </div>
  );
};
