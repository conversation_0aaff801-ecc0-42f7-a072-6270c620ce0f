import { RhButton } from '@/components/ui/pagbtn';
import { SectionTitle } from '@/pages/DeviceStatus/components/SectionTitle';
import { EditOutlined } from '@ant-design/icons';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { DataImportPanel } from './DataImportPanel';
import {
  useStartStop,
  useImportHandler,
  useUploadHandler,
  useExportHandler,
  handleConfirmExport,
  handleCancelConfirm,
  useGoDetailHandler,
  useGetDetail,
} from './util';
import UploadModal from '../../../../components/UploadModal/uploadModal';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import { useNavigate } from 'react-router-dom';

// 数据转发详情页
export const DataForwardingDetail = () => {
  const id = new URLSearchParams(window.location.search).get('id');
  const [detail, setDetail] = useState<any>(null);
  const navigate = useNavigate();

  // 二次确认状态
  const [confirmVisible, setConfirmVisible] = useState<boolean>(false);
  const [confirmFuncParams, setConfirmFuncParams] = useState<any>(null);

  // 导入相关状态
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalParams, setModalParams] = useState({
    title: '导入',
  });
  const [optLoading, setOptLoading] = useState<boolean>(false);

  const items = useMemo(() => {
    if (!detail) {
      return [];
    }
    const formTriggerValues: any = {};
    const triggerType =
      (detail.eventList || [])
        ?.map((item: any) => {
          return item.eventName;
        })
        ?.join('、') || '';
    const paramsMap: any = {};
    detail.paramList?.forEach((item: any) => {
      paramsMap[item.paraItemName] = item.paraItemValue;
    });
    const formatterData = {
      ...detail,
      triggerType: triggerType,
      ...formTriggerValues,
      ...paramsMap,
    };

    const infoList = [
      { label: '连接名称', key: 'appletName' },
      { label: '目标平台', key: 'appletModelText' },
      { label: 'QOS', key: 'qos' },
      // { label: '上报方式', key: 'reportType' },
      { label: '实时数据主题', key: 'topic' },
      { label: '触发方式', key: 'triggerType' },
      // 平台相关参数动态显示
      /* { label: 'IP地址', key: 'ipAddress' },
    { label: '端口', key: 'port' },
    { label: '用户名', key: 'username' },
    { label: '密码', key: 'password' },
    { label: '客户端IDID', key: 'clientId' }, */
      ...detail.paramList?.map((item: any) => {
        return {
          label: item.cnName,
          key: item.paraItemName,
        };
      }),
      { label: '断点续传', key: 'uploadHistoryData' },
      { label: '离线数据主题', key: 'historyTopic' },
      { label: '缓存时长(天)', key: 'historyDataSavedDays' },
    ];
    return infoList.map((item) => {
      return {
        ...item,
        value: formatterData[item.key],
      };
    });
  }, [detail]);

  // 使用公共方法
  const goDetial = useGoDetailHandler(detail);
  const handleImport = useImportHandler<{ title: string }>(setModalParams, setIsModalOpen);
  const handleExport = useExportHandler(setConfirmVisible, setConfirmFuncParams, id);

  const getDetail = useGetDetail(id, setDetail);
  const handleStart = useStartStop(
    id,
    detail?.isStartUp === 'true' ? false : true,
    setOptLoading,
    getDetail,
  );
  const handleUpload = useUploadHandler(id, setIsModalOpen, getDetail);
  
  const handleCancel = useCallback(() => {
    navigate('/system-maintenance/data-forwarding');
  }, [navigate]);

  useEffect(() => {
    getDetail();
  }, [getDetail]);

  return (
    <div className="relative h-full w-full">
      <div
        className={
          'absolute left-0 right-0 top-0 z-10 flex justify-between border-b-[1px] border-[#2A81C1] bg-[#031c5a] pb-5'
        }
      >
        <RhButton ghost text={'返回上级'} style={{ width: 110 }} onClick={handleCancel}></RhButton>
        <div className={'flex flex-row gap-[20px]'}>
          <RhButton
            ghost
            text={detail?.isStartUp === 'true' ? '停止' : '启动'}
            style={{ width: 110 }}
            onClick={handleStart}
            loading={optLoading}
          ></RhButton>
        </div>
      </div>
      <div className="h-full overflow-auto">
        <div className="flex pt-[68px]">
          <SectionTitle title="基本信息" />
          {/* 扩大编辑图标热点区域 */}
          <div
            className={`ml-1 flex items-center text-[#0478EC] ${detail?.isStartUp === 'true' ? 'cursor-not-allowed opacity-[0.5]' : 'cursor-pointer'}`}
            onClick={goDetial}
          >
            <EditOutlined style={{ width: 24, height: 24, fontSize: 24 }} />
          </div>
        </div>
        <div className="mt-4 grid grid-cols-3 gap-[10px]">
          {items.map((item) => (
            <div key={item.key} className="flex">
              <span
                className={`max-w-[130px] whitespace-nowrap text-left text-[20px] text-[#A7BEEA]`}
              >
                {item.label}：
              </span>
              <span className="w-[calc(100%-130px)] break-words text-[20px] text-[#E3F8FF]">
                {item.key === 'uploadHistoryData' ? (item.value ? '开启' : '关闭') : item.value}
              </span>
            </div>
          ))}
        </div>
        <div className={'mt-10'}>
          <SectionTitle title="转发设备" />
          <div className={'mb-5 flex justify-end'}>
            <div className={'flex flex-row gap-[20px]'}>
              <RhButton
                ghost
                text={'导入'}
                style={{ width: 110 }}
                disabled={detail?.isStartUp === 'true'}
                onClick={handleImport}
              ></RhButton>
              <RhButton
                ghost
                text={'导出'}
                style={{ width: 110 }}
                onClick={handleExport}
              ></RhButton>
            </div>
          </div>
          {/* 导入数据展示面板 */}
          <DataImportPanel detail={detail} />
        </div>
      </div>
      <UploadModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onUpload={handleUpload}
        accept=".xlsx"
        fileSize={10}
        {...modalParams}
      />
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => handleCancelConfirm(setConfirmVisible)}
        onConfirm={() => handleConfirmExport(confirmFuncParams, setConfirmVisible)}
        content={<div className="leading-7">确认导出配置吗？</div>}
      />
    </div>
  );
};

export default DataForwardingDetail;
