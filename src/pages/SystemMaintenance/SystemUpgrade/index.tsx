import React, { useEffect, useRef, useState } from 'react';
import './SystemUpgrade.css';
import { Button, Input } from 'antd';
import { RhKeyboard } from '@/components/RhKeyboard';
import { useNavigate } from 'react-router-dom';
import { loadStateWithExpiry } from './hooks/storage';

interface SystemUpgradeProps {
  onUpgrade?: (password: string) => void;
  loading?: boolean;
}

export const SystemUpgrade: React.FC<SystemUpgradeProps> = ({ loading = false }) => {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const keyboardRef = useRef<any>(null);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const navigate = useNavigate();

  const handleKeyboardInput = (input: string) => {
    setError('');
    setPassword(input);
  };
  const handleUpgrade = () => {
    if (!password.trim()) {
      setError('请输入升级口令');
      return;
    }
    setError('');
    navigate('/system-maintenance/system-upgrade/detail');
  };

  const handleInputFocus = () => {
    setShowKeyboard(true);
  };

  useEffect(() => {
    const data = loadStateWithExpiry('upgradedetail');
    if (data) {
      navigate('/system-maintenance/system-upgrade/detail');
    }
  }, []);
  return (
    <div className="system-upgrade-container">
      <div className="upgrade-content">
        <div className="input-section">
          <Input
            // type="password"
            className="upgrade-input"
            placeholder="请输入升级口令"
            value={password}
            onFocus={handleInputFocus}
            disabled={loading}
          />
          {error && <div className="error-message">{error}</div>}
        </div>

        <Button className="upgrade-button" onClick={handleUpgrade} disabled={loading}>
          {loading ? '升级中...' : '确认'}
        </Button>
      </div>
      <RhKeyboard
        init={(r: any) => (keyboardRef.current = r)}
        show={showKeyboard}
        onClose={() => setShowKeyboard(false)}
        onChange={handleKeyboardInput}
        layoutType={'chinese'}
      />
    </div>
  );
};
