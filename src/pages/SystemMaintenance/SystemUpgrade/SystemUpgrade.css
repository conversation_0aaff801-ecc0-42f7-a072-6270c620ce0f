.system-upgrade-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.upgrade-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: stretch;
  width: 350px;
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upgrade-input {
  width: 100%;
  padding: 16px 12px;
  background-color: transparent !important;
  border: 1px solid #39abff;
  border-radius: 6px;
  color: #76aad9;
  font-size: 20px;
  font-weight: 400;
  outline: none;
  transition: all 0.3s ease;
}

.upgrade-input::placeholder {
  color: #76aad9;
  opacity: 0.8;
}

.upgrade-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  color: #ff4757;
  font-size: 14px;
  /* text-align: center; */
  margin-top: 4px;
  animation: shake 0.3s ease-in-out;
}

.upgrade-button {
  width: 100%;
  height: 56px;
  border: 1px solid #39abff;
  border-radius: 6px;
  color: #6de875 !important;
  font-size: 22px;
  background: linear-gradient(
    180deg,
    rgba(0, 135, 255, 0.65) -3.41%,
    rgba(0, 135, 255, 0.08) 96.68%
  ) !important;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}
