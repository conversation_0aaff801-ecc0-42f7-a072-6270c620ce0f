import React, { useEffect } from 'react';
import { Modal } from 'antd';

interface UpgradeProgressModalProps {
  visible: boolean;
  progressPercent: number;
  onClose?: () => void;
  title?: string;
}

const UpgradeProgressModal: React.FC<UpgradeProgressModalProps> = ({
  visible,
  progressPercent,
  onClose,
  title,
}) => {

  return (
    <Modal
      open={visible}
      footer={null}
      closable={false}
      centered
      width={400}
      className="upgrade-progress-modal"
      styles={{
        mask: { backgroundColor: 'rgba(0, 0, 0, 0.45)' },
        content: {
          backgroundColor: '#143F8C',
          borderRadius: '8px',
          boxShadow: '0px 2px 8px 0px rgba(33, 37, 46, 0.15)',
          padding: '32px 24px',
        },
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '32px',
          alignItems: 'center',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
          }}
        >
          <span
            style={{
              color: '#E3F8FF',
              fontSize: '22px',
              fontFamily: 'PingFang SC',
              fontWeight: 400,
            }}
          >
            {title}
          </span>
        </div>

        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px',
            width: '100%',
          }}
        >
          <div
            style={{
              position: 'relative',
              width: '310px',
              height: '8px',
              backgroundColor: '#1D4CA6',
              borderRadius: '4px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                height: '100%',
                width: `${progressPercent}%`,
                backgroundColor: '#6DE875',
                transition: 'width 0.3s ease',
                borderRadius: '4px',
              }}
            />
          </div>

          <span
            style={{
              color: '#6797D6',
              fontSize: '16px',
              fontFamily: 'PingFang SC',
              fontWeight: 400,
            }}
          >
            {progressPercent}%
          </span>
        </div>
      </div>
    </Modal>
  );
};

export default UpgradeProgressModal;