import { RhButton } from '@/components/ui/pagbtn';
import { SectionTitle } from '@/pages/DeviceStatus/components/SectionTitle';
import { Upload, Button, message } from 'antd';
import React, { useCallback, useState } from 'react';
import UploadModal, { UploadModalProps } from '../../../../components/UploadModal/uploadModal';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import UpgradeProgressModal from './components/UpgradeProgressModal';
import { useDeviceStore } from '@/store/deviceStore';
import { httpGet, httpPost, httpPostFile } from '@/shared/http';
import { asyncDownloadFile } from '@/utils/download';

const EMSUpload = () => {
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  //   上传弹窗
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalParams, setModalParams] = useState<UploadModalProps>({
    tips: '',
    title: '升级',
    accept: '',
    type: '',
  });
  const [confirmVisible, setConfirmVisible] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmContent, setConfirmContent] = useState('');
  const [confirmFuncParams, setConfirmFuncParams] = useState<any>(null);
  //   进度条弹窗\进度比例
  const [progressVisible, setProgressVisible] = useState<boolean>(false);
  const [progressPercent, setProgressPercent] = useState<number>(0);
  const [progressTitle, setProgressTitle] = useState<string>('');
  //   完成信息
  const [upgradeInfo, setUpgradeInfo] = useState<any>({});
  // 系统状态为关机时才允许升级和恢复 systemStatus==1
  const { systemStatus } = useDeviceStore();

  // 确认文件上传
  const handleUpload = useCallback(
    async (file: File, type: string) => {
      if (systemStatus !== 1) {
        message.warning('系统状态为关机时才允许升级和恢复');
        return;
      }
      try {
        setIsModalOpen(false);
        // 系统升级中
        const title = (() => {
          if (type.includes('backup')) {
            return '文件备份中';
          } else if (type.includes('recovery')) {
            return '文件恢复中';
          } else {
            return '系统升级中';
          }
        })();
        setProgressTitle(title);

        await startSimulation(type, file);
      } catch (error: any) {
        setUpgradeInfo({
          status: 'failed',
          title: (() => {
            if (type.includes('backup')) {
              return '备份失败';
            } else if (type.includes('recovery')) {
              return '恢复失败';
            } else {
              return '升级失败';
            }
          })(),
          message: ` ${error.message || '未知错误'}`,
        });
        setConfirmVisible(true);
        setConfirmContent('');
        setProgressVisible(false);
      }
    },
    [systemStatus],
  );

  const handleModalset = (params: UploadModalProps, type: string) => {
    setModalParams({
      ...params,
      type,
    });
    setIsModalOpen(true);
  };

  const handleBackupEMS = async () => {
    // 使用asyncDownloadFile函数下载文件
    await asyncDownloadFile(
      '/api/system/backup/profiles',
      {},
      {
        defaultFilename: 'EMS_backup.zip',
        defaultType: 'application/zip',
      },
      true,
    );
  };

  const handleBackupJS = async () => {
    // 使用asyncDownloadFile函数下载文件
    await asyncDownloadFile(
      '/api/system/backup/nodered',
      {},
      {
        defaultFilename: 'JS_backup.zip',
        defaultType: 'application/zip',
      },
      true,
    );
  };
  //   模拟进度条
  const startSimulation = async (type: string, uploadedFile?: File) => {
    setProgressVisible(true);
    setProgressPercent(0);

    let uploadResponse: any;
    if (type === 'backupEMS') {
      // 下载工程文件
      await handleBackupEMS();
    }
    if (type === 'backupJS') {
      // 下载流编排文件
      await handleBackupJS();
    }
    if (type === 'upgradeEMS' || type === 'recoveryJS') {
      if (!uploadedFile) {
        throw new Error('请上传文件');
      }
    }
    if (type === 'recoveryEMS') {
      const formData = new FormData();
      // @ts-ignore
      formData.append('file', uploadedFile);
      formData.append('RemoteFileName', '');
      // 上传文件
      uploadResponse = await httpPostFile('/api/system/restore/profiles', formData, {
        timeout: 5 * 60 * 1000, // 5分钟超时
      });
    }
    if (type === 'recoveryJS') {
      const formData = new FormData();
      // @ts-ignore
      formData.append('file', uploadedFile);
      formData.append('RemoteFileName', '');

      // 上传文件
      uploadResponse = await httpPostFile('/api/system/restore/nodered', formData, {
        timeout: 5 * 60 * 1000, // 5分钟超时
      });
    }

    if (!uploadResponse || uploadResponse?.code === 0) {
      const needTime = type === 'recoveryEMS' ? 5 * 60 * 1000 : 5000;
      // 模拟进度更新
      const totalSteps = type === 'recoveryEMS' ? 60 : 15; // 总步数
      const stepTime = Math.floor(needTime / totalSteps); // 每步所需时间
      let steps = 0;

      const interval = setInterval(() => {
        steps++;
        setProgressPercent((prev) => {
          if (steps >= totalSteps || prev >= 100) {
            clearInterval(interval);
            setTimeout(() => {
              setProgressVisible(false);
              setUpgradeInfo({
                status: 'success',
                message: uploadResponse?.data?.message || '',
                title: (() => {
                  if (type.includes('backup')) {
                    return '备份完成';
                  } else if (type.includes('recovery')) {
                    return '恢复成功';
                  } else {
                    return '升级成功';
                  }
                })(),
                content: uploadResponse?.data?.message,
              });
              setConfirmVisible(true);
            }, 1000);
            return 100;
          }
          // 根据当前步数计算进度百分比
          const targetPercent = Math.min((steps / totalSteps) * 100, 95);
          // 确保每步都有进度增长
          return Math.min(
            parseFloat(targetPercent.toFixed(2)),
            parseFloat((prev + Math.random() * 15 + 1).toFixed(2)),
          );
        });
      }, stepTime);
    } else {
      throw new Error(uploadResponse.message || '失败');
    }
  };

  const handleCancel = () => {
    setConfirmVisible(false);
    //   关闭时清空信息
    setUpgradeInfo({});
    setConfirmContent('');
  };

  // 操作确认
  const handleConfirm = async (params: any) => {
    if (!!upgradeInfo.status) {
      //进度条完成后的提示弹窗
      handleCancel();
      return;
    }
    setConfirmLoading(true);
    try {
      setProgressTitle('备份中');
      setConfirmVisible(false);
      await startSimulation(params.type);
    } catch (error: any) {
      setUpgradeInfo({
        status: 'failed',
        title: (() => {
          if (params.type.includes('backup')) {
            return '备份失败';
          } else if (params.type.includes('recovery')) {
            return '恢复失败';
          } else {
            return '升级失败';
          }
        })(),
        message: ` ${error.message || '未知错误'}`,
      });
      setConfirmVisible(true);
      setConfirmContent('');
      setProgressVisible(false);
    } finally {
    }
  };

  const renderConfirmContent = () => {
    const { status, title, content, message } = upgradeInfo;
    return !!status ? (
      <>
        <span className={`${status === 'success' ? 'text-[#E3F8FF]' : 'text-[#FF3232]'}`}>
          {title ?? (status === 'success' ? '升级成功！' : '升级失败！')}
        </span>
        <br />
        {content ?? message}
      </>
    ) : (
      confirmContent
    );
  };
  return (
    <>
      {/* <SectionTitle title="EMS主程序" />
      <RhButton
        style={{ fontSize: 22, minWidth: 200, height: 60 }}
        onClick={() => {
          handleModalset({ title: 'EMS升级' }, 'uogradeEMS');
        }}
        disabled={`${systemStatus}` === '1'}
      >
        主程序升级
      </RhButton> */}
      <SectionTitle title="EMS工程文件" />
      <div className="flex flex-row gap-8">
        <RhButton
          style={{ fontSize: 22, minWidth: 200, height: 60 }}
          onClick={() => {
            handleModalset(
              {
                tips: '恢复后原有的工程无法找回(预计耗时5分钟)',
                title: 'EMS工程文件恢复',
                accept: '.zip',
              },
              'recoveryEMS',
            );
          }}
          disabled={`${systemStatus}` !== '1'}
        >
          工程文件恢复
        </RhButton>
        <RhButton
          style={{ fontSize: 22, minWidth: 200, height: 60 }}
          onClick={() => {
            setConfirmContent('是否要备份当前工程（不包含流编排文件）');
            setConfirmFuncParams({ type: 'backupEMS' });
            setConfirmVisible(true);
          }}
        >
          工程文件备份
        </RhButton>
      </div>
      <SectionTitle title="流编排程序文件" />
      <div className="flex flex-row gap-8">
        <RhButton
          style={{ fontSize: 22, minWidth: 200, height: 60 }}
          onClick={() => {
            handleModalset(
              { tips: '恢复后原有的文件无法找回', title: '流编排文件恢复', accept: '.json' },
              'recoveryJS',
            );
          }}
          disabled={`${systemStatus}` !== '1'}
        >
          流编排文件恢复
        </RhButton>
        <RhButton
          style={{ fontSize: 22, minWidth: 200, height: 60 }}
          onClick={() => {
            setConfirmContent('是否要备份当前流编排文件（不包含工程文件）');
            setConfirmFuncParams({ type: 'backupJS' });
            setConfirmVisible(true);
          }}
        >
          流编排文件备份
        </RhButton>
      </div>
      <UploadModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onUpload={handleUpload}
        fileSize={50}
        {...modalParams}
      />
      <UpgradeProgressModal
        title={progressTitle}
        visible={progressVisible}
        progressPercent={progressPercent}
        // onFinish={() => {
        //   setProgressVisible(false);
        // }}
      />
      <ConfirmModal
        open={confirmVisible}
        cancelButtonProps={{
          style: {
            display: !!upgradeInfo.status ? 'none' : 'block',
          },
        }}
        onCancel={handleCancel}
        onConfirm={() => handleConfirm(confirmFuncParams)}
        content={<div className="leading-7">{renderConfirmContent()}</div>}
      />
    </>
  );
};

export default EMSUpload;
