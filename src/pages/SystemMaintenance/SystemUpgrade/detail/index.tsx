import { Radio, Upload, Button, Space, message, Modal, Spin } from 'antd';
import { CloudUploadOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import './index.less';
import { SectionTitle } from '@/pages/DeviceStatus/components/SectionTitle';
import { useDeviceStore } from '@/store/deviceStore';
import { httpGet, httpPost, httpPostFile } from '@/shared/http';
import { getCorrespondingDeviceId } from '../../utils';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import { useNavigate } from 'react-router-dom';
import { clearStateWithExpiry, loadStateWithExpiry, saveStateWithExpiry } from '../hooks/storage';
import { getLocalUsername } from '@/shared/auth';
import EMSUpload from './EMSUpload';
import UpgradeProgressModal from './components/UpgradeProgressModal';

const upgradeType = [
  { value: 'PCS', label: 'PCS固件升级' },
  { value: 'BMS', label: 'BMS固件升级' },
  { value: 'EMS', label: 'EMS升级' },
];

export const SystemUpgradeDetail = () => {
  const navigate = useNavigate();
  const [type, setType] = useState('');
  const [selectedDevices, setSelectedDevices] = useState<string>('');
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [deviceList, setDeviceList] = useState<any[]>([]);
  const [upgrading, setUpgrading] = useState<boolean>(false);
  const [progressVisible, setProgressVisible] = useState<boolean>(false);
  const [progressPercent, setProgressPercent] = useState<number>(0);
  const [confirmVisible, setConfirmVisible] = useState<boolean>(false);
  const { systemStatus, getDevicesByType } = useDeviceStore();
  const [upgradeInfo, setUpgradeInfo] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [clearState, setClearState] = useState<boolean>(false);

  // 获取设备数据
  const fetchDeviceValues = async (_deviceList: any) => {
    try {
      const list = _deviceList?.map((item: { id: any }) => item.id);
      const versionPoint = ['PCS_SW_Ver_2', 'PCS_SW_Ver_1', 'BMS_SVN2', 'BMS_SVN1'];
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', [
        {
          // BMS_SVN2 BMS_SVN1 PCS_SW_Ver_2 PCS_SW_Ver_1
          deviceList: list,
          items: versionPoint.filter((item) => item.includes(type)),
        },
      ]);

      if (res.code === 0) {
        const device: any = {};
        res.data?.forEach((element: any) => {
          device[element.device.deviceID] = element.device;
          const vesionMap: any = {};
          element?.itemList?.forEach((item: any) => {
            vesionMap[item.itemName] = item.itemValue;
          });
          if (element.device === 'PCS') {
            device[element.device.deviceID].version =
              `${vesionMap['PCS_SW_Ver_1']}${vesionMap['PCS_SW_Ver_2']}`;
          } else if (element.device === 'BMS') {
            device[element.device.deviceID].version =
              `${vesionMap['BMS_SVN1']}${vesionMap['BMS_SVN2']}`;
          }
        });
        _deviceList.forEach((item: any) => {
          item.status = device[item.id]?.status;
          item.version = device[item.id]?.version;
        });
        setDeviceList(_deviceList);
      }
    } catch (error) {
      console.error('获取设备数据失败:', error);
    }
  };

  const handleTypeChange = (value: string) => {
    setType(value);
    // 清空type下的选中
    setSelectedDevices('');
  };

  useEffect(() => {
    if (type) {
      setLoading(true);
      const device = getDevicesByType(type);
      if (!selectedDevices) {
        setSelectedDevices(device[0]?.id);
      }
      fetchDeviceValues(device).then(() => {
        setLoading(false);
      });
    }
  }, [type, selectedDevices]);

  // 设备启停操作
  const handleDevice = (deviceIDs: string[], opt: 0 | 1) => {
    const params = {
      deviceIDs: deviceIDs,
      operationType: opt,
    };
    if (deviceIDs?.filter((item) => !!item)?.length === 0) {
      message.error('未获取到设备信息');
      return;
    }
    return httpPost('/api/RootInterface/EMSDeviceStartStop', params);
  };

  const handleConfirmUpgrade = async () => {
    if (selectedDevices.length === 0) {
      message.warning('请选择至少一个设备');
      return;
    }
    if (!uploadedFile || !uploadedFile.size) {
      message.warning('请上传升级文件');
      return;
    }

    let resStatus = '';
    // 开始升级前需要停止设备
    const mainDevice = selectedDevices;
    const correspondingDeviceId = getCorrespondingDeviceId(mainDevice);
    const devicesToOption = correspondingDeviceId
      ? [mainDevice, correspondingDeviceId]
      : [mainDevice];

    try {
      setUpgrading(true);
      setProgressVisible(true);
      setProgressPercent(0);

      // 步骤1: 停止设备
      resStatus = await handleDevice(devicesToOption, 0);
      const username = getLocalUsername();
      if (resStatus === 'successful') {
        // 获取当前选中设备的信息
        const _selectedDevice = deviceList.find((d) => d.id === selectedDevices);
        if (!_selectedDevice) {
          throw new Error('未找需要更新的设备信息');
        }
        // 获取当前部署设备的IP地址
        const getCurrentDeviceIP = () => {
          return window.location.hostname !== 'localhost' ? window.location.hostname : '127.0.0.1';
        };
        // 创建FormData对象上传文件和参数
        const formData = new FormData();
        formData.append('file', uploadedFile);
        formData.append('DeviceId', mainDevice);
        formData.append('IP', getCurrentDeviceIP());
        formData.append('UserName', username || 'test');
        formData.append('Descript', 'Descript');
        // 从接口获取进度更新
        const checkProgress = async () => {
          try {
            const progressResponse = await httpGet('/api/upgrade/firmware/progress');

            // 假设接口返回格式包含progress字段（0-100）和状态信息
            if (progressResponse.code === 0) {
              const currentProgress = progressResponse.message * 10 + 10;
              setProgressPercent(currentProgress);

              // 如果进度未完成，继续轮询
              if (currentProgress < 100) {
                progressTimer = setTimeout(checkProgress, 1000);
              }
            } else {
              // 接口返回错误状态
              throw new Error(progressResponse.message || '获取升级进度失败');
            }
          } catch (error: any) {
            // 网络错误或其他异常
            clearTimeout(progressTimer);
            setProgressVisible(false);
            throw new Error(error.message || '升级过程中发生错误');
          }
        };
        let progressTimer = setTimeout(checkProgress, 800);

        // 等待接口返回，直到进度100%或出现错误
        try {
          // 发送升级请求并等待接口返回（直到进度完成或出错）
          const res = await httpPostFile('/api/upgrade/firmware/upgrade', formData, {
            timeout: 5 * 60 * 1000, // 5分钟超时
          });
          if (res.code === 0) {
            // 如果接口正常返回（进度100%），显示升级成功
            setTimeout(() => {
              setProgressVisible(false);
              setUpgradeInfo({
                status: 'success',
                version: '',
                message: '升级完成',
              });
              setConfirmVisible(true);
            }, 500);
          } else {
            throw new Error(res.message || '升级过程中发生错误');
          }
        } catch (error: any) {
          // 接口请求失败时停止进度轮询并抛出异常
          clearTimeout(progressTimer);
          setProgressVisible(false);
          throw new Error(error.message || '升级过程中发生错误');
        }

        /*  if (uploadResponse.code === 200) {
          // 前端模拟升级进度
          let currentProgress = 0;
          const interval = setInterval(() => {
            currentProgress += Math.random() * 15 + 5; // 每次增加5-20%
            if (currentProgress >= 100) {
              currentProgress = 100;
              clearInterval(interval);
              setTimeout(() => {
                setProgressVisible(false);
                setUpgradeInfo({
                  status: 'success',
                  version: uploadResponse.data?.version || '1.0.0',
                  message: '升级完成',
                });
                setConfirmVisible(true);
              }, 500);
            }
            setProgressPercent(Math.min(currentProgress, 100));
          }, 800);
        } else {
          throw new Error(uploadResponse.message || '文件上传失败');
        } */
      } else {
        setProgressVisible(false);
        message.error('设备停止失败，无法开始升级');
      }
    } catch (error: any) {
      setUpgradeInfo({
        status: 'failed',
        message: ` ${error.message || '未知错误'}`,
      });
      setConfirmVisible(true);
      setProgressVisible(false);
    } finally {
      // 停止成功才需要恢复
      if (resStatus === 'successful') {
        await handleDevice(devicesToOption, 1);
      }
      setUpgrading(false);
    }
  };

  const handleCancelUpgrade = () => {
    setSelectedDevices('');
    setClearState(true);
    clearStateWithExpiry('upgradedetail');
    navigate('/system-maintenance/system-upgrade');
    setUploadedFile(null);
  };
  const finishUpgrade = () => {
    setConfirmVisible(false);
    navigate('/system-maintenance/system-upgrade');
  };
  useEffect(() => {
    const data = loadStateWithExpiry('upgradedetail');
    setType(data?.type || upgradeType[0].value);
    setSelectedDevices(data?.selectedDevices);
    setUploadedFile(data?.uploadedFile);
  }, []);

  //   离开页面时存储
  useEffect(() => {
    const data = {
      type,
      selectedDevices,
      uploadedFile,
    };

    const handleBeforeUnload = () => {
      if (clearState) {
        clearStateWithExpiry('upgradedetail');
      } else {
        saveStateWithExpiry('upgradedetail', data);
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      handleBeforeUnload();
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [type, selectedDevices, uploadedFile, clearState]);

  useEffect(() => {
    return () => {
      if (clearState) {
        clearStateWithExpiry('upgradedetail');
      }
    };
  }, [clearState]);

  return (
    <div className="upgrade-detail-content">
      <div className="relative flex">
        <Radio.Group
          onChange={(e) => handleTypeChange(e.target.value)}
          defaultValue={upgradeType?.[0]?.value || 'PCS'}
          value={type}
          className={`custom-radio-group flex w-full justify-center`}
        >
          {(upgradeType || [])?.map((item) => (
            <Radio.Button
              key={item?.value}
              value={item?.value}
              className={'custom-radio'}
              style={{ width: '200px' }}
            >
              {item?.label}
            </Radio.Button>
          ))}
        </Radio.Group>
      </div>
      <Spin spinning={loading}>
        <div className="upgrade-configure">
          {type === 'BMS' || type === 'PCS' ? (
            <>
              <SectionTitle title="设备选择" />
              <Radio.Group
                value={!!selectedDevices ? selectedDevices : undefined}
                onChange={(e) => {
                  setSelectedDevices(e.target.value);
                }}
                className="upgeade-radio w-full"
              >
                {(deviceList || []).map((device) => (
                  <Radio
                    key={device.id}
                    value={device.id}
                    className="h-16 w-[155px] text-base text-[#76AAD9]"
                    disabled={device.status !== 'Connect'}
                  >
                    <Space direction="vertical">
                      <span
                        className="device-status text-xl text-[#E3F8FF]"
                        data-status={device.status || ''}
                      >
                        {device.name}
                      </span>
                      <span
                        style={{
                          color: '#6797D6',
                          margin: 0,
                        }}
                      >
                        {device.version ??''}
                      </span>
                    </Space>
                  </Radio>
                ))}
              </Radio.Group>

              <div className="mt-[32px]">
                <SectionTitle title="选择文件" />
              </div>

              <Space direction="vertical" style={{ width: 550 }}>
                <Upload
                  beforeUpload={(file) => {
                    const isLimit = file.size < 50 * 1024 * 1024;
                    if (!isLimit) {
                      message.error(`文件大小超过${50}MB`);
                      return false;
                    }
                    setUploadedFile(file);
                    return false;
                  }}
                  //   onChange={(info) => {
                  //     if (info.fileList.length > 0) {
                  //       setUploadedFile(info.file);
                  //     } else {
                  //       setUploadedFile(null);
                  //     }
                  //   }}
                  accept=".srec"
                  className="custom-upload"
                  maxCount={1}
                  showUploadList={true}
                >
                  <Button
                    icon={<CloudUploadOutlined />}
                    style={{
                      background:
                        'linear-gradient(180deg, rgba(0, 135, 255, 0.65) 0%, rgba(0, 135, 255, 0.08) 100%)',
                      border: '1px dashed rgba(57, 171, 255, 0.3)',
                      color: '#6DE875',
                      fontSize: 22,
                      minWidth: 300,
                      height: 60,
                    }}
                  >
                    点击上传文件
                  </Button>
                </Upload>
              </Space>
            </>
          ) : (
            <EMSUpload />
          )}
        </div>
      </Spin>
      {type !== 'EMS' && (
        <Space className="absolute bottom-10 w-full justify-center">
          <Button
            type="primary"
            onClick={handleConfirmUpgrade}
            style={{
              background:
                'linear-gradient(180deg, rgba(0, 135, 255, 0.65) 0%, rgba(0, 135, 255, 0.08) 100%)',
              border: '1px solid #39ABFF',
              color: '#6DE875',
              fontSize: 22,
              minWidth: 200,
              height: 56,
            }}
            disabled={`${systemStatus}` !== '1'}
            loading={upgrading || loading}
          >
            确认升级
          </Button>
          <Button
            onClick={handleCancelUpgrade}
            ghost
            style={{
              background: 'rgba(118, 170, 217, 0.1)',
              border: '1px solid rgba(118, 170, 217, 0.3)',
              color: '#6DE875',
              fontSize: 22,
              minWidth: 200,
              height: 56,
            }}
          >
            退出升级
          </Button>
        </Space>
      )}

      {/* 升级进度弹窗 */}
      <UpgradeProgressModal visible={progressVisible} progressPercent={progressPercent} />

      <ConfirmModal
        open={confirmVisible}
        onCancel={finishUpgrade}
        cancelButtonProps={{
          style: {
            display: 'none',
          },
        }}
        onConfirm={finishUpgrade}
        content={
          <div className={`leading-7`}>
            <span
              className={`${upgradeInfo.status === 'success' ? 'text-[#E3F8FF]' : 'text-[#FF3232]'}`}
            >
              {upgradeInfo.status === 'success' ? '升级成功！' : '升级失败！'}
            </span>
            <br />
            {upgradeInfo.status === 'success'
              ? ` ${selectedDevices}设备已经升级为${upgradeInfo.version}版本`
              : upgradeInfo?.message || ''}
          </div>
        }
      />
    </div>
  );
};
