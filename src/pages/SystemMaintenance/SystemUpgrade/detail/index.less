.upgrade-detail-content {
  height: 100%;
  width: 100%;
  background-color: #04276a;
  border-radius: 8px;
  padding: 40px 24px;
  position: relative;
}
.upgrade-configure {
  // padding: 40px24px;
  width: 100%;
  // background: rgba(4, 39, 106, 0.3);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  .upgeade-radio {
    .device-status {
      display: flex;
      align-items: center;
      &::after {
        content: '';
        display: inline-block;
        width: 9px;
        height: 9px;
        border-radius: 50%;
        margin-left: 8px;
        background-color: #909599;
      }
      &[data-status='Connect'] {
        &::after {
          background-color: #00ff51 !important;
        }
      }
    }
    .ant-radio-wrapper {
      margin-inline-end: 0;
    }
    .ant-radio-wrapper .ant-radio-inner {
      width: 46px;
      height: 46px;
      background-color: transparent;
      border-color: #579cdb;
      border-width: 2px;
      &::after {
        width: 55px;
        height: 55px;
        border-radius: 50%;
        left: -6px;
        top: -6px;
        margin-block-start: 0;
        margin-inline-start: 0;
      }
    }
    .ant-radio-wrapper.ant-radio-wrapper-checked .ant-radio-inner {
      background-color: transparent;
      border-color: #6de875;
      &::after {
        background-color: #6de875;
      }
    }
    .ant-radio-wrapper.ant-radio-wrapper-disabled .ant-radio-inner {
      background: #1c428a;
      border-color: #2f689c;
    }
  }
}

.upgrade-configure,.custom-upload {
  .ant-upload-list {
    margin-top: 8px;
    .ant-upload-list-item {
      color: #6797d6;
      font-size: 16px;
      .ant-upload-icon .anticon {
        color: #6797d6;
      }
    }
    .ant-upload-wrapper{
      color: #6797d6;
      font-size: 16px;
    }
    .ant-progress{
      line-height: 0;
      .ant-progress-bg{
        background: #6DE875;
        height: 8px !important;
      }
      .ant-progress-inner {
        background-color: #1D4CA6;
      }
    } 
  }
  .ant-btn {
    border-radius: 6px;
    font-size: 14px;
    height: 36px;
    padding: 0 20px;
  }
  .ant-btn-primary {
    background: #1890ff;
    border-color: #1890ff;
  }

  .ant-btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
  }

  .ant-btn-dangerous {
    background: transparent;
    border-color: #ff4d4f;
    color: #ff4d4f;
  }
  .ant-btn-dangerous:hover {
    background: rgba(255, 77, 79, 0.1);
    border-color: #ff7875;
    color: #ff7875;
  }

  .ant-typography {
    color: #76aad9;
  }
.custom-upload{
  .ant-upload-list-item-actions{
    display: none;
  }
}
}
