const _STORAGE_KEY = 'myApp:formState';
const expiry_time = 5 * 60 * 1000; // 5分钟
export const saveStateWithExpiry = (
  STORAGE_KEY: string = _STORAGE_KEY,
  state: any,
  expiryTime: number = expiry_time,
) => {
  const item = {
    value: state,
    expiry: Date.now() + expiryTime,
  };
  localStorage.setItem(STORAGE_KEY, JSON.stringify(item));
};

export const loadStateWithExpiry = (STORAGE_KEY: string = _STORAGE_KEY) => {
  const itemStr = localStorage.getItem(STORAGE_KEY);
  if (!itemStr) return null;

  const item = JSON.parse(itemStr);
  if (Date.now() > item.expiry) {
    localStorage.removeItem(STORAGE_KEY);
    return null;
  }
  return item.value;
};

export const clearStateWithExpiry = (STORAGE_KEY: string = _STORAGE_KEY) => {
  localStorage.removeItem(STORAGE_KEY);
};