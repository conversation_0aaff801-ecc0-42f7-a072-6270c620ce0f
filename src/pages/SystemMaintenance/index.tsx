import React from 'react';
import { Layout, Menu } from 'antd';
import { Outlet, useNavigate } from 'react-router-dom';
import styles from './index.module.less';

const { Sider, Content } = Layout;

const menuItems = [

    {
        key: 'device-monitor',
        label: '设备数据',
        children: [
            {
                key: 'pcs',
                label: 'PCS',
            },
            {
                key: 'bms',
                label: 'BMS',
            },
            {
                key: 'charging-pile',
                label: '充电桩',
            },
            {
                key: 'water-cooling',
                label: '水冷机组',
            }
        ],
    },
    {
        key: 'logs',
        label: '日志管理',
        children: [
            {
                key: 'command-logs',
                label: '指令日志',
            },
        ],
    },
];

export const SystemMaintenance: React.FC = () => {
    const navigate = useNavigate();

    const handleMenuClick = (key: string) => {
        navigate(`/system-maintenance/${key}`);
    };

    return (
        <Layout style={{ height: '100%', background: 'transparent', color: '#76AAD9' }}>
            <Sider width={200} className={styles.sider} style={{ color: '#76AAD9' }}>
                <Menu
                    mode="inline"
                    items={menuItems}
                    defaultOpenKeys={['device-monitor']}
                    onClick={({ key }) => handleMenuClick(key)}
                    style={{ background: 'transparent', color: '#76AAD9' }}
                />
            </Sider>
            <Content className="overflow-hidden" style={{ padding: '24px' }}>
                <Outlet />
            </Content>
        </Layout>
    );
};