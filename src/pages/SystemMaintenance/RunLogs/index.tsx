import RhDatePicker from '@/components/RhDatePicker';
import RhPagination from '@/components/RhPagination';
import { RhButton } from '@/components/ui/pagbtn';
import { message, Modal, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { formatDate } from '@/utils/formatDate';
import { ColumnsType } from 'antd/es/table';
import { CloseOutlined } from '@ant-design/icons';
import RhSelectInput from '@/components/RhSelectInput';
import { httpGet, httpPost } from '@/shared/http';
import { generateUUID } from '@/utils/uuid';

const flowTypeMap: any = {
  // 1: '关机',
  // 2: '待机',
  // 3: '补能',
  // 4: '供电',
  // 5: '补能并供电',
  6: 'DCDC',
  7: '并网放电',
  8: '直流补能',
  9: '交流补能',
  10: '交流供电',
  11: '充电桩供电',
};
const stepMap = {
  0: '关闭',
  1: '启动中',
  2: '进行中',
  3: '结束中',
};
const DEFAULTPAGESIZE = 10;
export const RunLogs = () => {
  const [timeRange, setTimeRange] = useState<any>({ startTime: '', endTime: '' });
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [dateValue, setDateValue] = useState<any>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    currentPage: 1,
    pageSize: DEFAULTPAGESIZE,
  });
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<any>();
  const [queryParams, setQueryParams] = useState<any>({
    startTime: '',
    endTime: '',
    pageIndex: 1,
    pageSize: DEFAULTPAGESIZE,
    flowtype: '',
    deviceid: '',
  });
  //   设备名称
  const [options, setOptions] = useState([]);
  //   运行流程
  const [flowtypeOptions, setFlowtypeOptions] = useState([]);

  const columns: ColumnsType<any> = [
    {
      title: '日志ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '时间',
      dataIndex: 'createtime',
      key: 'createtime',
      width: 180,
      render: (time) => formatDate(time),
    },
    {
      title: '流程',
      dataIndex: 'flowtype',
      key: 'flowtype',
      width: 150,
      render: (flowtype) => flowTypeMap[flowtype],
    },
    {
      title: '运行状态',
      dataIndex: 'step',
      key: 'step',
      width: 150,
      render: (step: any) => {
        const numericStep = typeof step === 'number' ? step : parseInt(step, 10);
        if (isNaN(numericStep) || !Object.prototype.hasOwnProperty.call(stepMap, numericStep)) {
          return '';
        }
        return stepMap[numericStep as keyof typeof stepMap] || '';
      },
    },
    {
      title: '设备名称',
      dataIndex: 'deviceid',
      key: 'deviceid',
      width: 150,
    },
    {
      title: '信息描述',
      dataIndex: 'error',
      key: 'error',
      width: 250,
    },
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      width: 80,
      render: (_, item) => (
        <div
          className="h-full w-full"
          onClick={() => {
            setCurrentItem(item);
            setDetailVisible(true);
          }}
        >
          <a className="text-[#6DE875]">详情</a>
        </div>
      ),
    },
  ];
  const getFlowTypeOptions = async () => {
    const items: any = Object.keys(flowTypeMap).map((item: any) => ({
      id: item,
      value: flowTypeMap[item],
      label: flowTypeMap[item],
    }));
    setFlowtypeOptions(items || []);
  };
  // 获取全部设备
  const getDeviceItems = async () => {
    const res = await httpGet('/api/device/all');
    if (res.code === 0) {
      const deviceType: any = [];
      const items = res.data.map((item: any) => {
        deviceType.push(item.type);
        return {
          ...item,
          key: item.id,
          value: item.id,
          label: item.id,
        };
      });
      setOptions(items || []);
    }
  };

  // 处理日期变化
  const handleDateChange = (dates: any) => {
    setTimeRange({
      startTime: dates?.[0]?.valueOf() ? formatDate(dates?.[0]?.valueOf()) : '',
      endTime: dates?.[1]?.valueOf() ? formatDate(dates?.[1]?.valueOf()) : '',
    });
  };
  const handlePageChange = (page: number) => {
    setPagination((prev: any) => ({
      ...prev,
      currentPage: page,
    }));
    setQueryParams((prev: any) => ({
      ...prev,
      pageIndex: page,
    }));
    fetchLogsList(page);
  };

  //   获取日志列表
  const fetchLogsList = async (current: number = 1, params?: any) => {
    let body = params || queryParams;
    body = {
      ...body,
      pageIndex: current || pagination.currentPage,
    };

    setLoading(true);
    try {
      const res = await httpPost('/api/history/log/flowslog/list', body);
      if (res.code === 0) {
        // 为每条数据添加 uuid
        const listWithUuid = (res.data?.list || []).map((item: any) => ({
          ...item,
          uuid: generateUUID(),
        }));
        setData(listWithUuid);
        setPagination((prev:any)=>({
          ...prev,
          total: res.data?.total || 0,
        }));
      }
    } finally {
      setLoading(false);
    }
  };

  // 处理查询
  const handleConfirmSearch = () => {
    // if (!timeRange.startTime && !timeRange.endTime) {
    //   message.info('请选择时间范围');
    //   return;
    // }
    const params = {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime,
    };
    setQueryParams((prev: any) => ({ ...prev, ...params }));
    setPagination({
      ...pagination,
      currentPage: 1,
    });
    fetchLogsList();
  };
  // 首次加载
  useEffect(() => {
    getFlowTypeOptions();
    fetchLogsList();
    // 获取设备
    getDeviceItems();
  }, []);

  return (
    <div className="flex flex-col">
      <div className="cst mb-4 flex items-center justify-between gap-3 text-[18px]">
        <div className="flex gap-3">
          <div className="relative flex items-center gap-4">
            <span>开始时间</span>
            <RhDatePicker
              allowClear={true}
              value={dateValue} // 使用受控值
              onChange={(dates: any) => {
                setDateValue(dates); // 更新日期选择器的值
                handleDateChange(dates);
              }}
              width={300}
              fontSize={15}
              className="cst"
              format={'YYYY/MM/DD HH:mm'}
            />
          </div>
          <div className="flex items-center gap-4">
            <span>流程</span>
            <RhSelectInput
              allowClear={true}
              value={queryParams.flowtypeName}
              onChange={(val) => {
                const foundItem: any = flowtypeOptions?.find((item: any) => item.value === val);
                const id = foundItem ? foundItem.id : undefined;
                setQueryParams((prev: any) => ({ ...prev, flowtypeName: val, flowtype: id }));
              }}
              options={flowtypeOptions}
              className={'w-[140px]'}
            />
          </div>
          <div className="flex items-center gap-4">
            <span>设备名称</span>
            <RhSelectInput
              allowClear={true}
              value={queryParams.deviceid}
              onChange={(val) => {
                setQueryParams((prev: any) => ({ ...prev, deviceid: val }));
              }}
              options={options}
              className={'w-[140px]'}
            />
          </div>
        </div>
        <RhButton text={'查询'} onClick={handleConfirmSearch} />
      </div>
      <Table
        rowKey="uuid"
        size="middle"
        columns={columns}
        dataSource={data}
        pagination={false}
        className="cst"
        loading={loading}
        scroll={{ y: 503 }}
      />
      {/* 翻页区域 超出当前页面显示才需要翻页*/}
      {pagination?.total > 10 && (
        <div>
          <RhPagination
            total={pagination.total}
            current={pagination.currentPage}
            pageSize={pagination.pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      )}
      <Modal
        title="详情"
        open={detailVisible && !!currentItem}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        className="cst"
        centered={true}
        closeIcon={<CloseOutlined className="modal-close-icon" />}
        zIndex={10000}
      >
        <div className="text-xl leading-loose">
          {columns.map((item: any) => {
            if (item.key === 'option') {
              return <></>;
            }
            return (
              <p className="flex items-center" key={item.key}>
                <span className="w-[100px] flex-shrink-0 text-[#9CCEFF]">{item.title}：</span>
                <span className="flex-1">
                  {item.render?.(currentItem?.[item.key]) || currentItem?.[item.key] || '-'}
                </span>
              </p>
            );
          })}
        </div>
      </Modal>
    </div>
  );
};
