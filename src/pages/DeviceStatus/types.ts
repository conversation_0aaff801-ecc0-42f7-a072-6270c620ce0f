/*
 * @Author: xiangfu.wu
 * @Date: 2025-07-23 19:52:01
 * @Description: 🚀
 * @FilePath: /sanyleemsui/src/pages/DeviceStatus/types.ts
 */
// 设备类型枚举
export enum DeviceType {
  PCS = 'PCS',
  BMS = 'BMS',
  TemperContr = 'TemperContr',
  DCChargingStation = 'ChargingStation',
  ChargeGun = 'ChargeGun',
}

// 弹窗类型枚举
export enum ModalTypeKey {
  PCS_KEY = 'PCSType',
  PCS_MANAGE_KEY = 'PCSManageType',
  BMS_KEY = 'BMSType',
  TEMPER_CONTR_KEY = 'TemperContrType',
  CHARGING_STATION_KEY = 'ChargingStationType',
}