import { useState, useCallback, useRef } from 'react';
import { Form, message } from 'antd';
import { httpPost } from '@/shared/http';
import { isNumber } from 'lodash';
import { Rule } from 'antd/es/form';
import { generateUUID } from '@/utils/uuid';

export interface ConfigItem {
  Name: string;
  Descript: string;
  Unit?: string;
  Value?: any;
  ViewType?: string;
  Type?: string;
  Max?: number;
  Min?: number;
}

interface ParamSettingsOptions {
  /**
   * 设备编码
   */
  deviceCode: string | string[];
  /**
   * 刷新数据的函数
   */
  refreshData: (silent?: boolean, params?: Record<string, any>) => Promise<void>;
  // 是否调用写值接口
  WriteCommand?: boolean;
}

/**
 * 参数设置钩子，用于处理参数配置表单、键盘输入和保存操作
 */
export const useParamSettings = ({
  deviceCode,
  refreshData,
  WriteCommand = true,
}: ParamSettingsOptions) => {
  const [form] = Form.useForm();
  const [editingFields, setEditingFields] = useState<Set<string>>(new Set());
  const [buttonLoading, setButtonLoading] = useState<Record<string, boolean>>({});

  // 键盘相关状态
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [currentEditField, setCurrentEditField] = useState<string>('');
  const keyboardRef = useRef<any>(null);

  // 确认框状态
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmAction, setConfirmAction] = useState<{
    name: string;
    value: any;
    descript: string;
  } | null>(null);

  // 初始化表单字段
  const initFormValues = useCallback(
    (formItems: ConfigItem[], detailData: Record<string, any>) => {
      const formValues: Record<string, any> = {};
      formItems.forEach((item) => {
        // 只更新非编辑中的字段
        if (!editingFields.has(item.Name)) {
          formValues[item.Name] = detailData[item.Name] ?? 0;
        }
      });
      form.setFieldsValue(formValues);
    },
    [form, editingFields],
  );

  // 处理输入框获取焦点事件
  const handleInputFocus = useCallback(
    (fieldName: string) => {
      // 标记字段正在编辑
      setEditingFields((prev) => new Set(prev).add(fieldName));

      // 设置当前编辑字段
      setCurrentEditField(fieldName);

      // 显示键盘
      setShowKeyboard(true);

      // 设置键盘输入的初始值
      if (keyboardRef.current) {
        const currentValue = form.getFieldValue(fieldName)?.toString() || '';
        keyboardRef.current.setInput(currentValue);
      }
    },
    [form],
  );

  // 处理键盘输入
  const handleKeyboardInput = useCallback(
    (input: string) => {
      if (currentEditField) {
        // 更新表单值
        form.setFieldsValue({ [currentEditField]: input });

        // 强制表单项重新渲染
        form.validateFields([currentEditField]).then(
          () => {},
          () => {},
        );
      }
    },
    [currentEditField, form],
  );

  // 处理键盘按键事件
  const handleKeyPress = useCallback(
    async (button: string, configItems: ConfigItem[], detailData: Record<string, any> = {}) => {
      if (!currentEditField) return;

      if (button === '{bksp}') {
        const currentValue = form.getFieldValue(currentEditField);
        const newValue = currentValue?.toString()?.slice(0, -1) || '';
        form.setFieldsValue({ [currentEditField]: newValue });
      } else if (button === '{enter}') {
        try {
          await form.validateFields([currentEditField]);
          const value = form.getFieldValue(currentEditField);
          const oldValue = detailData[currentEditField];

          // 关闭键盘
          setShowKeyboard(false);

          // 如果值改变了，保存参数
          if (value?.toString() !== oldValue?.toString()) {
            // 查找当前字段的描述
            const fieldConfig = configItems.find((item) => item.Name === currentEditField);

            if (fieldConfig) {
              // 显示确认框
              setConfirmAction({
                name: currentEditField,
                value: value,
                descript: fieldConfig.Descript,
              });
              setConfirmVisible(true);
            }
          }

          // 重置当前编辑字段
          setCurrentEditField('');

          // 标记字段编辑结束
          setEditingFields((prev) => {
            const next = new Set(prev);
            next.delete(currentEditField);
            return next;
          });
        } catch (error: any) {
          console.error('校验失败:', error);
          // error对象结构为 { errorFields: [ { name: [...], errors: [...] } ] }
          let errorMessage = '参数设置失败';
          if (error && Array.isArray(error.errorFields)) {
            // 查找当前字段的错误信息
            const fieldError = error.errorFields.find((item: any) => {
              // item.name 是数组，currentEditField 是字符串
              return Array.isArray(item.name) && item.name.includes(currentEditField);
            });
            if (fieldError && Array.isArray(fieldError.errors) && fieldError.errors.length > 0) {
              errorMessage = fieldError.errors.join(',');
            }
          }
          message.error(errorMessage);
        }
      }
    },
    [currentEditField, form],
  );

  // 关闭键盘
  const closeKeyboard = useCallback(() => {
    setShowKeyboard(false);
    setCurrentEditField('');

    setEditingFields((prev) => {
      const next = new Set(prev);
      if (currentEditField) {
        next.delete(currentEditField);
      }
      return next;
    });
  }, [currentEditField]);

  // 执行参数保存
  const executeParamSave = useCallback(
    async (name: string, value: any) => {
      try {
        setButtonLoading((prev) => ({ ...prev, [`param_${name}`]: true }));
        let params = {};
        if (Array.isArray(deviceCode)) {
          params = {
            devices: deviceCode.map((item) => ({
              msgId: generateUUID(),
              deviceCode: item,
              addresses: [
                {
                  name,
                  value,
                },
              ],
            })),
          };
        } else {
          params = {
            devices: [
              {
                msgId: Date.now().toString(),
                deviceCode,
                addresses: [
                  {
                    name,
                    value,
                  },
                ],
              },
            ],
          };
        }

        if (WriteCommand){
          const res = await httpPost('/api/RootInterface/WriteCommand', params);
  
          if (res.code === 0) {
            message.success('参数设置成功');
            // 刷新数据
            refreshData(undefined, params);
          } else {
            message.error(res.message || '参数设置失败');
          }
        }else{
            refreshData(undefined, params);
        }
      } catch (error) {
        message.error('参数设置失败，请重试');
        console.error('参数设置失败:', error);
      } finally {
        setButtonLoading((prev) => ({ ...prev, [`param_${name}`]: false }));
        setConfirmVisible(false);
        setConfirmLoading(false);
        setConfirmAction(null);
      }
    },
    [deviceCode, refreshData],
  );

  // 确认参数保存
  const handleConfirm = useCallback(() => {
    if (!confirmAction) return;

    setConfirmLoading(true);
    executeParamSave(confirmAction.name, confirmAction.value);
  }, [confirmAction, executeParamSave]);

  // 取消参数保存
  const handleCancel = useCallback(() => {
    setConfirmVisible(false);
    setConfirmAction(null);
  }, []);

  const createItemRules = useCallback((item: ConfigItem): Rule[] => {
    const rules: Rule[] = [];
    // 最大值
    if (isNumber(item.Max)) {
      rules.push({
        validator: (_, value) => {
          if (value > (item?.Max as number)) {
            return Promise.reject('最大值不能大于' + item.Max);
          }
          return Promise.resolve();
        },
      });
    }
    // 最小值
    if (isNumber(item.Min)) {
      rules.push({
        validator: (_, value) => {
          if (value < (item?.Min as number)) {
            return Promise.reject('最小值不能小于' + item.Min);
          }
          return Promise.resolve();
        },
      });
    }
    // 类型
    if (item?.Type) {
      if (item?.Type.toUpperCase() === 'INT') {
        rules.push({
          validator: (_, value) => {
            if (!/^-?\d+$/.test(String(value))) {
              return Promise.reject('请输入整数');
            }
            return Promise.resolve();
          },
        });
      } else if (item?.Type.toUpperCase() === 'FLOAT') {
        rules.push({
          validator: (_, value) => {
            if (!/^-?\d+(\.\d+)?$/.test(String(value))) {
              return Promise.reject('请输入数字');
            }
            return Promise.resolve();
          },
        });
      }
    }
    return rules;
  }, []);

  return {
    form,
    editingFields,
    buttonLoading,
    showKeyboard,
    currentEditField,
    keyboardRef,
    confirmVisible,
    confirmLoading,
    confirmAction,
    initFormValues,
    handleInputFocus,
    handleKeyboardInput,
    handleKeyPress,
    closeKeyboard,
    executeParamSave,
    handleConfirm,
    handleCancel,
    createItemRules,
  };
}; 