import { useState, useCallback } from 'react';
import { message } from 'antd';
import { httpPost } from '@/shared/http';

// 定义接口
interface DeviceAddress {
  name: string;
  value: any;
  isSuc?: boolean;
}

interface DeviceResponse {
  msgId: string;
  deviceCode: string;
  addresses: DeviceAddress[];
}

interface CommandResponse {
  code: number;
  message: string;
  data?: {
    devices?: DeviceResponse[];
  };
}

interface CommandExecutionOptions {
  /**
   * 刷新数据的函数
   */
  refreshData: (silent?: boolean) => Promise<void>;
  /**
   * 是否需要确认
   */
  needConfirm?: boolean;
  /**
   * 执行前验证函数，返回true才会继续执行
   */
  beforeExec?: (params: {
    name: string;
    value: any;
    deviceCode: string;
    deviceType?: string;
    api?: string;
    index?: number;
  }) => boolean;
  /**
   * 自定义参数设置函数，允许完全自定义请求参数
   */
  setRequestParams?: (params: {
    name: string;
    value: any;
    deviceCode: string;
    deviceType?: string;
  }) => any;
}

// 确认操作的类型
export interface ConfirmActionType {
  type: 'button' | 'api' | 'param';
  name: string;
  value: any;
  descript: string;
  api?: string;
  index?: number;
  deviceCode: string; // 添加设备代码
  deviceType?: string;
}

/**
 * 命令执行钩子，提供统一的按钮命令执行逻辑
 */
export const useCommandExecution = ({
  refreshData,
  needConfirm = true,
  beforeExec,
  setRequestParams,
}: CommandExecutionOptions) => {
  // 按钮加载状态
  const [buttonLoading, setButtonLoading] = useState<Record<string, boolean>>({});

  // 确认框状态
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmAction, setConfirmAction] = useState<ConfirmActionType | null>(null);

  // 执行按钮命令
    const executeButtonCommand = useCallback(
    async (name: string, value: any, deviceCode: string, deviceType?: string) => {
      console.log('executeButtonCommand', name, value, deviceCode, deviceType);
      try {
        // 如果存在执行前验证，则先执行验证
        if (beforeExec && !await beforeExec?.({ name, value, deviceCode, deviceType })) {
          return false; // 如果验证失败，则不继续执行
        }
        
        setButtonLoading((prev) => ({ ...prev, [`${name}_${value}`]: true }));

        // 使用自定义参数设置函数或默认参数设置逻辑
        const params = setRequestParams 
          ? await setRequestParams?.({ name, value, deviceCode, deviceType })
          : (() => {
              // 默认参数设置逻辑
              // 单设备操作模式
              const usedDeviceCode = deviceType || deviceCode;
              
              const devices = [
                {
                  msgId: Date.now().toString(),
                  deviceCode: usedDeviceCode,
                  addresses: [
                    {
                      name,
                      value,
                    },
                  ],
                },
              ];
              
              return { devices };
            })();

        const res = await httpPost('/api/RootInterface/WriteCommand', params);

        if (res.code === 0) {
          // 检查是否有多个设备，并判断成功和失败情况
          if (res.data?.devices && res.data.devices.length > 0) {
            const successDevices = res.data.devices.filter((device: DeviceResponse) => 
              device.addresses.every((address: DeviceAddress) => address.isSuc === true)
            );
            const failedDevices = res.data.devices.filter((device: DeviceResponse) => 
              device.addresses.some((address: DeviceAddress) => address.isSuc !== true)
            );
            
            // 如果都成功
            if (failedDevices.length === 0) {
              message.success({
                content: '指令发送成功',
              });
            } 
            // 如果部分成功部分失败
            else if (successDevices.length > 0 && failedDevices.length > 0) {
              message.warning({
                content: `部分指令发送成功，失败设备: ${failedDevices.map((d: DeviceResponse) => d.deviceCode).join(', ')}`,
                duration: 5
              });
            } 
            // 如果都失败
            else {
              message.warning({
                content: `指令发送失败，失败设备: ${failedDevices.map((d: DeviceResponse) => d.deviceCode).join(', ')}`,
              });
            }
          } else {
            message.success({
              content: '指令发送成功',
            });
          }
          
          // 刷新数据
          refreshData();
          return true;
        } else {
          message.error({
            content: res.message || '指令发送失败',
            duration: 5
          });
          return false;
        }
      } catch (error) {
        message.error('指令发送失败，请重试');
        console.error('执行按钮命令失败:', error);
        return false;
      } finally {
        setButtonLoading((prev) => ({ ...prev, [`${name}_${value}`]: false }));
      }
    },
    [refreshData],
  );

  // 执行API命令
    const executeApiCommand = useCallback(
    async (api: string, value: any, index: number, deviceCode: string) => {
      try {
        // 如果存在执行前验证，则先执行验证
        if (beforeExec && !await beforeExec?.({ name: '', value, deviceCode, api, index })) {
          return false; // 如果验证失败，则不继续执行
        }
        
        // 替换value中的deviceID为当前deviceCode
        const requestValue = { ...value };
        if (requestValue.deviceID) {
          requestValue.deviceID = deviceCode;
        }
        
        // 使用model和index来标识按钮
        const buttonKey = `api_${value.model || 'default'}_${index}`;
        setButtonLoading((prev) => ({ ...prev, [buttonKey]: true }));

        const res = await httpPost(api, requestValue);

        if (res.result === 'successful') {
          message.success({
            content: res.resultInfo || '指令发送成功',
            duration: 5
          });
          // 刷新数据
          refreshData();
          return true;
        } else {
          message.error({
            content: res.resultInfo || '指令发送失败',
            duration: 5
          });
          return false;
        }
      } catch (error) {
        message.error('指令发送失败，请重试');
        console.error('执行API命令失败:', error);
        return false;
      } finally {
        // 使用model和index来标识按钮
        const buttonKey = `api_${value.model || 'default'}_${index}`;
        setButtonLoading((prev) => ({ ...prev, [buttonKey]: false }));
      }
    },
    [refreshData],
  );

  // 处理按钮点击事件
  const handleButtonClick = useCallback(
    (name: string, value: any, descript: string, deviceCode: string, deviceType?: string) => {
      if (needConfirm) {
        // 如果需要确认，显示确认对话框
        setConfirmAction({
          type: 'button',
          name,
          value,
          descript,
          deviceCode, // 添加设备代码
          deviceType,
        });
        setConfirmVisible(true);
      } else {
        // 如果不需要确认，直接执行
        executeButtonCommand(name, value, deviceCode, deviceType);
      }
    },
    [executeButtonCommand, needConfirm],
  );

  // 处理API按钮点击事件
  const handleApiButtonClick = useCallback(
    (api: string, value: any, descript: string, index: number, deviceCode: string) => {
      if (needConfirm) {
        // 如果需要确认，显示确认对话框
        setConfirmAction({
          type: 'api',
          api,
          name: '',
          value,
          descript,
          index,
          deviceCode, // 添加设备代码
        });
        setConfirmVisible(true);
      } else {
        // 如果不需要确认，直接执行
        executeApiCommand(api, value, index, deviceCode);
      }
    },
    [executeApiCommand, needConfirm],
  );

  // 处理确认操作
  const handleConfirm = useCallback(async () => {
    if (!confirmAction) return;

    setConfirmLoading(true);

    try {
      if (confirmAction.type === 'button' && confirmAction.name) {
        await executeButtonCommand(
          confirmAction.name,
          confirmAction.value,
          confirmAction.deviceCode,
          confirmAction.deviceType,
        );
      } else if (confirmAction.type === 'api' && confirmAction.api) {
        await executeApiCommand(
          confirmAction.api,
          confirmAction.value,
          confirmAction.index || 0,
          confirmAction.deviceCode,
        );
      }
    } finally {
      setConfirmVisible(false);
      setConfirmLoading(false);
      setConfirmAction(null);
    }
  }, [confirmAction, executeButtonCommand, executeApiCommand]);

  // 取消确认
  const handleCancel = useCallback(() => {
    setConfirmVisible(false);
    setConfirmAction(null);
  }, []);

  return {
    buttonLoading,
    executeButtonCommand,
    executeApiCommand,
    handleButtonClick,
    handleApiButtonClick,
    // 确认框相关
    confirmVisible,
    confirmLoading,
    confirmAction,
    handleConfirm,
    handleCancel,
  };
};
