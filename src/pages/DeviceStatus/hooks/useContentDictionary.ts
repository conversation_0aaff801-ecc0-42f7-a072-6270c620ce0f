import { useCallback } from 'react';
import { getPcsRunStateLabel } from '@/constants/device';
import { DeviceType } from '../types';
import { useDictStore } from '@/store/dictStore';

// 处理ViewType为Content类型的数据字典
export const useContentDictionary = () => {
  const { hasType, hasName, getDictValue } = useDictStore();
  const getContentValue = useCallback((type: DeviceType, propertyName: string, value: any) => {
    // 先从字典中查找
    getValueByDictLabel: {
      let res = '';
      if (!hasType(type) || !hasName(type, propertyName)) {
        break getValueByDictLabel;
      }
      res = getDictValue(type, propertyName, value);
      if (res !== null && res !== undefined && res !== '') {
        return res;
      }
      // 如果字典中没有找到，则继续走默认逻辑
    }
    console.log('\x1b[31m%s\x1b[0m', `此数据没有找到默认字典: ${type} ${propertyName} ${value}`)
    // 根据不同的字段名返回对应的显示内容
    switch (propertyName) {
      case 'PCS_Operation_Status':
        return getPcsRunStateLabel(value);
      case 'PowerOn_Comm_ID':
        return value !== null && value !== undefined ? value : '-';
      case 'WorkingMode':
        return getWorkingModeLabel(value);
      // 可以根据需要添加更多字段的处理逻辑
      default:
        return value !== null && value !== undefined ? value : '-';
    }
  }, []);

  return {
    getContentValue
  };
};

// 获取工作模式显示标签
function getWorkingModeLabel(value: number): string {
  const modes: Record<number, string> = {
    0: '关机',
    1: '离网逆变',  // 从"制冷"修改为"离网逆变"
    2: '制热',
    3: '自循环'
  };
  return modes[value] || `未知(${value})`;
} 