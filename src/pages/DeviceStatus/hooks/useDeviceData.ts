import { useCallback } from 'react';

export interface DeviceConfig {
  Label?: string;
  Title?: string;
  deviceID: Array<{
    Code: string;
    Name: string;
    IDList?: number[]; // 添加IDList属性
  }>;
  ItemList: Array<{
    Name: string;
    Descript: string;
    Unit?: string;
    ViewType?: string;
    ID?: number; // 添加ID属性
    Group?: Array<{
      Name: string;
      Descript: string;
      Unit?: string;
      ViewType?: string;
    }>;
  }>;
}

export interface DeviceItem {
  device: {
    name: string;
    deviceID: string;
    type: string;
    status: string;
    lastStatus: string;
  };
  itemList: Array<{
    name: string;
    description: string;
    value: any;
    lastValue: any;
  }>;
}

// 处理设备数据的hook
export const useDeviceData = () => {
  // 查找设备数据中指定名称的项
  const findItemByName = useCallback((itemList: any[], itemName: string) => {
    return itemList?.find((item: any) => item.name === itemName);
  }, []);

  // 获取设备的在线状态
  const getDeviceStatus = useCallback((deviceData: any) => {
    const isOnline = deviceData?.device?.status === "Connect";
    return {
      status: isOnline ? "online" : "offline",
      statusColor: isOnline ? "#00ff50" : "#4365a9",
      statusText: isOnline ? "在线" : "离线",
      borderColor: isOnline ? "#39abff" : "#4365a9",
      textColor: isOnline ? "#00C8FF" : "#4365a9",
    };
  }, []);

  // 构建设备卡片数据
  const buildDeviceCardData = useCallback((rawData: any, config: DeviceConfig) => {
    if (!rawData || !config?.deviceID?.length) return [];

    // 将原始数据转换为以deviceID为key的映射
    const deviceMap = Array.isArray(rawData) ? rawData.reduce((acc: any, item: DeviceItem) => {
      acc[item.device.deviceID] = item;
      return acc;
    }, {}) : {};

    // 根据配置构建卡片数据
    return config.deviceID.map((device, index) => {
      const deviceData = deviceMap[device.Code] || null;
      
      if (!deviceData) {
        // 设备数据不存在时的默认值
        return {
          id: index + 1,
          name: device.Name,
          deviceCode: device.Code,
          status: "offline",
          statusColor: "#4365a9",
          statusText: "离线",
          borderColor: "#4365a9",
          textColor: "#4365a9",
          properties: [],
          hasAlarm: false,
          rawData: null
        };
      }

      const statusInfo = getDeviceStatus(deviceData);
      
      // 处理设备属性
      const properties = config.ItemList.map(item => {
        const dataItem = findItemByName(deviceData.itemList, item.Name);
        return {
          name: item.Name,
          label: item.Descript,
          value: dataItem?.value,
          unit: item.Unit || '',
          viewType: item.ViewType || '',
          rawItem: dataItem
        };
      });

      // 检查是否有告警状态（可以根据实际逻辑调整）
      const hasAlarm = deviceData.itemList?.some((item: any) => 
        item.name?.includes('Alarm') && item.value === 1
      ) || false;

      return {
        id: index + 1,
        name: device.Name,
        deviceCode: device.Code,
        ...statusInfo,
        properties,
        hasAlarm,
        rawData: deviceData
      };
    });
  }, [findItemByName, getDeviceStatus]);

  return {
    findItemByName,
    getDeviceStatus,
    buildDeviceCardData
  };
}; 