import { useCallback, useState } from 'react';
import { useDeviceData, DeviceConfig } from '../../hooks/useDeviceData';
import { useContentDictionary } from '../../hooks/useContentDictionary';
import { SectionHeader } from '../../components/SectionHeader/SectionHeader';
import { CoolingUnitCard } from '../../components/CoolingUnitCard';
import { CoolingUnitDetailModal } from '../../components/CoolingUnitDetailModal';
import { DeviceType } from '../../types';

// CoolingUnitSection组件属性定义
interface CoolingUnitSectionProps {
  data: any[];
  config?: DeviceConfig;
  type?: 'short' | 'full';
}
const TYPE = DeviceType.TemperContr;
export const CoolingUnitSection = ({ data = [], config, type = 'full' }: CoolingUnitSectionProps): JSX.Element => {
  const { buildDeviceCardData } = useDeviceData();
  const { getContentValue } = useContentDictionary();
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [currentDevice, setCurrentDevice] = useState<any>(null);

  // 处理设备数据
  const coolingConfig = config || undefined;
  const coolingData = coolingConfig ? buildDeviceCardData(data, coolingConfig) : [];
  
  // 处理详情点击
  const handleDetailClick = useCallback((id: number) => {
    const device = coolingData.find(item => item.id === id);
    if (device) {
      setCurrentDevice(device);
      setIsDetailModalOpen(true);
    }
  }, [coolingData]);

  // 处理"总览"点击
  const handleOverviewClick = useCallback(() => {
    console.log('跳转到总览页面');
  }, []);
  
  // 处理属性数据用于显示
  const processCardProperties = useCallback((device: any) => {
    if (!device || !device.properties) return [];
    
    const processed = device.properties.map((prop: any) => {
      const value = prop.viewType === 'Content'
        ? getContentValue(TYPE, prop.name, prop.value)
        : prop.value;
      
      return {
        name: prop.name,
        label: prop.label,
        value: value !== undefined && value !== null ? value : '-',
        unit: prop.name === 'EffluentTemperature' ? '℃' : prop.unit || ''
      };
    });
    
    return processed;
  }, [getContentValue]);

  return (
    <div 
      className="relative bg-no-repeat w-[420px] h-[708px]"
      style={{
        backgroundImage: `url('/device/rectangle-1382${type === 'short' ? '' : '-2'}.svg')`,
        // backgroundImage: `url('/device/rectangle-1382.svg')`,
      }}
    >
      <div className="relative h-[708px]">
        <img
          className="absolute w-1.5 h-5 top-[34px] right-0"
          alt="Rectangle"
          src="/device/rectangle-1409.svg"
        />

        <img
          className="absolute w-1.5 h-5 top-[34px] left-0"
          alt="Rectangle"
          src="/device/rectangle-1420.svg"
        />

        <SectionHeader 
          title={coolingConfig?.Title || "水冷机组"} 
          label={coolingConfig?.Label}
          onLabelClick={handleOverviewClick}
        />

        <div 
          className="flex flex-col w-full items-start gap-[8px] left-[25px] max-h-[708px] overflow-y-auto mt-[22px] px-[17px]"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#39abff #001857',
            paddingBottom: '100px',
          }}
        >
          {coolingData.map((unit) => (
            <CoolingUnitCard
              key={unit.id}
              id={unit.id}
              name={unit.name}
              status={unit.status}
              statusText={unit.statusText}
              statusColor={unit.statusColor}
              textColor={unit.textColor}
              borderColor={unit.borderColor}
              properties={processCardProperties(unit)}
              hasAlarm={unit.hasAlarm}
              onDetailClick={handleDetailClick}
            />
          ))}
        </div>
      </div>

      {/* 冷水机组详情弹窗 */}
      {currentDevice && (
        <CoolingUnitDetailModal
          open={isDetailModalOpen}
          onOpenChange={setIsDetailModalOpen}
          deviceCode={currentDevice.deviceCode}
          deviceName={currentDevice.name}
        />
      )}
    </div>
  );
}; 