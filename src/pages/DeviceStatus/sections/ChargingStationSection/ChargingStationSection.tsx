import { useState, useCallback } from 'react';
import { useDeviceData, DeviceConfig } from '../../hooks/useDeviceData';
import { useContentDictionary } from '../../hooks/useContentDictionary';
import { SectionHeader } from '../../components/SectionHeader/SectionHeader';
import { ChargingStationCard } from '../../components/ChargingStationCard';
import { ChargingStationDetailModal } from '../../components/ChargingStationDetailModal';
import { DeviceType } from '../../types';

// ChargingStationSection组件属性定义
interface ChargingStationSectionProps {
  data: any[];
  config?: DeviceConfig;
  type?: 'short' | 'full';
}
const TYPE = DeviceType.DCChargingStation;
export const ChargingStationSection = ({ data = [], config, type = 'full' }: ChargingStationSectionProps): JSX.Element => {
  const { buildDeviceCardData } = useDeviceData();
  const { getContentValue } = useContentDictionary();
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [currentDevice, setCurrentDevice] = useState<any>(null);

  // 处理设备数据
  const chargingConfig = config || undefined;
  const chargingData = chargingConfig ? buildDeviceCardData(data, chargingConfig) : [];
  
  // 处理详情点击
  const handleDetailClick = useCallback((id: number) => {
    const device = chargingData.find(item => item.id === id);
    if (device) {
      setCurrentDevice(device);
      setIsDetailModalOpen(true);
    }
  }, [chargingData]);

  // 处理"总览"点击
  const handleOverviewClick = useCallback(() => {
    console.log('跳转到总览页面');
  }, []);
  
  // 处理充电枪数据用于显示
  const processCardProperties = useCallback((device: any) => {
    if (!device || !device.properties || !chargingConfig) return [];
    
    // 提取充电枪属性（通常是A枪和B枪）
    return device.properties.map((prop: any) => {
      const value = prop.viewType === 'Content'
        ? getContentValue(TYPE, prop.name, prop.value)
        : prop.value;
      
      // 根据属性名称判断是A枪还是B枪
      const label = prop.name.includes('1') ? 'A枪' : 'B枪';
      
      return {
        name: prop.name,
        label: label,
        value: value !== undefined && value !== null ? value : '--',
        unit: prop.unit || ''
      };
    });
  }, [getContentValue, chargingConfig]);

  return (
    <div 
      className="relative bg-no-repeat flex-grow"
      style={{
        width: '420px',
        height: type === 'short' ? '350px' : '708px',
        backgroundImage: `url('/device/rectangle-1382${type === 'short' ? '' : '-2'}.svg')`,
      }}
    >
      <div className="relative h-full">
        <img
          className="absolute w-1.5 h-5 top-[34px] right-0"
          alt="Rectangle"
          src="/device/rectangle-1409.svg"
        />

        <img
          className="absolute w-1.5 h-5 top-[34px] left-0"
          alt="Rectangle"
          src="/device/rectangle-1420.svg"
        />

        <SectionHeader 
          title={chargingConfig?.Title || "充电桩"} 
          label={chargingConfig?.Label}
          onLabelClick={handleOverviewClick}
        />

        <div 
          className="flex flex-col w-full items-start gap-[8px] max-h-full overflow-y-auto mt-[22px] px-[17px]"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#39abff #001857',
            paddingBottom: '17px',
          }}
        >
          {chargingData.map((station) => (
            <ChargingStationCard
              key={station.id}
              id={station.id}
              name={station.name}
              status={station.status}
              statusText={station.statusText}
              statusColor={station.statusColor}
              textColor={station.textColor}
              borderColor={station.borderColor}
              guns={processCardProperties(station)}
              hasAlarm={station.hasAlarm}
              onDetailClick={handleDetailClick}
            />
          ))}
        </div>
      </div>

      {/* 充电桩详情弹窗 */}
      {currentDevice && (
        <ChargingStationDetailModal
          open={isDetailModalOpen}
          onOpenChange={setIsDetailModalOpen}
          deviceCode={currentDevice.deviceCode}
          deviceName={currentDevice.name}
        />
      )}
    </div>
  );
}; 