import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { getGunCCCSText } from '@/constants/device';
import { useMemo } from "react";

// const chargingStations = [
//   {
//     id: "1",
//     name: "1#充电桩",
//     status: "在线",
//     isOnline: true,
//     guns: [
//       { id: "A", name: "A枪", status: "待机（不能充放）" },
//       { id: "B", name: "B枪", status: "停机（不能充放）" },
//     ],
//   },
//   {
//     id: "2",
//     name: "2#充电桩",
//     status: "在线",
//     isOnline: true,
//     guns: [
//       { id: "A", name: "A枪", status: "待机（不能充放）" },
//       { id: "B", name: "B枪", status: "停机（不能充放）" },
//     ],
//   },
//   {
//     id: "3",
//     name: "3#充电桩",
//     status: "离线",
//     isOnline: false,
//     isShutdown: true,
//   },
// ];


export const CoolingSystemSection = ({ data = [] }: any): JSX.Element => {

  // 处理设备数据
  const chargingList = useMemo(() => {
    data.sort?.((a: any, b: any) => a.device?.name?.localeCompare?.(b.device?.name))
    return data.map?.((d: any, index: number) => {
      const isOnline = d.device.status === 'Connect';

      return {
        id: d.device.deviceID,
        name: `${index + 1}#充电桩`,
        status: isOnline ? "在线" : "离线",
        statusColor: isOnline ? "#00ff50" : "#4365a9",
        borderColor: isOnline ? "#39abff" : "#4365a9",
        textColor: isOnline ? "#00c7ff" : "#4365a9",
        iconSrc: "/device/vector-113.svg",
        isOnline,
        items: d.itemList || []
      };
    })
  }, [JSON.stringify(data)]);

  // console.log('chargingList=', chargingList)

  return (
    <section className="relative w-full max-w-[421px]">
      <div className="relative bg-[url(/device/rectangle-1382.svg)] bg-no-repeat w-[420px] h-[370px]" >
    {/*     <img
          className="h-[422px] w-full"
          alt="Rectangle"
          src="/device/rectangle-1382.svg"
        /> */}
        <img
          className="absolute w-1.5 h-5 top-[35px] right-0"
          alt="Rectangle"
          src="/device/rectangle-1409.svg"
        />
        <img
          className="absolute w-1.5 h-5 top-[35px] left-0"
          alt="Rectangle"
          src="/device/rectangle-1420.svg"
        />

        {/* Title section */}
        <div className="absolute left-1/2 -translate-x-1/2 w-[196px] h-[43px] top-0">
          <div className="relative w-[194px] h-[43px]">

            <div className="top-[9px] left-[70px] absolute [text-shadow:0px_0px_12px_#0f7cfb66] text-[#56B2F3] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[22px] tracking-[0] leading-6 whitespace-nowrap">
              充电桩
            </div>

            <div className="absolute w-[194px] h-[43px] top-0 left-0">
              <div className="relative w-[200px] h-[45px] -top-0.5 left-[-3px]">
                <img
                  className="absolute w-[193px] h-[42px] top-[3px] left-[3px]"
                  alt="Vector"
                  src="/device/vector-113.svg"
                />

                <div className="absolute w-3 h-2.5 top-[35px] left-2.5">
                  <img
                    className="absolute w-[18px] h-4 -top-0.5 left-[-3px]"
                    alt="Group"
                    src="/device/rect-lb.png"
                  />
                </div>

                <div className="absolute w-3 h-2.5 top-[35px] left-[177px] rotate-180">
                  <img
                    className="absolute w-[18px] h-4 -top-1 left-[-3px] -rotate-180"
                    alt="Group"
                    src="/device/rect-rb.png"
                  />
                </div>

                <img
                  className="absolute w-[74px] h-px top-0.5 left-[15px]"
                  alt="Group"
                  src="/device/group-line.png"
                />

                <img
                  className="absolute w-[74px] h-px top-11 left-[103px]"
                  alt="Group"
                  src="/device/group-line.png"
                />

                <img
                  className="absolute w-[200px] h-[19px] top-0 left-0"
                  alt="Group"
                  src="/device/group-365-1.png"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Charging stations container */}
        <div className="w-[385px] h-[352px] flex flex-col items-start gap-2 absolute top-[55px] left-[17px] pr-1.5"
          style={{
            maxHeight: '400px',
            scrollbarWidth: 'thin',
            scrollbarColor: '#39abff #001857',
            paddingBottom: '50px',
          }}
        >
          {chargingList.map((station: any) => (
            <Card
              key={station.id}
              className={`flex-1 w-full border-[3px] border-solid ${station.isOnline ? "border-[#39abff]" : "border-[#4365a9]"
                } p-0 shadow-none bg-transparent`}
            >
              <CardContent className="p-3 flex flex-col gap-1.5 h-full">
                {/* Station header */}
                <div className="flex items-center gap-1 w-full">
                  <img
                    className="w-5 h-5"
                    alt="Frame"
                    src="/device/frame-627003-9.svg"
                  />
                  <div className="flex items-center gap-2.5 flex-1">
                    <div
                      className={`w-fit mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal ${station.isOnline ? "text-[#00c7ff]" : "text-[#4365a9]"
                        } text-base leading-[18px] whitespace-nowrap tracking-[0]`}
                    >
                      {station.name}
                    </div>
                  </div>
                  <div className="inline-flex items-center gap-1">
                    <div
                      className={`w-1.5 h-1.5 rounded-[3px] ${station.isOnline ? "bg-[#00ff50]" : "bg-[#4365a9]"
                        }`}
                    />
                    <Badge
                      variant="outline"
                      className={`border-none bg-transparent p-0 ${station.isOnline ? "text-[#00ff50]" : "text-[#4365a9]"
                        } text-sm [font-family:'PingFang_SC-Semibold',Helvetica] font-normal`}
                    >
                      {station.status}
                    </Badge>
                  </div>
                </div>

                {/* Station content */}
                {station.isShutdown ? (
                  <div className="flex-1 flex flex-col items-start justify-center gap-1.5 py-[15px]">
                    <div className="flex items-center justify-center gap-1 w-full">
                      <div className="inline-flex items-center gap-2.5">
                        <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#4365a9] text-base leading-[18px] whitespace-nowrap tracking-[0]">
                          关机
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="items-center gap-1.5 flex-1 w-full flex">
                    {station?.items?.map?.((gun: any, index: number) => (
                      <div
                        key={gun.name}
                        className={`gap-1 px-4 py-[7px] flex flex-col items-start flex-1 self-stretch grow 
                          ${index === 0 ? "mt-[-1.00px] mb-[-1.00px] ml-[-1.00px]" : "mt-[-1.00px] mb-[-1.00px] mr-[-1.00px]"}
                          border border-solid border-transparent shadow-[inset_0px_0px_8.34px_#00a6ffcc] 
                          [border-image:linear-gradient(360deg,rgba(32,143,255,1)_0%,rgba(36,145,255,0)_100%)_1] 
                          [background:linear-gradient(0deg,rgba(17,72,173,0.6)_0%,rgba(5,48,126,0)_100%)]`}
                      >
                        <div className="inline-flex items-center gap-2.5">
                          <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#00c7ff] text-sm leading-4 whitespace-nowrap tracking-[0]">
                            {gun.name === 'CC1CS1' ? 'A枪' : 'B枪'}
                          </div>
                        </div>
                        <div className="inline-flex items-center gap-2.5 flex-1 grow">
                          <div className="w-fit [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-xs leading-[14px] whitespace-nowrap tracking-[0]">
                            {getGunCCCSText(gun.value) || '--'}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
