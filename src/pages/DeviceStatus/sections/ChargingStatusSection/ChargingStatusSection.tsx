import { Card, CardContent } from "@/components/ui/card";

interface DeviceItem {
  name: string;
  description: string;
  value: number;
  lastValue: number | null;
}

interface TemperContrDevice {
  device: {
    name: string;
    deviceID: string;
    type: string;
    status: string;
    lastStatus: string;
  };
  itemList: DeviceItem[];
}

export const ChargingStatusSection = ({ type, data = [] }: any): JSX.Element => {
  // 处理设备数据
  data.sort?.((a: any, b: any) => a.device?.name?.localeCompare?.(b.device?.name))
  const units = data.map?.((d: any, index: number) => {
    const isOnline = d.device.status === 'Connect';

    return {
      id: d.device.deviceID,
      name: `${index + 1}#机组`,
      status: isOnline ? "在线" : "离线",
      statusColor: isOnline ? "#00ff50" : "#4365a9",
      borderColor: isOnline ? "#39abff" : "#4365a9",
      textColor: isOnline ? "#00c7ff" : "#4365a9",
      iconSrc: "/device/frame-627003.svg",
      isOnline,
      items: d.itemList || []
    };
  });

  return (
    <div className="relative bg-[url('/device/rectangle-1382-2.svg')] bg-no-repeat w-[423px]"
      style={{
        maxHeight: type === 'short' ? '300px' : '700px',
        backgroundImage: type === 'short' ? "url('/device/rectangle-1382.svg')" : `url('/device/rectangle-1382-2.svg')`,
      }}
    >
      {/* <div className="relative bg-[url('/device/rectangle-1382-1.svg')] bg-no-repeat w-[423px] h-[700px]"> */}
      <img
        className="absolute w-1.5 h-5 top-[34px] right-0"
        alt="Rectangle"
        src={"/device/rectangle-1409.svg"}
      />

      <img
        className="absolute w-1.5 h-5 top-[34px] left-0"
        alt="Rectangle"
        src="/device/rectangle-1420.svg"
      />

      {/* Title section */}
      <div className="absolute w-[196px] h-[43px] top-0 left-1/2 -translate-x-1/2">
        <div className="relative w-[194px] h-[43px]">
          <div className="top-[9px] left-[60px] absolute [text-shadow:0px_0px_12px_#0f7cfb66] text-[#56B2F3] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[22px] text-center tracking-[0] leading-6 whitespace-nowrap">
            水冷机组
          </div>

          <div className="absolute w-[194px] h-[43px] top-0 left-0">
            <div className="relative w-[200px] h-[45px] -top-0.5 left-[-3px]">
              <img
                className="absolute w-[193px] h-[42px] top-[3px] left-[3px]"
                alt="Vector"
                src="/device/vector-113.svg"
              />

              <div className="absolute w-3 h-2.5 top-[35px] left-2.5">
                <img
                  className="absolute w-[18px] h-4 -top-0.5 left-[-3px]"
                  alt="Group"
                  src="/device/rect-lb.png"
                />
              </div>

              <div className="absolute w-3 h-2.5 top-[35px] left-[177px] rotate-180">
                <img
                  className="absolute w-[18px] h-4 -top-1 left-[-3px] -rotate-180"
                  alt="Group"
                  src="/device/rect-rb.png"
                />
              </div>

              <img
                className="absolute w-[74px] h-px top-0.5 left-[15px]"
                alt="Group"
                src="/device/group-line.png"
              />

              <img
                className="absolute w-[74px] h-px top-11 left-[103px]"
                alt="Group"
                src="/device/group-line.png"
              />

              <img
                className="absolute w-[200px] h-[19px] top-0 left-0"
                alt="Group"
                src="/device/group-365-1.png"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Cards container overflow-y-auto */}
      <div className="flex flex-col items-start gap-2 mx-auto w-[90%] mt-[55px]  pr-1.5"
        style={{
          maxHeight: type === 'short' ? '300px' : '700px',
          scrollbarWidth: 'thin',
          scrollbarColor: '#39abff #001857',
          paddingBottom: '100px',
        }}
      >
        {units.map((unit: any) => (
          <Card
            key={unit.id}
            className={`w-full border-[3px] border-solid border-[${unit.borderColor}] p-0`}
          >
            <CardContent className="p-3">
              <div className="flex items-center gap-1 w-full">
                <img
                  className="w-5 h-5"
                  alt={`${unit.name} icon`}
                  src="/device/frame-627003.svg"
                />

                <div className="flex items-center gap-2.5 flex-1">
                  <div
                    className={`w-fit mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[${unit.textColor}] text-base leading-[18px] whitespace-nowrap tracking-[0]`}
                  >
                    {unit.name}
                  </div>
                </div>

                <div className="inline-flex items-center gap-1">
                  <div
                    className={`w-1.5 h-1.5 bg-[${unit.statusColor}] rounded-[3px]`}
                  />
                  <div className="inline-flex items-center gap-2.5 rounded-[10px]">
                    <div
                      className={`w-fit mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[${unit.statusColor}] text-sm leading-4 whitespace-nowrap tracking-[0]`}
                    >
                      {unit.status}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col items-start justify-center gap-1.5 px-0 py-2 w-full">
                {unit.items?.map?.((item: DeviceItem) => (
                  <div key={item.name} className="flex items-center justify-between w-full">
                    <div
                      className={`[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[${unit.textColor}] text-base leading-[18px]`}
                    >
                      {item.description}:
                    </div>
                    <div
                      className={`[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[${unit.textColor}] text-base leading-[18px]`}
                    >
                      {item.value}{item.name === 'EffluentTemperature' ? '℃' : ''}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
