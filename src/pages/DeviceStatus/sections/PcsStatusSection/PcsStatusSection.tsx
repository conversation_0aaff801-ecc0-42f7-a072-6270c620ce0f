import { useCallback, useEffect, useRef, useState } from 'react';
import { Modal, Input, message, Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import Keyboard from 'react-simple-keyboard';
import { httpPost } from '@/shared/http';

import { useDeviceData, DeviceConfig } from '../../hooks/useDeviceData';
import { useContentDictionary } from '../../hooks/useContentDictionary';
import { PcsCard } from '../../components/PcsCard';
import { PcsDetailModal } from '../../components/PcsDetailModal';
import { PcsOverviewModal } from '../../components/PcsOverviewModal';
import { SectionHeader } from '../../components/SectionHeader/SectionHeader';
import { DeviceType } from '../../types';

// 开发模式：是否使用本地测试数据
const DEV_MODE = false; // 设置为 false 可以使用真实接口数据
const TYPE = DeviceType.PCS;
export const PcsStatusSection = ({ 
  data = [], 
  config
}: { 
  data: any[], 
  config?: DeviceConfig
}): JSX.Element => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isOverviewModalOpen, setIsOverviewModalOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [currentDevice, setCurrentDevice] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);

  const { buildDeviceCardData } = useDeviceData();
  const { getContentValue } = useContentDictionary();

  // 处理设备数据
  const pcsConfig = config || undefined;
  const pcsData = pcsConfig ? buildDeviceCardData(data, pcsConfig) : [];
  
  // 处理并机通讯ID点击事件
  const handleCommIdClick = useCallback((device: any) => {
    // 找到并机通讯ID属性
    const commIdProp = device.properties.find((p: any) => p.name === 'PowerOn_Comm_ID');
    const commIdValue = commIdProp?.value?.toString() || '';
    
    setCurrentDevice(device);
    setInputValue(commIdValue);
    setIsModalVisible(true);
  }, []);
  
  // 处理详情点击
  const handleDetailClick = useCallback((device: any) => {
    // 打开详情弹窗
    setCurrentDevice(device);
    setIsDetailModalOpen(true);
  }, []);

  // 处理确认设置
  const handleOk = async () => {
    if (!currentDevice) return;
    const value = parseInt(inputValue);
    if (isNaN(value)) {
      message.error('请输入有效的数字');
      return;
    } else if (value < 0 || value > 6) {
      message.error('请输入0-6的数值');
      return;
    }

    setLoading(true);
    try {
      const params = {
        devices: [
          {
            msgId: "00001",
            deviceCode: currentDevice.deviceCode,
            addresses: [
              {
                name: "PowerOn_Comm_ID",
                value: value
              }
            ]
          }
        ]
      };

      const res = await httpPost('/api/RootInterface/WriteCommand', params);

      if (res.code === 0) {
        message.success('设置成功');
        setIsModalVisible(false);
        setInputValue('');
        setCurrentDevice(null);
      } else {
        message.error(res.message || '设置失败');
      }
    } catch (error) {
      message.error('设置失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setInputValue('');
    setCurrentDevice(null);
    keyboardRef.current?.setInput("");
  };

  const handleInputFocus = () => {
    setShowKeyboard(true);
    keyboardRef.current?.setInput("");
  };

  const handleInput = useCallback((v: string) => {
    setInputValue(v);
  }, []);

  const handleKeyboardInput = useCallback((input: string) => {
    handleInput(input);
  }, [handleInput]);

  // 处理设备卡片点击
  // const handleCardClick = useCallback((device: any) => {
  //   // 找到并机通讯ID属性
  //   const commIdProp = device.properties.find((p: any) => p.name === 'PowerOn_Comm_ID');
  //   if (commIdProp) {
  //     handleCommIdClick(device);
  //   }
  // }, [handleCommIdClick]);

  // 处理"总览"点击
  const handleOverviewClick = useCallback(() => {
    // 打开总览弹窗
    setIsOverviewModalOpen(true);
  }, []);
  
  // 转换处理属性数据用于显示
  const processCardProperties = useCallback((device: any) => {
    const processed = [];
    // 运行状态
    const operationStatusProp = device.properties.find((p: any) => p.name === 'PCS_Operation_Status');
    if (operationStatusProp) {
      const statusValue = operationStatusProp.viewType === 'Content' 
        ? getContentValue(TYPE, operationStatusProp.name, operationStatusProp.value)
        : operationStatusProp.value;
        
      processed.push({
        name: operationStatusProp.name,
        label: '',
        value: statusValue
      });
    }
    
    // 并机通讯ID
    const commIdProp = device.properties.find((p: any) => p.name === 'PowerOn_Comm_ID');
    if (commIdProp) {
      const commIdValue = commIdProp.viewType === 'Content'
        ? getContentValue(TYPE, commIdProp.name, commIdProp.value)
        : commIdProp.value;
        
      processed.push({
        name: commIdProp.name,
        label: '并机ID',
        value: commIdValue
      });
    }
    
    return processed;
  }, [getContentValue]);

  return (
    <div className="w-[281px] h-[708px] relative">
      <div className="relative h-[708px]">
        <img
          className="absolute w-[280px] h-[778px] top-0 left-0"
          alt="Rectangle"
          src="/device/rectangle-1382-3.svg"
        />

        <img
          className="absolute w-1.5 h-5 top-[34px] left-[274px]"
          alt="Rectangle"
          src="/device/rectangle-1409.svg"
        />

        <img
          className="absolute w-1.5 h-5 top-[34px] left-0"
          alt="Rectangle"
          src="/device/rectangle-1420.svg"
        />

        <SectionHeader 
          title={pcsConfig?.Title || "PCS"} 
          label={pcsConfig?.Label}
          onLabelClick={handleOverviewClick}
        />

        <div className="flex flex-col w-[245px] items-start gap-2 absolute top-[66px] left-[18px] max-h-[708px] overflow-y-auto pr-1.5"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#39abff #001857',
            paddingBottom: '100px',
          }}
        >
          {pcsData.map((device: any) => (
            <PcsCard
              key={device.id}
              name={device.name}
              status={device.status}
              statusText={device.statusText}
              statusColor={device.statusColor}
              textColor={device.textColor}
              borderColor={device.borderColor}
              properties={processCardProperties(device)}
              hasAlarm={device.hasAlarm}
              onDetailClick={ () => handleDetailClick(device) }
              onCommIdClick={ () => handleCommIdClick(device) }
            />
          ))}
        </div>
      </div>

      {/* 并机通讯ID设置弹窗 */}
      <Modal
        title="设置并机通讯ID"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
        okText="确定"
        cancelText="取消"
        className="custom-modal"
        width={900}
        styles={{ body: { height: '400px', overflow: 'hidden' } }}
      >
        <>
          <div className="space-y-4">
            <div className="text-gray-600">
              请输入PCS的并机通讯ID：
            </div>
            <Input
              type="number"
              min={0}
              max={6}
              value={inputValue}
              onChange={(e) => handleInput(e.target.value)}
              placeholder="请输入0~6的数值"
              className="w-full"
              onFocus={handleInputFocus}
            />
            {currentDevice && (
              <div className="text-sm text-gray-500">
                当前设备：{currentDevice.name}，请输入0~6的数值
              </div>
            )}
          </div>
          {showKeyboard && (
            <div className="absolute bottom-16 left-0 w-full z-50 bg-[#1B53B7]"
              onTouchStart={(e) => e.stopPropagation()}
              onTouchMove={(e) => e.preventDefault()}
            >
              <div className="flex justify-end p-2">
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  onClick={() => setShowKeyboard(false)}
                  style={{ color: '#fff',fontSize:'22px' }}
                />
              </div>
              <Keyboard
                keyboardRef={(r) => (keyboardRef.current = r)}
                layout={{
                  default: ["1 2 3", "4 5 6", "7 8 9", ". 0 {bksp}"]
                }}
                onChange={handleKeyboardInput}
                theme="hg-theme-default custom-keyboard"
                useTouchEvents={true}
                disableCaretPositioning={true}
              />
            </div>
          )}
        </>
      </Modal>

      {/* PCS详情弹窗 */}
      {currentDevice && (
        <PcsDetailModal
          open={isDetailModalOpen}
          onOpenChange={setIsDetailModalOpen}
          deviceCode={currentDevice.deviceCode}
          deviceName={currentDevice.name}
        />
      )}

      {/* PCS总览弹窗 */}
      <PcsOverviewModal
        open={isOverviewModalOpen}
        onOpenChange={setIsOverviewModalOpen}
      />
    </div>
  );
};
