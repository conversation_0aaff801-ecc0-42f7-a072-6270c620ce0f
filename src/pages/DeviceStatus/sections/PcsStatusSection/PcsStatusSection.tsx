import { Card, CardContent } from "@/components/ui/card";
import { getPcsRunStateLabel } from '@/constants/device';
import { useCallback, useRef, useState } from 'react';
import { Modal, Input, message, Button } from 'antd';
import { httpPost } from '@/shared/http';
import { CloseOutlined } from '@ant-design/icons';
import Keyboard from 'react-simple-keyboard';

// 开发模式：是否使用本地测试数据
const DEV_MODE = false; // 设置为 false 可以使用真实接口数据

// 本地测试数据（从test/device.json提取的PCS数据）
const mockPcsData = [
  {
    device: {
      name: "PCS1",
      deviceID: "PCS1",
      type: "PCS",
      status: "Connect",
      lastStatus: "Disconnect"
    },
    itemList: [
      {
        name: "PCS_Operation_Status",
        description: "PCS 运行状态",
        value: 0,
        lastValue: null
      },
      {
        name: "PowerOn_Comm_ID",
        description: "并机通讯ID",
        value: 101,
        lastValue: null
      }
    ]
  },
  {
    device: {
      name: "PCS2",
      deviceID: "PCS2",
      type: "PCS",
      status: "Connect",
      lastStatus: "Disconnect"
    },
    itemList: [
      {
        name: "PCS_Operation_Status",
        description: "PCS 运行状态",
        value: 0,
        lastValue: null
      },
      {
        name: "PowerOn_Comm_ID",
        description: "并机通讯ID",
        value: 102,
        lastValue: null
      }
    ]
  },
  {
    device: {
      name: "PCS3",
      deviceID: "PCS3",
      type: "PCS",
      status: "Connect",
      lastStatus: "Disconnect"
    },
    itemList: [
      {
        name: "PCS_Operation_Status",
        description: "PCS 运行状态",
        value: 0,
        lastValue: null
      },
      {
        name: "PowerOn_Comm_ID",
        description: "并机通讯ID",
        value: 103,
        lastValue: null
      }
    ]
  },
  {
    device: {
      name: "PCS4",
      deviceID: "PCS4",
      type: "PCS",
      status: "Connect",
      lastStatus: "Disconnect"
    },
    itemList: [
      {
        name: "PCS_Operation_Status",
        description: "PCS 运行状态",
        value: 256,
        lastValue: null
      },
      {
        name: "PowerOn_Comm_ID",
        description: "并机通讯ID",
        value: 104,
        lastValue: null
      }
    ]
  },
  {
    device: {
      name: "PCS5",
      deviceID: "PCS5",
      type: "PCS",
      status: "Connect",
      lastStatus: "Disconnect"
    },
    itemList: [
      {
        name: "PCS_Operation_Status",
        description: "PCS 运行状态",
        value: 256,
        lastValue: null
      },
      {
        name: "PowerOn_Comm_ID",
        description: "并机通讯ID",
        value: 105,
        lastValue: null
      }
    ]
  },
  {
    device: {
      name: "PCS6",
      deviceID: "PCS6",
      type: "PCS",
      status: "Connect",
      lastStatus: "Disconnect"
    },
    itemList: [
      {
        name: "PCS_Operation_Status",
        description: "PCS 运行状态",
        value: 0,
        lastValue: null
      },
      {
        name: "PowerOn_Comm_ID",
        description: "并机通讯ID",
        value: 106,
        lastValue: null
      }
    ]
  }
];

export const PcsStatusSection = ({ data = [] }: any): JSX.Element => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [currentDevice, setCurrentDevice] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);


  // 从接口数据中获取运行状态
  const getDeviceMode = (deviceData: any) => {
    const runState = deviceData?.itemList?.find((item: any) => item.name === 'PCS_Operation_Status');
    return getPcsRunStateLabel(runState?.value);
  };

  // 获取并机通讯ID
  const getPowerOnCommId = (deviceData: any) => {
    const powerOnCommId = deviceData?.itemList?.find((item: any) => item.name === 'PowerOn_Comm_ID');
    return powerOnCommId?.value ?? '-';
  };

  // 处理设备数据
  const processData = DEV_MODE ? mockPcsData : data;
  const pcsData = processData.map?.((item: any, index: number) => {
    const isOnline = item?.device?.status === "Connect";
    const obj = {
      id: index + 1,
      name: item.device.name,
      deviceCode: item.device.name || item.device.deviceID,
      status: isOnline ? "online" : "offline",
      statusColor: isOnline ? "#00ff50" : "#4365a9",
      statusText: isOnline ? "在线" : "离线",
      mode: getDeviceMode(item),
      powerOnCommId: getPowerOnCommId(item),
      borderColor: isOnline ? "#39abff" : "#4365a9",
      textColor: isOnline ? "#00c7ff" : "#4365a9",
      rawData: item
    }
    return obj;
  })?.sort?.((a: any, b: any) => a.name?.localeCompare?.(b.name)) || [];

  // 处理点击并机通讯ID区域
  const handleCommIdClick = (pcs: any) => {
    setCurrentDevice(pcs);
    setInputValue(pcs.powerOnCommId?.toString?.() || '');
    setIsModalVisible(true);
  };

  // 处理确认设置
  const handleOk = async () => {
    if (!currentDevice) return;
    const value = parseInt(inputValue);
    if (isNaN(value)) {
      message.error('请输入有效的数字');
      return;
    } else if (value < 0 || value > 6) {
      message.error('请输入0-6的数值');
      return;
    }

    setLoading(true);
    try {
      const params = {
        devices: [
          {
            msgId: "00001",
            deviceCode: currentDevice.deviceCode,
            addresses: [
              {
                name: "PowerOn_Comm_ID",
                value: value
              }
            ]
          }
        ]
      };

      const res = await httpPost('/api/RootInterface/WriteCommand', params);

      if (res.code === 0) {
        message.success('设置成功');
        setIsModalVisible(false);
        setInputValue('');
        setCurrentDevice(null);
      } else {
        message.error(res.message || '设置失败');
      }
    } catch (error) {
      message.error('设置失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setInputValue('');
    setCurrentDevice(null);
    keyboardRef.current?.setInput("");
  };


  const handleInputFocus = () => {
    setShowKeyboard(true);
    keyboardRef.current?.setInput("");
  };

  const handleInput = useCallback((v: string) => {
    setInputValue(v);
  }, []);

  const handleKeyboardInput = useCallback((input: string) => {
    handleInput(input);
  }, [handleInput]);


  return (
    <div className="w-[281px] h-[708px] relative">
      <div className="relative h-[708px]">
        <img
          className="absolute w-[280px] h-[778px] top-0 left-0"
          alt="Rectangle"
          src="/device/rectangle-1382-3.svg"
        />

        <img
          className="absolute w-1.5 h-5 top-[34px] left-[274px]"
          alt="Rectangle"
          src="/device/rectangle-1409.svg"
        />

        <img
          className="absolute w-1.5 h-5 top-[34px] left-0"
          alt="Rectangle"
          src="/device/rectangle-1420.svg"
        />

        <div className="absolute w-[196px] h-[43px] top-0 left-10">
          <div className="relative w-[200px] h-[45px] -top-0.5 left-[-3px]">
            <div className="top-[10px] left-[74px] absolute [text-shadow:0px_0px_12px_#0f7cfb66] text-[#56B2F3] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[22px] tracking-[0] leading-6 whitespace-nowrap">
              PCS
            </div>
            <img
              className="absolute w-[193px] h-[42px] top-[3px] left-[3px]"
              alt="Vector"
              src="/device/vector-113.svg"
            />

            <div className="absolute w-3 h-2.5 top-[35px] left-2.5">
              <img
                className="absolute w-[18px] h-4 -top-0.5 left-[-3px]"
                alt="Group"
                src="/device/rect-lb.png"
              />
            </div>

            <div className="absolute w-3 h-2.5 top-[35px] left-[177px] rotate-180">
              <img
                className="absolute w-[18px] h-4 -top-1 left-[-3px] -rotate-180"
                alt="Group"
                src="/device/rect-rb.png"
              />
            </div>

            <img
              className="absolute w-[74px] h-px top-0.5 left-[15px]"
              alt="Group"
              src="/device/group-line.png"
            />

            <img
              className="absolute w-[74px] h-px top-11 left-[103px]"
              alt="Group"
              src="/device/group-line.png"
            />

            <img
              className="absolute w-[200px] h-[19px] top-0 left-0"
              alt="Group"
              src="/device/group-365-1.png"
            />
          </div>
        </div>

        <div className="flex flex-col w-[245px] items-start gap-2 absolute top-[68px] left-[18px] max-h-[708px] overflow-y-auto pr-1.5"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#39abff #001857',
            paddingBottom: '100px',
          }}
        >
          {pcsData.map((pcs: any) => (
            <Card
              key={pcs.id}
              className={`h-[90px] min-h-[90px] flex flex-col items-start gap-1 p-3 relative w-full border-[3px] border-solid border-[${pcs.borderColor}] rounded-none shadow-none`}
            >
              <div className="flex items-center gap-0.5 relative  w-full flex-[0_0_auto]">
                <img
                  className="relative w-[26px] h-6"
                  alt="Frame"
                  src="/device/frame-627003-3.svg"
                />

                <div className="flex items-center gap-2.5 relative flex-1 grow">
                  <div
                    className={`w-fit mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[${pcs.textColor}] text-base leading-[18px] whitespace-nowrap relative tracking-[0]`}
                  >
                    {/* {pcs.id}#PCS */}
                    {pcs.name}
                  </div>
                </div>

                <div className="inline-flex items-center gap-1 relative flex-[0_0_auto]">
                  <div
                    className={`relative w-1.5 h-1.5 bg-[${pcs.statusColor}] rounded-[3px]`}
                  />

                  <div className="inline-flex items-center gap-2.5 relative flex-[0_0_auto] rounded-[10px]">
                    <div
                      className={`w-fit mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[${pcs.statusColor}] text-sm leading-4 whitespace-nowrap relative tracking-[0]`}
                    >
                      {pcs.statusText}
                    </div>
                  </div>
                </div>
              </div>

              <CardContent className="flex-col items-center justify-center gap-1.5 px-0 py-2 flex-1 w-full grow flex relative  p-0">
                <div className="flex items-center justify-center gap-1 relative  w-full flex-[0_0_auto]">
                  <div className="inline-flex items-center gap-2.5 relative flex-[0_0_auto]">
                    <div
                      className={`w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[${pcs.textColor}] text-base leading-[18px] whitespace-nowrap relative tracking-[0]`}
                    >
                      {pcs.mode}
                    </div>
                  </div>
                </div>

                {/* 并机通讯ID显示区域 */}
                <div
                  className="flex items-center justify-center gap-1 relative w-full flex-[0_0_auto] cursor-pointer hover:bg-blue-500/10 rounded p-1 transition-colors"
                  onClick={() => handleCommIdClick(pcs)}
                >
                  <div className="inline-flex items-center gap-2.5 relative flex-[0_0_auto]">
                    <div
                      className={`w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[${pcs.textColor}] text-sm leading-4 whitespace-nowrap relative tracking-[0]`}
                    >
                      并机通讯ID: {pcs.powerOnCommId }
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
      <Modal
        title="设置并机通讯ID"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
        okText="确定"
        cancelText="取消"
        className="custom-modal"
        width={900}
        styles={{ body: { height: '400px', overflow: 'hidden' } }}
      >
        <>
          <div className="space-y-4">
            <div className="text-gray-600">
              请输入PCS的并机通讯ID：
            </div>
            <Input
              type="number"
              min={0}
              max={6}
              value={inputValue}
              onChange={(e) => handleInput(e.target.value)}
              placeholder="请输入0~6的数值"
              className="w-full"
              onFocus={handleInputFocus}
            />
            {currentDevice && (
              <div className="text-sm text-gray-500">
                当前设备：{currentDevice.name}，请输入0~6的数值
              </div>
            )}
          </div>
          {showKeyboard && (
            <div className="absolute bottom-16 left-0 w-full z-50 bg-gray-100"
              onTouchStart={(e) => e.stopPropagation()}
              onTouchMove={(e) => e.preventDefault()}
            >
              <div className="flex justify-end p-2">
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  onClick={() => setShowKeyboard(false)}
                  style={{ color: '#000' }}
                />
              </div>
              <Keyboard
                keyboardRef={(r) => (keyboardRef.current = r)}
                layout={{
                  default: ["1 2 3", "4 5 6", "7 8 9", ". 0 {bksp}"]
                }}
                onChange={handleKeyboardInput}
                theme="hg-theme-default custom-keyboard"
                useTouchEvents={true}              // 启用触摸事件支持
                // useMouseEvents={true}              // 启用鼠标事件
                disableCaretPositioning={true}     // 禁用光标定位，避免触摸冲突
              />
            </div>
          )}
        </>
      </Modal>
    </div >
  );
};
