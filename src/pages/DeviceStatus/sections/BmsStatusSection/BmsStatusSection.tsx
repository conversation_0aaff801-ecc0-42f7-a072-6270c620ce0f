import { useCallback, useState } from 'react';
import { useDeviceData, DeviceConfig } from '../../hooks/useDeviceData';
import { useContentDictionary } from '../../hooks/useContentDictionary';
import { SectionHeader } from '../../components/SectionHeader/SectionHeader';
import { BmsCard, BatteryBranch } from '../../components/BmsCard';
import { BmsDetailModal } from '../../components/BmsDetailModal';
import { DeviceType } from '../../types';

// BMS组件属性定义
interface BmsStatusSectionProps {
  data: any[];
  config?: DeviceConfig;
}
const TYPE = DeviceType.BMS;
export const BmsStatusSection = ({ data = [], config }: BmsStatusSectionProps): JSX.Element => {
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [currentDevice, setCurrentDevice] = useState<any>(null);
  
  const { buildDeviceCardData } = useDeviceData();
  const { getContentValue } = useContentDictionary();

  // 处理设备数据
  const bmsConfig = config || undefined;
  const bmsData = bmsConfig ? buildDeviceCardData(data, bmsConfig) : [];
  
  // 处理详情点击
  const handleDetailClick = useCallback((id: number) => {
    // 找到对应的设备
    const device = bmsData.find(bms => bms.id === id);
    if (device) {
      setCurrentDevice(device);
      setIsDetailModalOpen(true);
    }
  }, [bmsData]);

  // 处理"总览"点击
  const handleOverviewClick = () => {
    console.log('跳转到总览页面');
  };

  // 处理电池支路组数据
  const processCardProperties = (device: any): BatteryBranch[] => {
    if (!device || !bmsConfig || device.status !== 'online') return [];

    const batteryBranches: BatteryBranch[] = [];

    // 获取当前设备的IDList
    const deviceConfig = bmsConfig.deviceID.find((dev: any) => dev.Code === device.deviceCode);
    const deviceIDList = deviceConfig?.IDList || [];
    
    // 如果设备没有IDList或IDList为空，则返回空数组
    if (!deviceIDList.length) return [];

    // 遍历配置中的ItemList，找到具有Group的项并且ID在deviceIDList中的项
    bmsConfig.ItemList.forEach(item => {
      // 只处理ID在设备的IDList中的项
      if (item.ID && deviceIDList.includes(item.ID) && item.Group && item.Group.length > 0) {
        const branchData: BatteryBranch = {
          name: item.Descript || '', // 如果Descript为空，使用空字符串
          properties: item.Group.map(groupItem => {
            // 查找设备中对应的数据项
            const dataItem = device.rawData?.itemList.find((di: any) => di.name === groupItem.Name);
            let value = dataItem?.value;
            
            // 如果ViewType为Content，则从数据字典获取真正的值
            if (groupItem.ViewType === 'Content') {
              value = getContentValue(TYPE, groupItem.Name, value);
            }

            return {
              name: groupItem.Name,
              label: groupItem.Descript,
              value: value !== undefined && value !== null ? value : '-',
              unit: groupItem.Unit || '',
              viewType: groupItem.ViewType || '',
              progress: groupItem.ViewType === 'progressAndLabel' ? value : null
            };
          })
        };
        
        batteryBranches.push(branchData);
      }
    });

    return batteryBranches;
  };

  return (
    <section className="bg-[url(/device/rectangle-1382-2.svg)] bg-no-repeat w-[521px] h-[778px]">
      <div className="relative w-full">
        <img
          className="absolute w-1.5 h-5 top-[34px] right-0"
          alt="Rectangle"
          src="/device/rectangle-1409.svg"
        />

        <img
          className="absolute w-1.5 h-5 top-[34px] left-0"
          alt="Rectangle"
          src="/device/rectangle-1420.svg"
        />

        <SectionHeader 
          title={bmsConfig?.Title || "BMS"} 
          label={bmsConfig?.Label}
          onLabelClick={handleOverviewClick}
        />

        <div className="w-full flex flex-col items-start gap-[8px] py-2 px-4 mt-3 max-h-[708px] overflow-y-auto"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#39abff #001857',
            paddingBottom: '100px',
          }}
        >
          {bmsData.map((bms) => (
            <BmsCard
              key={bms.id}
              id={bms.id}
              name={bms.name}
              status={bms.status}
              statusText={bms.statusText}
              statusColor={bms.statusColor}
              textColor={bms.textColor}
              borderColor={bms.borderColor}
              hasAlarm={bms.hasAlarm}
              batteryBranches={processCardProperties(bms)}
              onDetailClick={handleDetailClick}
            />
          ))}
        </div>
      </div>

      {/* BMS详情弹窗 */}
      {currentDevice && (
        <BmsDetailModal
          open={isDetailModalOpen}
          onOpenChange={setIsDetailModalOpen}
          deviceCode={currentDevice.deviceCode}
          deviceName={currentDevice.name}
        />
      )}
    </section>
  );
};
