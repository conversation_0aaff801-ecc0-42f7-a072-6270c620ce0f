import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";

// BMS data structure
type BatteryCluster = {
  name: string;
  status: string;
  soc: number;
  soh: number;
};

type BmsUnit = {
  id: number;
  name: string;
  online: boolean;
  clusters: BatteryCluster[] | null;
};

interface DeviceItem {
  name: string;
  description: string;
  value: number;
  lastValue: number | null;
}

interface BmsDevice {
  device: {
    name: string;
    deviceID: string;
    type: string;
    status: string;
    lastStatus: string;
  };
  itemList: DeviceItem[];
}

export const BmsStatusSection = ({ data = [] }: { data: BmsDevice[] }): JSX.Element => {
  // 处理BMS数据
  const bmsUnits: BmsUnit[] = data.map((bmsData, index) => {
    // 获取电池簇数据
    const clusters: BatteryCluster[] = [];
    const batNumbers = new Set<number>();

    // 从itemList中提取电池簇编号
    bmsData.itemList.forEach(item => {
      const match = item.name.match(/\d+$/);
      if (match) {
        batNumbers.add(Number(match[0]));
      }
    });

    // 为每个电池簇整理数据
    batNumbers.forEach(batNum => {
      const soc = bmsData.itemList.find(item => item.name === `SOCOfBat${batNum}`)?.value || 0;
      const soh = bmsData.itemList.find(item => item.name === `SOHOfBat${batNum}`)?.value || 0;

      clusters.push({
        name: `${batNum}#电池簇`,
        status: "待机", // 这里可以根据实际状态判断逻辑修改
        soc,
        soh
      });
    });

    return {
      id: index + 1,
      name: `${bmsData.device.name}`,
      online: bmsData.device.status === "Connect",
      clusters: clusters.length > 0 ? clusters : null
    };
  })?.sort?.((a: any, b: any) => a.name?.localeCompare?.(b.name));

  return (
    <section className="bg-[url(/device/rectangle-1382-2.svg)] bg-no-repeat w-[521px] h-[778px]">
      <div className="relative w-full">
        <img
          className="absolute w-1.5 h-5 top-[34px] right-0"
          alt="Rectangle"
          src="/device/rectangle-1409.svg"
        />

        <img
          className="absolute w-1.5 h-5 top-[34px] left-0"
          alt="Rectangle"
          src="/device/rectangle-1420.svg"
        />

        <div className="mx-auto w-[196px] h-[43px]">
          <div className="relative w-[194px] h-[43px]">
            <div className="top-[9px] left-[74px] absolute [text-shadow:0px_0px_12px_#0f7cfb66] text-[#56B2F3] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[22px] tracking-[0] leading-6 whitespace-nowrap">
              BMS
            </div>

            <div className="absolute w-[194px] h-[43px] top-0 left-0">
              <div className="relative w-[200px] h-[45px] -top-0.5 left-[-3px]">
                <img
                  className="absolute w-[193px] h-[42px] top-[3px] left-[3px]"
                  alt="Vector"
                  src="/device/vector-113.svg"
                />

                <div className="absolute w-3 h-2.5 top-[35px] left-2.5">
                  <img
                    className="absolute w-[18px] h-4 -top-0.5 left-[-3px]"
                    alt="Group"
                    src="/device/rect-lb.png"
                  />
                </div>

                <div className="absolute w-3 h-2.5 top-[35px] left-[177px] rotate-180">
                  <img
                    className="absolute w-[18px] h-4 -top-1 left-[-3px] -rotate-180"
                    alt="Group"
                    src="/device/rect-rb.png"
                  />
                </div>

                <img
                  className="absolute w-[74px] h-px top-0.5 left-[15px]"
                  alt="Group"
                  src="/device/group-line.png"
                />

                <img
                  className="absolute w-[74px] h-px top-11 left-[103px]"
                  alt="Group"
                  src="/device/group-line.png"
                />

                <img
                  className="absolute w-[200px] h-[19px] top-0 left-0"
                  alt="Group"
                  src="/device/group-365-1.png"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="w-full flex flex-row flex-wrap items-start gap-4 p-4 mt-3 max-h-[708px] overflow-y-auto pr-1.5"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#39abff #001857',
            paddingBottom: '100px',
          }}
        >
          {bmsUnits.map((bms) => (
            <Card
              key={bms.id}
              className={`w-[236px] max-h-[200px] border-[3px] border-solid ${bms.online ? "border-[#39abff]" : "border-[#4365a9]"}`}
            >
              <CardContent className="p-3 h-[160px]">
                <div className="flex items-center gap-0.5 w-full">
                  <img
                    className="w-5 h-5"
                    alt="Frame"
                    src="/device/frame-627003.svg"
                  />

                  <div className="flex items-center gap-2.5 flex-1">
                    <span
                      className={`mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal ${bms.online ? "text-[#00c7ff]" : "text-[#4365a9]"} text-base leading-[18px] whitespace-nowrap tracking-[0]`}
                    >
                      {bms.name}
                    </span>
                  </div>

                  <div className="flex items-center gap-0.5">
                    <div
                      className={`w-1.5 h-2 ${bms.online ? "bg-[#00ff50]" : "bg-[#4365a9]"} rounded-[3px]`}
                    />
                    <Badge
                      variant="outline"
                      className={`rounded-[10px] bg-transparent border-0 ${bms.online ? "text-[#00ff50]" : "text-[#4365a9]"} text-sm leading-4 [font-family:'PingFang_SC-Semibold',Helvetica] font-normal`}
                    >
                      {bms.online ? "在线" : "离线"}
                    </Badge>
                  </div>
                </div>

                {bms.online ? (
                  <div className="flex items-center gap-2 w-full mt-1">
                    {bms.clusters?.map((cluster, index) => (
                      <div
                        key={index}
                        className={`flex flex-col justify-center gap-2 px-3 py-[7px] h-[110px] flex-1 border border-solid border-transparent shadow-[inset_0px_0px_8.34px_#00a6ffcc] [border-image:linear-gradient(360deg,rgba(32,143,255,1)_0%,rgba(36,145,255,0)_100%)_1] [background:linear-gradient(0deg,rgba(17,72,173,0.6)_0%,rgba(5,48,126,0)_100%)] ${index === 0 ? "ml-[-1.00px]" : ""} ${index === 2 ? "mr-[-1.00px]" : ""}`}
                      >
                        <div className="flex items-start gap-0.5 w-full">
                          <div className="flex items-center gap-2.5 flex-1">
                            <span className="flex-1 mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#00c7ff] text-s leading-[14px] tracking-[0]">
                              {cluster.name}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-s leading-[14px] whitespace-nowrap tracking-[0]">
                              {/* {cluster.status} */}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center gap-1 w-full">
                          <span className="mt-1 [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-xs leading-3 whitespace-nowrap tracking-[0]">
                            SOC
                          </span>
                          <div className="flex-1 flex items-center">
                            <div
                              className={`h-2 [background:linear-gradient(95deg,rgba(104,188,52,1)_0%,rgba(129,221,75,1)_56%,rgba(255,221,54,1)_100%)]`}
                              style={{ width: `${cluster.soc}%` }}
                            />
                            <div className="flex-1 h-2 bg-[#1e4d9f]" />
                          </div>
                          <span className="mt-1 [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-xs leading-3 whitespace-nowrap tracking-[0]">
                            {cluster.soc}%
                          </span>
                        </div>

                        <div className="flex items-center gap-1 w-full">
                          <span className="mt-1 [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-xs leading-3 whitespace-nowrap tracking-[0]">
                            SOH
                          </span>
                          <div className="flex-1 flex items-center">
                            <div
                              className={`h-2 [background:linear-gradient(95deg,rgba(104,188,52,1)_0%,rgba(129,221,75,1)_56%,rgba(255,221,54,1)_100%)]`}
                              style={{ width: `${cluster.soh}%` }}
                            />
                            <div className="flex-1 h-2 bg-[#1e4d9f]" />
                          </div>
                          <span className="mt-1 [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-xs leading-3 whitespace-nowrap tracking-[0]">
                            {cluster.soh}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-[27px] w-full">
                    <span className="mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#4365a9] text-base leading-[18px] whitespace-nowrap tracking-[0]">
                      关机
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
