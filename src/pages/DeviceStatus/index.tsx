// import { Separator } from "./components/separator";
import { BmsStatusSection } from "./sections/BmsStatusSection/BmsStatusSection";
import { ChargingStatusSection } from "./sections/ChargingStatusSection";
import { CoolingSystemSection } from "./sections/CoolingSystemSection";
import { PcsStatusSection } from "./sections/PcsStatusSection/PcsStatusSection";

import { useEffect, useState } from "react";
import { httpGet, httpPost } from "@/shared/http";
import { projectType } from '@/constants';

interface MonitorRequestItem {
  deviceList: string[];
  items: string[];
}

const formatData = (data: any) => {
  const pcsList: any[] = [];
  const bmsList: any[] = [];
  const temperContrList: any[] = [];
  const chargeStationList: any[] = [];
  data.forEach((item: any) => {
    if (item.device.type === 'PCS') {
      pcsList.push(item);
    } else if (item.device.type === 'BMS') {
      bmsList.push(item);
    } else if (item.device.type === 'TemperContr') {
      temperContrList.push(item);
    } else if (item.device.type === 'DCChargingStation') {
      chargeStationList.push(item);
    }
  });
  return {
    PCS: pcsList,
    BMS: bmsList,
    chargeStationList: chargeStationList,
    TemperContr: temperContrList
  }
}

export const DeviceStatus = (): JSX.Element => {
  const [deviceValues, setDeviceValues] = useState<any>({});

  useEffect(() => {
    const fetchDeviceStatus = async () => {
      try {
        // 获取监控配置
        const config = await httpGet('/api/Ems/Config/GetEMSRequestConfig');
        // console.log(1, config, config?.data?.[0]?.MonitorRequest)
        // 转换配置为请求参数格式
        const monitorRequest: MonitorRequestItem[] = Object.entries(config?.data?.[0]?.MonitorRequest || {}).map(([_, value]: any) => ({
          deviceList: value.deviceID || [],
          items: value.ItemList || []
        }));
        // 获取设备状态数据
        const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', monitorRequest);
        if (res.code === 0) {
          const v = formatData(res.data || []);
          setDeviceValues(v);
        }
      } catch (error) {
        console.error('获取设备状态失败:', error);
      }
    };

    fetchDeviceStatus();
    // 设置定时刷新
    const timer = setInterval(fetchDeviceStatus, 5000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="bg-[#001857] flex flex-row justify-center w-full max-h-[800px] overflow-hidden">
      <div className="bg-[#001857] w-full max-w-[1280px] relative flex flex-col">
        <main className="flex flex-col w-full p-4">
          <div className="flex gap-3 w-full">
            <PcsStatusSection data={deviceValues?.PCS} />
            <BmsStatusSection data={deviceValues?.BMS} />
            {['jiangxi', 'liyuan', 'lafangda'].includes(projectType) ? <>
              <div className="flex flex-col gap-4">
                {/* 充电桩 */}
                <CoolingSystemSection data={deviceValues?.chargeStationList} />
                {/* 水冷机组 */}
                <ChargingStatusSection data={deviceValues?.TemperContr} type="short" />
              </div>
            </> :
              <ChargingStatusSection data={deviceValues?.TemperContr} />
            }
          </div>
        </main>
      </div >
    </div >
  );
};
