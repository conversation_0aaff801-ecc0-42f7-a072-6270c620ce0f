// import { Separator } from "./components/separator";
import { BmsStatusSection } from "./sections/BmsStatusSection";
import { ChargingStationSection } from "./sections/ChargingStationSection";
import { CoolingUnitSection } from "./sections/CoolingUnitSection";
import { PcsStatusSection } from "./sections/PcsStatusSection/PcsStatusSection";
import { ModalConfigContext } from "./context/ModalConfigContext";

import { useEffect, useState } from "react";
import { httpGet, httpPost } from "@/shared/http";
import { DeviceType } from "./types";
import { useInterval } from "ahooks";

interface MonitorRequestItem {
  deviceList: string[];
  items: string[];
}

const formatData = (data: any) => {
  const pcsList: any[] = [];
  const bmsList: any[] = [];
  const temperContrList: any[] = [];
  const chargeStationList: any[] = [];
  data.forEach((item: any) => {
    if (item.device.type === DeviceType.PCS) {
      pcsList.push(item);
    } else if (item.device.type === DeviceType.BMS) {
      bmsList.push(item);
    } else if (item.device.type === DeviceType.TemperContr) {
      temperContrList.push(item);
    } else if (item.device.type === DeviceType.DCChargingStation) {
      chargeStationList.push(item);
    }
  });
  return {
    PCS: pcsList,
    BMS: bmsList,
    chargeStationList: chargeStationList,
    TemperContr: temperContrList
  }
}

export const DeviceStatus = (): JSX.Element => {
  const [deviceValues, setDeviceValues] = useState<any>({});

  const [configData, setConfigData] = useState<any>(null);
  const [modalConfig, setModalConfig] = useState<any>(null);
  const fetchModalConfig = async () => {
    const config = await httpGet('/api/configs/get?fileName=WriteDataViewAddress.cfg');
    setModalConfig(config || null);
  }
  const fetchDeviceStatus = async () => {
    try {
      // 获取监控配置
      // const config = await httpGet('/api/Ems/Config/GetEMSRequestConfig');
      // const monitorConfig = config?.data?.[0]?.MonitorRequest || {};
    
      const config = await httpGet('/api/configs/get?fileName=MonitorView.cfg');
      const monitorConfig = config?.MonitorRequest || {};
      console.log('monitorConfig', monitorConfig)
      setConfigData(monitorConfig);
      
      // 转换配置为请求参数格式
      const monitorRequest: MonitorRequestItem[] = Object.entries(monitorConfig).map(([_, value]: any) => {
        return {
          deviceList: value.deviceID?.map?.((item: any) => (typeof item === 'string' ? item : item.Code)) || [],
          items: value.ItemList?.map?.((item: any) => {
            if (typeof item === 'string') {
              return item
            } else if (typeof item === 'object') {
              // 如果有分组
              if (item.Group && Array.isArray(item.Group)) {
                return item.Group.map((groupItem: any) => groupItem?.Name)
              }
              return item?.Name
            }
          }).flat() || []
        }
      });
      // 获取设备状态数据
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', monitorRequest);
      if (res.code === 0) {
        const v = formatData(res.data || []);
        setDeviceValues(v);
        console.log('deviceValues', v)
      }
    } catch (error) {
      console.error('获取设备状态失败:', error);
    }
  };
  const clearInterval = useInterval(fetchDeviceStatus, 5000, {immediate: true});
  const clearInterval2 = useInterval(fetchModalConfig, 5000, {immediate: true});
  useEffect(() => {

    // fetchDeviceStatus();
    // fetchModalConfig();
    // 设置定时刷新
    
    return () => {
      clearInterval();
      clearInterval2();
    }
  }, []);

  return (
    <ModalConfigContext.Provider value={modalConfig}>
      <div className="bg-[#001857] flex flex-row justify-center w-full max-h-[800px] overflow-hidden">
        <div className="bg-[#001857] w-full max-w-[1280px] relative flex flex-col">
          <main className="flex flex-col w-full p-4">
            <div className="flex gap-[8px] w-full">
              <PcsStatusSection 
                data={ deviceValues?.PCS } 
                config={configData?.[DeviceType.PCS]}
              />
              <BmsStatusSection 
                data={ deviceValues?.BMS } 
                config={configData?.[DeviceType.BMS]}
              />
              <div className="flex flex-col gap-[8px] h-[708px]">
                {/* 充电桩和水冷机组 */ }
                {configData?.[DeviceType.DCChargingStation] && configData?.[DeviceType.TemperContr] ? (
                  <>
                    <div className="flex-1">
                      <ChargingStationSection
                        data={deviceValues?.chargeStationList}
                        config={configData?.[DeviceType.DCChargingStation]}
                        type="short"
                      />
                    </div>
                    <div className="flex-1">
                      <CoolingUnitSection
                        data={deviceValues?.TemperContr}
                        type="short"
                        config={configData?.TemperContr}
                      />
                    </div>
                  </>
                ) : configData?.[DeviceType.DCChargingStation] ? (
                  <div className="flex-1">
                    <ChargingStationSection
                      data={deviceValues?.chargeStationList}
                      config={configData?.[DeviceType.DCChargingStation]}
                      type="full"
                    />
                  </div>
                ) : configData?.[DeviceType.TemperContr] ? (
                  <div className="flex-1">
                    <CoolingUnitSection
                      data={deviceValues?.TemperContr}
                      type="full"
                      config={configData?.[DeviceType.TemperContr]}
                    />
                  </div>
                ) : (
                  // 默认显示空的CoolingUnitSection，避免渲染缺失问题
                  <div className="flex-1">
                    <CoolingUnitSection
                      data={[]}
                      type="full"
                    />
                  </div>
                )}
              </div>
            </div>
          </main>
        </div >
      </div >
    </ModalConfigContext.Provider>
  );
};
