import React from "react";
import "./styles.less";

interface SectionTitleProps {
  title: string;
  className?: string;
}

/**
 * 区域标题组件
 * 用于在详情页面中分隔不同的区域，显示为带有左侧蓝色竖条的标题
 */
export const SectionTitle: React.FC<SectionTitleProps> = ({ title, className = "" }) => {
  return (
    <div className={`section-title-container ${className}`}>
      <img 
        src="/figma_images/section_title_indicator.svg" 
        alt="标题指示器" 
        className="section-title-indicator-img"
      />
      <div className="section-title-text">
        {title}
      </div>
    </div>
  );
}; 