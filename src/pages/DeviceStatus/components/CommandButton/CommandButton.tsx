import React from 'react';
import { Button } from 'antd';

interface CommandButtonProps {
  descript: string;
  value: any;
  api?: string;
  name?: string;
  type?: string;
  index: number;
  deviceType?: string;
  isWiderButton?: boolean;
  buttonLoading: Record<string, boolean>;
  onApiButtonClick?: (api: string, value: any, descript: string, index: number) => void;
  onButtonClick: (name: string, value: any, descript: string, deviceType?: string) => void;
  className?: string;
  disabled?: boolean;
}

export const CommandButton: React.FC<CommandButtonProps> = ({
  descript,
  value,
  api,
  name,
  type,
  index,
  deviceType,
  isWiderButton,
  buttonLoading,
  onApiButtonClick,
  onButtonClick,
  className = 'command-button',
  disabled = false
}) => {
  // 判断是否使用API按钮逻辑
  if (api && onApiButtonClick) {
    const buttonKey = `api_${value.model || 'default'}_${index}`;
    return (
      <Button
        type="default"
        className={`${className} ${isWiderButton ? 'wider-button' : ''}`}
        loading={buttonLoading[buttonKey]}
        onClick={() => onApiButtonClick(api, value, descript, index)}
        disabled={disabled}
      >
        {descript}
      </Button>
    );
  } else {
    // 使用普通按钮逻辑
    const buttonName = name || type || '';
    return (
      <Button
        type="default"
        className={className}
        loading={buttonLoading[`${buttonName}_${value}`]}
        onClick={() => onButtonClick(buttonName, value, descript, deviceType)}
        disabled={disabled}
      >
        {descript}
      </Button>
    );
  }
}; 