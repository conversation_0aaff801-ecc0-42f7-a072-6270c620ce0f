import React, { ReactNode } from 'react';
import { useCommandExecution } from '../../hooks/useCommandExecution';
import { CommandButton } from './CommandButton';
import { ConfirmModal } from '../ConfirmModal';

interface CommandButtonContainerProps {
  children: (props: {
    /**
     * 按钮加载状态
     */
    buttonLoading: Record<string, boolean>;
    /**
     * 渲染一个命令按钮
     */
    renderCommandButton: (props: {
      key: React.Key;
      descript: string;
      value: any;
      api?: string;
      name?: string;
      type?: string;
      index: number;
      deviceType?: string;
      isWiderButton?: boolean;
      disabled?: boolean;
      className?: string;
    }) => ReactNode;
  }) => ReactNode;
  /**
   * 刷新数据的函数
   */
  refreshData: (silent?: boolean) => Promise<void>;
  /**
   * 设备编码
   */
  deviceCode: string;
  /**
   * 是否需要确认框
   */
  needConfirm?: boolean;
  /**
   * 自定义确认框内容生成函数
   */
  getConfirmContent?: (action: { type: string; descript: string; }) => string;
  /**
   * 执行前验证函数
   */
  beforeExec?: (params: {
    name: string;
    value: any;
    deviceCode: string;
    deviceType?: string;
    api?: string;
    index?: number;
  }) => boolean;
  /**
   * 自定义参数设置函数
   */
  setRequestParams?: (params: {
    name: string;
    value: any;
    deviceCode: string;
    deviceType?: string;
  }) => any;
}

/**
 * 命令按钮容器组件，提供渲染命令按钮的上下文
 */
export const CommandButtonContainer: React.FC<CommandButtonContainerProps> = ({
  children,
  refreshData,
  deviceCode,
  needConfirm = true,
  getConfirmContent,
  beforeExec,
  setRequestParams
}) => {
  // 使用命令执行钩子
  const {
    buttonLoading,
    handleButtonClick,
    handleApiButtonClick,
    confirmVisible,
    confirmLoading,
    confirmAction,
    handleConfirm,
    handleCancel
  } = useCommandExecution({
    refreshData,
    needConfirm,
    beforeExec,
    setRequestParams
  });

  // 命令按钮渲染函数
  const renderCommandButton = ({
    key,
    descript,
    value,
    api,
    name,
    type,
    index,
    deviceType,
    isWiderButton,
    disabled = false,
    className = 'command-button'
  }: {
    key: React.Key;
    descript: string;
    value: any;
    api?: string;
    name?: string;
    type?: string;
    index: number;
    deviceType?: string;
    isWiderButton?: boolean;
    disabled?: boolean;
    className?: string;
  }) => {
    return (
      <CommandButton
        key={key}
        descript={descript}
        value={value}
        api={api}
        name={name}
        type={type}
        index={index}
        deviceType={deviceType}
        isWiderButton={isWiderButton}
        buttonLoading={buttonLoading}
        onApiButtonClick={api ? 
          (api, value, descript, index) => handleApiButtonClick(api, value, descript, index, deviceCode) : 
          undefined}
        onButtonClick={(name, value, descript, deviceType) => 
          handleButtonClick(name, value, descript, deviceCode, deviceType)}
        disabled={disabled}
        className={className}
      />
    );
  };

  // 生成确认框内容
  const confirmContent = confirmAction ? 
    (getConfirmContent ? 
      getConfirmContent(confirmAction) : 
      `当前操作 ${confirmAction.descript} 是否确认？`) : 
    '';

  // 将按钮渲染函数和加载状态提供给子组件
  return (
    <>
      {children({ buttonLoading, renderCommandButton })}
      
      {/* 操作确认弹窗 */}
      <ConfirmModal
        open={confirmVisible}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        confirmLoading={confirmLoading}
        content={confirmContent}
      />
    </>
  );
}; 