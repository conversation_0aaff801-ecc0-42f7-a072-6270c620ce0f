import React from "react";
import alarmIcon from '@/assets/monitor/alert.png';

interface AlarmTagProps {
  text?: string;
}

export const AlarmTag: React.FC<AlarmTagProps> = ({ text = "告警" }) => {
  return (
    <div 
      className="flex items-center justify-center px-3 py-1 rounded-sm relative"
      style={{ 
        background: "rgba(238, 31, 31, 0.4)",
        border: "1px solid #EE1F1F",
        height: "34px",
        width: "fit-content",
        minWidth: "84px"
      }}
    >
      <div className="flex items-center justify-center gap-[5px]">
        <img 
          src={alarmIcon} 
          alt="告警图标" 
          className="w-[18px] h-[18px]" 
        />
        <span 
          className="text-white text-base" 
          style={{ 
            fontFamily: "PingFang SC", 
            fontSize: "16px", 
            fontWeight: 400,
            lineHeight: "18px"
          }}
        >
          {text}
        </span>
      </div>
    </div>
  );
}; 