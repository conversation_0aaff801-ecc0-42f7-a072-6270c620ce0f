import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Modal, Table, Spin, Row, Col, message } from 'antd';
import { httpPost } from '@/shared/http';
import { useModalConfig } from '../../context/ModalConfigContext';
import { ModalTypeKey } from '../../types';
import { useContentDictionary } from '../../hooks/useContentDictionary';
import { DeviceType } from '../../types';
import { SectionTitle } from '../SectionTitle';
import { CommandButtonContainer } from '../CommandButton';
import { MultiWriteItemsContainer } from '../MultiWriteItems';
import './styles.less';
import { CloseOutlined } from '@ant-design/icons';

interface PcsDetailModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceCode: string;
  deviceName: string;
}

interface DetailData {
  [key: string]: any;
}

interface ConfigItem {
  Name: string;
  Descript: string;
  Unit?: string;
  ViewType?: string;
  Value?: any;
  Type?: string;
}

export const PcsDetailModal: React.FC<PcsDetailModalProps> = ({
  open,
  onOpenChange,
  deviceCode,
  deviceName,
}) => {
  const modalConfig = useModalConfig(); // 使用Context获取modalConfig
  const { getContentValue } = useContentDictionary();
  const [detailData, setDetailData] = useState<DetailData>({});
  const [initialized, setInitialized] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取PCS配置
  const pcsConfig = modalConfig?.DeviceParaItems?.[modalConfig?.[ModalTypeKey.PCS_KEY]]?.DataView;

  // 初始化数据
  useEffect(() => {
    if (open && deviceCode && modalConfig && pcsConfig) {
      // 初次打开时获取数据
      fetchDetailData();

      // 设置定时刷新
      timerRef.current = setInterval(() => {
        fetchDetailData(true); // true表示是静默刷新
      }, 5000); // 每5秒刷新一次
    }

    return () => {
      // 组件卸载或modal关闭时清除定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [open, deviceCode, modalConfig,pcsConfig]);

  const fetchDetailData = async (silent = false) => {
    if (!modalConfig || !deviceCode || !pcsConfig) return;

    try {
      // 构建需要请求的所有字段
      let allItems: string[] = [];

      // 处理Single字段
      if (pcsConfig.Single) {
        const singleItems = pcsConfig.Single.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...singleItems];
      }

      // 处理StateLight字段
      if (pcsConfig.StateLight?.Group) {
        const stateLightItems = pcsConfig.StateLight.Group.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...stateLightItems];
      }

      // 处理ParaInfo字段
      if (pcsConfig.ParaInfo?.Group) {
        const paraInfoItems = pcsConfig.ParaInfo.Group.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...paraInfoItems];
      }

      // 处理MultiWriteItems字段
      if (pcsConfig.MultiWriteItems?.Group) {
        const writeItems = pcsConfig.MultiWriteItems.Group.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...writeItems];
      }

      // 过滤掉重复项和空项
      allItems = [...new Set(allItems)].filter(Boolean);

      // 构建请求数据
      const requestData = [
        {
          deviceList: [deviceCode],
          items: allItems,
        },
      ];

      // 发送请求获取详情数据
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', requestData);

      if (res.code === 0 && res.data && res.data.length > 0) {
        const deviceData = res.data[0];

        // 正确处理返回的数据结构
        // 将itemList中的数据转换成一个键值对对象
        const formattedData: DetailData = {};
        if (deviceData.itemList && Array.isArray(deviceData.itemList)) {
          deviceData.itemList.forEach((item: any) => {
            if (item.name) {
              formattedData[item.name] = item.value;
            }
          });
        }

        if (process.env.NODE_ENV === 'development') {
          console.log('PcsDetailModal: 获取到的数据', { deviceData, formattedData });
        }

        setDetailData(formattedData);
        if (!initialized) {
          setInitialized(true);
        }
      } else {
        if (!silent) {
          console.error('PcsDetailModal: 返回数据格式错误', res);
        }
      }
    } catch (error) {
      console.error('获取PCS详情数据失败:', error);
      if (!silent) {
        message.error('获取PCS详情数据失败');
      }
    }
  };

  // 确认框内容生成函数
  const getConfirmContent = useCallback((action: { type: string; descript: string }) => {
    return `当前操作 ${action.descript} 是否确认？`;
  }, []);

  // 获取字段值并处理Content类型的翻译
  const getFieldValue = useCallback(
    (name: string, viewType?: string) => {
      const value = detailData[name];

      if (viewType === 'Content') {
        return getContentValue(DeviceType.PCS, name, value);
      }

      return value !== undefined && value !== null ? value : '-';
    },
    [detailData, getContentValue],
  );

  // 渲染Single字段
  const renderSingleFields = () => {
    if (!pcsConfig?.Single || pcsConfig.Single.length === 0) return null;

    return (
      <Row gutter={[24, 14]} className="single-field-section mb-[32px]">
        {pcsConfig.Single.map((item: ConfigItem, index: number) => (
          <Col key={index} span={8}>
            <div className="flex items-center">
              <span className="label-text">{item.Descript}：</span>
              <span className="value-text">
                {getFieldValue(item.Name, item?.ViewType)}
                {item.Unit ? ` ${item.Unit}` : ''}
              </span>
            </div>
          </Col>
        ))}
      </Row>
    );
  };

  // 渲染指令下发按钮
  const renderButtonItems = () => {
    if (!pcsConfig?.ButtonItems) return null;

    return (
      <CommandButtonContainer
        refreshData={fetchDetailData}
        deviceCode={deviceCode}
        getConfirmContent={getConfirmContent}
      >
        {({ renderCommandButton }) => (
          <>
            <SectionTitle title={pcsConfig.ButtonItems.Label || '指令下发'} />
            <div className="command-buttons-container mb-8">
              {pcsConfig.ButtonItems.Group?.map((item: ConfigItem, index: number) => {
                // 扩展ConfigItem类型以适应可能包含Api的情况
                const itemWithApi = item as ConfigItem & { Api?: string };

                return renderCommandButton({
                  key: index,
                  descript: item.Descript,
                  value: item.Value,
                  api: itemWithApi.Api,
                  name: item.Name,
                  index: index,
                });
              })}
            </div>
          </>
        )}
      </CommandButtonContainer>
    );
  };

  // 渲染继电器状态
  const renderStateLight = () => {
    if (!pcsConfig?.StateLight) return null;

    return (
      <>
        <SectionTitle title={pcsConfig.StateLight.Label || '继电器状态'} />
        <div className="state-light-container">
          {pcsConfig.StateLight.Group?.map((item: ConfigItem, index: number) => {
            const value = getFieldValue(item.Name, item.ViewType);
            const isOn = value === 1 || value === '1' || value === true;

            return (
              <div key={index} className="state-light-item">
                <img
                  src={isOn ? '/figma_images/relay_active.svg' : '/figma_images/relay_inactive.svg'}
                  alt={isOn ? '继电器开启' : '继电器关闭'}
                  width={24}
                  height={24}
                />
                <span className="label-text">{item.Descript}</span>
              </div>
            );
          })}
        </div>
      </>
    );
  };

  // 渲染参数信息
  const renderParaInfo = () => {
    if (!pcsConfig?.ParaInfo) return null;

    return (
      <>
        <SectionTitle title={pcsConfig.ParaInfo.Label || '参数信息'} />
        <Row gutter={[24, 14]} className="para-info-section mb-8">
          {pcsConfig.ParaInfo.Group?.map((item: ConfigItem, index: number) => (
            <Col key={index} span={8}>
              <div className="flex items-center">
                <span className="label-text">{item.Descript}：</span>
                <span className="value-text">
                  {getFieldValue(item.Name, item.ViewType)}
                  {item.Unit ? ` ${item.Unit}` : ''}
                </span>
              </div>
            </Col>
          ))}
        </Row>
      </>
    );
  };

  // 渲染参数配置 - 使用MultiWriteItemsContainer
  const renderMultiWriteItems = () => {
    if (!pcsConfig?.MultiWriteItems) return null;

    return (
      <>
        <SectionTitle title={pcsConfig.MultiWriteItems.Label || '参数配置'} />
        <MultiWriteItemsContainer
          configItems={pcsConfig.MultiWriteItems.Group || []}
          detailData={detailData}
          deviceCode={deviceCode}
          refreshData={fetchDetailData}
          columnsPerRow={3}
        />
      </>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="flex h-[200px] flex-col items-center justify-center">
      <Spin tip="加载中..." />
    </div>
  );

  return (
    <>
      <Modal
        title={`${deviceName || 'PCS'}`}
        open={open}
        onCancel={() => onOpenChange(false)}
        footer={null}
        width={1128}
        className="pcs-detail-modal"
        maskClosable={false}
        closeIcon={<CloseOutlined className="modal-close-icon" />}
        centered
      >
        {!initialized ? (
          renderEmptyState()
        ) : (
          <div className="pcs-detail-content">
            {pcsConfig ? (
              <>
                {/* Single字段 - 显示在最上方 */}
                {renderSingleFields()}

                {/* 指令下发 */}
                {renderButtonItems()}

                {/* 继电器状态 */}
                {renderStateLight()}

                {/* 参数信息 */}
                {renderParaInfo()}

                {/* 参数配置 */}
                {renderMultiWriteItems()}
              </>
            ) : (
              <div className="py-8 text-center text-[#E3F8FF]">未找到PCS配置信息，请检查配置</div>
            )}
          </div>
        )}
      </Modal>

      {/* 虚拟键盘和确认框已集成到MultiWriteItemsContainer和CommandButtonContainer组件中 */}
    </>
  );
};
