@import '../modalCommon.less';

/* PCS详情模态框样式 */
.pcs-detail-modal,
div.ant-modal.pcs-detail-modal {
  .device-modal-common();

  .label-text {
    max-width: 185px !important; /* 限制标签最大宽度 */
  }

  .param-config-item {
    .label-text {
      max-width: 150px !important; /* 限制参数标签最大宽度 */
    }
  }
  
  /* PCS详情模态框特定样式 */
  .command-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    
    .ant-col {
      width: auto !important;
      flex: none !important;
    }
  }

  .command-button {
    span {
      color: #6DE875;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 100% */
    }
  }

  /* 状态指示器容器样式 */
  .state-light-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 32px;
    
    .state-light-item {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-basis: calc(20% - 10px);
      
      @media screen and (max-width: 768px) {
        flex-basis: calc(33.33% - 8px);
      }
    }
  }

  /* 状态指示器样式 */
  .state-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    position: relative;
    
    &.active {
      background-color: rgba(19, 85, 34, 0.48);
      border: 0.86px solid #1E9C5F;
      
      &:before {
        content: '';
        position: absolute;
        width: 17.14px;
        height: 17.14px;
        left: 3.43px;
        top: 3.43px;
        border-radius: 50%;
        background-color: #1CC367;
        box-shadow: inset 3.43px 3.43px 3.43px -1.71px rgba(255, 255, 255, 0.25);
      }
      
      &:after {
        content: '';
        position: absolute;
        width: 5.71px;
        height: 5.71px;
        left: 9.14px;
        top: 9.14px;
        border-radius: 50%;
        background-color: #FFFFFF;
        opacity: 0.8;
        filter: blur(2.57px);
      }
    }
    
    &.inactive {
      background-color: rgba(119, 126, 151, 0.31);
      border: 0.86px solid #676E87;
      
      &:before {
        content: '';
        position: absolute;
        width: 17.14px;
        height: 17.14px;
        left: 3.43px;
        top: 3.43px;
        border-radius: 50%;
        background-color: #788096;
        box-shadow: inset 3.43px 3.43px 3.43px -1.71px rgba(255, 255, 255, 0.25);
      }
      
      &:after {
        content: '';
        position: absolute;
        width: 5.71px;
        height: 5.71px;
        left: 9.14px;
        top: 9.14px;
        border-radius: 50%;
        background-color: #FFFFFF;
        opacity: 0.8;
        filter: blur(2.57px);
      }
    }
  }

  /* 参数信息和状态灯样式 */
  .para-info-section,
  .param-config-section {
    margin-bottom: 32px;
  }
  
  /* 参数配置区域样式 */
  .param-config-section {
    .ant-row {
      row-gap: 14px !important;
    }
    
    .ant-form-item {
      margin-bottom: 0;
    }
  }
  
  /* 参数配置项样式 */
  .param-config-item {
    margin-bottom: 0;
    
    .param-input-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .unit-text {
      margin-right: 0;
      white-space: nowrap;
    }
  }
  
  /* 单个字段样式 */
  .single-field-section {
    margin-top: 8px;
    
    .ant-col {
      .flex {
        min-height: 28px;
      }
    }
  }

  /* PCS详情内容区域特定样式 */
  .pcs-detail-content {
    .modal-content();
  }
}
