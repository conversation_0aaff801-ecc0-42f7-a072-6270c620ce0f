.device-confirm-modal,
div.ant-modal.device-confirm-modal {
  .ant-modal-content {
    background-color: #143F8C;
    border-radius: 0;
    padding: 24px 24px 0;
    box-shadow: 0px 2px 8px 0px rgba(33, 37, 46, 0.15);
  }

  .ant-modal-header {
    background-color: transparent;
    border-bottom: none;
    padding: 0;
    margin: 0;
    
    .ant-modal-title {
      color: #E3F8FF !important;
      font-family: 'PingFang SC';
      font-size: 22px;
      font-weight: 400;
      line-height: 1em;
      display: flex;
      align-items: center;
      
      &::before {
        content: 'i';
        display: inline-block;
        width: 24px;
        height: 24px;
        background-color: #0478EC;
        border-radius: 50%;
        margin-right: 8px;
        position: relative;
        color: #143F8C;
        font-size: 16px;
        text-align: center;
        line-height: 24px;
        font-weight: bold;
      }
    }
  }

    .ant-modal-close {
      // top: 24px;
      // right: 24px;
      color: #A5CFFF;
      height: 82px;
      top: 0;
      width: 72px;
      right: 0;
  
      &:hover {
        color: #E3F8FF;
      }
    }

  .modal-close-icon {
    color: #A5CFFF;
    font-size: 22px;
    font-weight: 200;
    
    &:hover {
      color: #E3F8FF;
    }
  }

  .confirm-content {
    padding: 24px 0;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-weight: 400;
    line-height: 1em;
    color: #E3F8FF;
  }

  .confirm-footer {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    padding: 12px 0 24px;
    
    button {
      flex:1;
      width: 50%;
      padding: 12px 16px;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-weight: 400;
      border-radius: 6px;
      line-height: 1.25em;
      cursor: pointer;
      min-width: 80px;
      text-align: center;
      
      &.cancel-button {
        background-color: transparent;
        border: 1px solid #A5CFFF;
        color: #A5CFFF;
        
        &:hover {
          color: #E3F8FF;
          border-color: #E3F8FF;
        }
      }
      
      &.confirm-button {
        background-color: #0478EC;
        border: 1px solid #0478EC;
        color: #FFFFFF;
        
        &:hover {
          opacity: 0.9;
        }
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}