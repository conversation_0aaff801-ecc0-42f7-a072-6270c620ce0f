import React from 'react';
import { Modal } from 'antd';
import './styles.less';
import { CloseOutlined } from '@ant-design/icons';

export interface ConfirmModalProps {
  open: boolean;
  title?: string;
  content: React.ReactNode;
  onCancel: () => void;
  onConfirm: () => void;
  confirmLoading?: boolean;
  cancelText?: string;
  confirmText?: string;
  cancelButtonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
  zIndex?: number;
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  open,
  title = "提示",
  content,
  onCancel,
  onConfirm,
  confirmLoading = false,
  cancelText = "取消",
  cancelButtonProps = {},
  confirmText = "确定",
  zIndex = 10000000,
}) => {
  return (
    <Modal
      open={open}
      title={title}
      onCancel={onCancel}
      footer={null}
      width={400}
      centered
      closable={true}
      closeIcon={<CloseOutlined className="modal-close-icon" />}
      className="device-confirm-modal"
      maskClosable={false}
      zIndex={zIndex}
    >
      <div className="confirm-content">
        {content}
      </div>
      <div className="confirm-footer">
        <button className="cancel-button" {...cancelButtonProps} onClick={onCancel}>
          {cancelText}
        </button>
        <button className="confirm-button" onClick={onConfirm} disabled={confirmLoading}>
          {confirmLoading ? "处理中..." : confirmText}
        </button>
      </div>
    </Modal>
  );
};