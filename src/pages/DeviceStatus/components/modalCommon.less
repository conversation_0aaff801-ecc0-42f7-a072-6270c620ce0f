/* 设备状态页面模态框公共样式 */
.device-modal-common {
  .ant-modal-content {
    background-color: #143F8C;
    border: none;
    border-radius: 0;
    padding: 24px 24px 40px;
    box-shadow: none;

    // label公共样式，最大宽度+换行
    .label-text {
      max-width: 200px;
      // 允许文本换行
      word-break: break-word;
      white-space: normal;
      overflow-wrap: break-word;
    }
  }

  .ant-modal-header {
    background-color: transparent;
    border-bottom: none;
    padding: 0 0 24px 0;
    margin: 0;

    .ant-modal-title {
      color: #E3F8FF !important;
      font-family: 'PingFang SC';
      font-size: 24px;
      font-weight: 400;
      line-height: 1em;
    }
  }

  /* 弹窗关闭按钮 */
  .ant-modal-close {
    // top: 20px;
    // right: 32px;
    height: 82px;
    top: 0;
    width: 72px;
    right: 0;

    .ant-modal-close-x {
      // width: 32px;
      // height: 32px;
      // padding: 4px;
      // position: relative;

      &:hover {
        .modal-close-icon {
          color: #E3F8FF;
        }
      }
    }
  }

  .modal-close-icon {
    color: #A5CFFF;
    width: 24px;
    height: 24px;
    font-size: 64px;
    font-weight: 200;
    // position: absolute;
    // top: -5px;
    // left: 0;

    &:hover {
      color: #E3F8FF;
    }
  }

  // 全局文本样式
  .label-text {
    color: #9CCEFF !important;
    font-size: 20px !important;
    font-weight: 400;
    margin-right: 8px;
    line-height: 1em;
  }

  .value-text {
    color: #E3F8FF !important;
    font-size: 20px !important;
    font-weight: 400;
    line-height: 0.9em;
    word-break: break-word;
  }

  .modal-content {
    padding: 0;
    max-height: 80vh;
    overflow-y: auto;
    overflow-x: hidden;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #3D5DAC62;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background: #3D5DAC;
    }
  }

  /* 命令按钮公共样式 */
  .command-button {
    min-width: 125px;
    height: auto;
    padding: 16px 9px;
    border-radius: 6px;
    font-size: 20px;
    font-weight: 400;
    text-align: center;
    color: #A5CFFF !important;
    border: 1px solid #39ABFF;
    background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);

    &:hover,
    &:focus {
      border: 1px solid #39ABFF;
      background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);
      color: #6DE875 !important;
    }
  }

  /* 输入框公共样式 */
  .param-input {
    background: transparent;
    border: 1px solid #39ABFF;
    border-radius: 6px;
    color: #E3F8FF !important;
    height: 48px;
    font-size: 20px;
    line-height: 1em;
    width: 100%;
    max-width: 200px;
    text-align: left;
    padding: 16px 9px;

    &:hover,
    &:focus {
      border-color: #39ABFF;
      box-shadow: none;
      color: #E3F8FF !important;
      background: transparent;
      outline: none;
    }

    &::placeholder {
      color: rgba(227, 248, 255, 0.5);
    }

    /* 移除Input number的上下箭头 */
    &[type="number"]::-webkit-inner-spin-button,
    &[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    &[type="number"] {
      -moz-appearance: textfield;
    }
  }

  /* 加载指示器样式 */
  .ant-spin {
    color: #00C8FF;

    .ant-spin-dot-item {
      background-color: #00C8FF;
    }

    .ant-spin-text {
      color: #E3F8FF;
      font-family: 'PingFang SC';
      margin-top: 8px;
    }
  }

  // 添加键盘相关样式
 /*  .custom-keyboard {
    max-width: 100%;
    margin: 0 auto;
    background-color: #143F8C;

    .hg-button {
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #0478EC !important;
      // border: 1px solid #ccc;
      font-size: 18px;
      color: #333;
    }

    .hg-button:active {
      background-color: #39abff;
      color: white;
    }
  }
 */
 
 // 参数输入框样式
  .param-input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;

    .ant-form-item {
      flex: 1;
      margin-bottom: 0;
    }

    .param-input {
      background: transparent !important;
      color: #e3f8ff;
      border-color: #39ABFF;
    }

    .unit-text {
      margin-left: 8px;
      color: #e3f8ff;
    }
  }
}