import React, { useEffect, useState, useCallback, useRef } from "react";
import { Modal, Row, Col, Spin } from "antd";
import { httpPost } from "@/shared/http";
import { useModalConfig } from "../../context/ModalConfigContext";
import { ModalTypeKey } from "../../types";
import { useContentDictionary } from "../../hooks/useContentDictionary";
import { DeviceType } from "../../types";
import { CloseOutlined } from '@ant-design/icons';
import './styles.less';

interface BmsDetailModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceCode: string;
  deviceName: string;
}

interface DetailData {
  [key: string]: any;
}

interface ConfigItem {
  Name: string;
  Descript: string;
  Unit?: string;
  ViewType?: string;
  Value?: any;
}

interface TabConfig {
  Label: string;
  Items: ConfigItem[];
}

export const BmsDetailModal: React.FC<BmsDetailModalProps> = ({
  open,
  onOpenChange,
  deviceCode,
  deviceName
}) => {
  const modalConfig = useModalConfig(); // 使用Context获取modalConfig
  const { getContentValue } = useContentDictionary();
  const [detailData, setDetailData] = useState<DetailData>({});
  const [activeTab, setActiveTab] = useState<number>(0);
  const [initialized, setInitialized] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取BMS配置
  const bmsConfig = modalConfig?.DeviceParaItems?.[modalConfig?.[ModalTypeKey.BMS_KEY]]?.DataView;

  // 初始化数据
  useEffect(() => {
    if (open && deviceCode && modalConfig) {
      // 初次打开时获取数据
      fetchDetailData();

      // 设置定时刷新
      timerRef.current = setInterval(() => {
        fetchDetailData(true); // true表示是静默刷新
      }, 5000); // 每5秒刷新一次
    }

    return () => {
      // 组件卸载或modal关闭时清除定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [open, deviceCode, modalConfig]);

  // 获取所有需要请求的字段
  const getAllRequestItems = useCallback(() => {
    if (!bmsConfig?.Multi || !Array.isArray(bmsConfig.Multi)) return [];

    const allItems: string[] = [];
    
    // 从所有Tab页签中收集字段
    bmsConfig.Multi.forEach((tab: TabConfig) => {
      if (tab.Items && Array.isArray(tab.Items)) {
        const tabItems = tab.Items.map((item: ConfigItem) => item.Name);
        allItems.push(...tabItems);
      }
    });
    
    // 过滤掉重复项和空项
    return [...new Set(allItems)].filter(Boolean);
  }, [bmsConfig]);

  const fetchDetailData = async (silent = false) => {
    if (!modalConfig || !deviceCode || !bmsConfig) return;

    try {
      // 构建需要请求的所有字段
      const allItems = getAllRequestItems();
      
      if (allItems.length === 0) {
        console.warn("BmsDetailModal: 没有找到需要请求的字段");
        return;
      }

      // 构建请求数据
      const requestData = [
        {
          deviceList: [deviceCode],
          items: allItems
        }
      ];

      // 发送请求获取详情数据
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', requestData);
      
      if (res.code === 0 && res.data && res.data.length > 0) {
        const deviceData = res.data[0];
        
        // 正确处理返回的数据结构
        // 将itemList中的数据转换成一个键值对对象
        const formattedData: DetailData = {};
        if (deviceData.itemList && Array.isArray(deviceData.itemList)) {
          deviceData.itemList.forEach((item: any) => {
            if (item.name) {
              formattedData[item.name] = item.value;
            }
          });
        }
        
        if (process.env.NODE_ENV === 'development') {
          console.log('BmsDetailModal: 获取到的数据', { deviceData, formattedData });
        }
        
        setDetailData(formattedData);
        if (!initialized) {
          setInitialized(true);
        }
      } else {
        if (!silent) {
          console.error('BmsDetailModal: 返回数据格式错误', res);
        }
      }
    } catch (error) {
      console.error("获取BMS详情数据失败:", error);
      if (!silent) {
        // message.error("获取BMS详情数据失败");
      }
    }
  };

  // 获取字段值并处理Content类型的翻译
  const getFieldValue = useCallback((name: string, viewType?: string) => {
    const value = detailData[name];
    
    if (viewType === 'Content') {
      return getContentValue(DeviceType.BMS, name, value);
    }
    
    return value !== undefined && value !== null ? value : '-';
  }, [detailData, getContentValue]);

  // 切换Tab
  const handleTabChange = useCallback((index: number) => {
    setActiveTab(index);
  }, []);

  // 渲染Tab页签
  const renderTabs = () => {
    if (!bmsConfig?.Multi || !Array.isArray(bmsConfig.Multi) || bmsConfig.Multi.length === 0) {
      return null;
    }

    return (
      <div className="bms-tabs">
        <div className="flex">
          {bmsConfig.Multi.map((tab: TabConfig, index: number) => (
            <div
              key={index}
              className={`bms-tab-item ${activeTab === index ? 'active' : ''}`}
              onClick={() => handleTabChange(index)}
            >
              {tab.Label}
            </div>
          ))}
        </div>
        <div className="bms-tabs-divider" />
      </div>
    );
  };

  // 渲染当前激活Tab的内容
  const renderActiveTabContent = () => {
    if (!bmsConfig?.Multi || !Array.isArray(bmsConfig.Multi) || bmsConfig.Multi.length === 0) {
      return null;
    }

    const currentTab = bmsConfig.Multi[activeTab];
    if (!currentTab || !currentTab.Items || !Array.isArray(currentTab.Items)) {
      return null;
    }

    // 将Items分成多行，每行两个字段
    const rows: ConfigItem[][] = [];
    for (let i = 0; i < currentTab.Items.length; i += 2) {
      rows.push(currentTab.Items.slice(i, i + 2));
    }

    return (
      <div className="data-section">
        {rows.map((row, rowIndex) => (
          <Row key={rowIndex} gutter={[24, 14]}>
            {row.map((item, itemIndex) => (
              <Col key={itemIndex} span={12}>
                <div className="flex items-center">
                  <span className="label-text">{item.Descript}：</span>
                  <span className="value-text">
                    {getFieldValue(item.Name, item.ViewType)}
                    {item.Unit ? ` ${item.Unit}` : ''}
                  </span>
                </div>
              </Col>
            ))}
          </Row>
        ))}
      </div>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-[200px]">
      <Spin tip="加载中..." />
    </div>
  );

  return (
    <Modal
      title={`${deviceName || "BMS"}`}
      open={open}
      onCancel={() => onOpenChange(false)}
      footer={null}
      width={700}
      className="bms-detail-modal"
      maskClosable={false}
      // closeIcon={<span className="modal-close-icon">×</span>}
      closeIcon={<CloseOutlined className="modal-close-icon" />}
      centered
    >
      {!initialized && !bmsConfig ? renderEmptyState() : (
        <div className="bms-detail-content">
          {renderTabs()}
          {renderActiveTabContent()}
          
          {!bmsConfig && (
            <div className="text-center py-8 text-[#E3F8FF]">
              未找到BMS配置信息，请检查配置
            </div>
          )}
        </div>
      )}
    </Modal>
  );
}; 