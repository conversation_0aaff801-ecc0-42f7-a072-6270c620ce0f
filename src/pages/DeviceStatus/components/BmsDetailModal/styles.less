@import '../../components/modalCommon.less';

.bms-detail-modal,
div.ant-modal.bms-detail-modal {
  .device-modal-common();
  
  /* BMS详情模态框特有样式 */
  .bms-detail-content {
    .modal-content();
  }

  /* Tab页签样式 */
  .bms-tabs {
    margin-bottom: 24px;
    
    .flex {
      display: flex;
      flex-direction: row;
    }
    
    .bms-tab-item {
      display: inline-flex;
      align-items: center;
      padding: 0 10px;
      height: 38px;
      cursor: pointer;
      font-family: 'PingFang SC';
      font-size: 22px;
      color: #579CDB;
      position: relative;
      transition: color 0.3s;
      margin-right: 68px; /* 根据Figma设计调整间距 */
      
      &:last-child {
        margin-right: 0;
      }

      &.active {
        color: #3BE354;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          width: 100%;
          height: 4px;
          background-color: #3BE354;
        }
      }

      &:hover {
        color: #3BE354;
      }
    }

    .bms-tabs-divider {
      width: 100%;
      height: 1px;
      background-color: #477EDD;
      margin-top: 4px;
    }
  }

  /* 数据组部分的样式 */
  .data-section {
    margin-bottom: 16px;

    .ant-row {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
} 