import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Divider } from "antd";
import { RightOutlined } from '@ant-design/icons';

// 定义水冷机组卡片属性类型
interface UnitProperty {
  name: string;
  label: string;
  value: any;
  unit?: string;
}

// CoolingUnitCard属性定义
export interface CoolingUnitCardProps {
  id: number;
  name: string; // 机组名称，如"1#机组"
  status: string; // 在线状态："online" | "offline"
  statusText: string; // 状态文本，如"在线"
  statusColor: string; // 状态颜色
  textColor: string; // 文本颜色
  borderColor: string; // 边框颜色
  properties: UnitProperty[]; // 属性列表
  hasAlarm?: boolean; // 是否有告警
  onDetailClick?: (id: number) => void; // 详情点击回调
}

export const CoolingUnitCard: React.FC<CoolingUnitCardProps> = ({
  id,
  name,
  status,
  statusText,
  statusColor,
  textColor,
  borderColor,
  properties = [],
  hasAlarm = false,
  onDetailClick,
}) => {
  // 根据状态确定样式
  const isOffline = status === "offline";
  const nameColor = isOffline ? "#4365A9" : "#00C8FF";
  const textStyle = isOffline ? "#4365A9" : "#C7F3FF";
  
  return (
    <Card
      className={`h-[112px] w-full border-[2px] border-solid overflow-hidden ${isOffline ? "border-[#4365a9]" : "border-[#39abff]"}`}
      style={{ 
        background: "linear-gradient(180deg, rgba(33, 85, 164, 0.20) 0%, rgba(23, 63, 129, 0.00) 100%, rgba(14, 80, 188, 1.00) 0%)"
      }}
      onClick={(e) => {
        e.stopPropagation();
        !isOffline && onDetailClick?.(id);
      }}
    >
      <CardContent className="p-2 h-full flex flex-col overflow-y-auto mb-1">
        <div className="flex items-center justify-between w-full">
          {/* 机组名称 */}
          <div
            className="text-[18px] font-normal leading-[18px] whitespace-nowrap"
            style={{ 
              fontFamily: "PingFang SC",
              color: nameColor,
              textShadow: isOffline ? "none" : "0px 0px 4px rgba(0, 200, 255, 0.4)"
            }}
          >
            {name}
          </div>
          
          {/* 在线状态和详情按钮 */}
          <div className="flex items-center gap-3.5">
            <div className="flex items-center gap-1">
              <div
                className={`w-[6px] h-[6px] rounded-full`}
                style={{ 
                  backgroundColor: statusColor,
                  boxShadow: status === "online" ? "0px 0px 4px 1px rgba(0, 255, 80, 0.7)" : "none"
                }}
              />
              <div
                className="text-[16px] font-normal whitespace-nowrap"
                style={{ 
                  fontFamily: "PingFang SC", 
                  color: statusColor,
                  textShadow: status === "online" ? "0px 0px 6px rgba(0, 255, 80, 0.7)" : "none"
                }}
              >
                {statusText}
              </div>
            </div>
            
            {/* 分割线 */}
            <Divider type="vertical" className="h-5 m-0 border-[#2568C2]" />
            
            {onDetailClick && (
              <div 
                className={`flex items-center gap-2 ${!isOffline ? "cursor-pointer" : "cursor-default"}`}
                style={{ 
                  fontFamily: "PingFang SC",
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "16px",
                  color: isOffline ? "#4365A9" : "#C7F3FF"
                }}
              >
                详情 <RightOutlined className="ml-0.5 text-xs" />
              </div>
            )}
          </div>
        </div>

        {/* 机组内容 */}
        {isOffline ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-[18px] font-normal"
              style={{ 
                fontFamily: "PingFang SC",
                color: "#4365A9"
              }}
            >
              关机
            </div>
          </div>
        ) : (
          <div className="flex-1 flex flex-col justify-center gap-1">
            {properties.map((prop, index) => (
              <div key={index} className="flex items-center justify-between w-full">
                <div 
                  className="text-[18px] font-normal"
                  style={{ 
                    fontFamily: "PingFang SC",
                    color: textColor
                  }}
                >
                  {prop.label}:
                </div>
                <div 
                  className="text-[18px] font-normal"
                  style={{ 
                    fontFamily: "PingFang SC",
                    color: textColor
                  }}
                >
                  {prop.value}{prop.unit}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 