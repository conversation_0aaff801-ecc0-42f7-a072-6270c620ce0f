import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Modal, Spin, Checkbox, Row, Col, message } from 'antd';
import { httpPost } from '@/shared/http';
import { useModalConfig } from '../../context/ModalConfigContext';
import { ModalTypeKey } from '../../types';
import { SectionTitle } from '../SectionTitle';
import { CommandButtonContainer } from '../CommandButton';
import './styles.less';
import { MultiWriteItemsContainer } from '../MultiWriteItems';
import { ConfigItem } from '../../hooks/useParamSettings';
import { CloseOutlined } from '@ant-design/icons';

interface PcsOverviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface SelectedDevices {
  [key: string]: boolean;
}

interface DeviceSelectOption {
  label: string;
  value: string;
}

export const PcsOverviewModal: React.FC<PcsOverviewModalProps> = ({ open, onOpenChange }) => {
  const modalConfig = useModalConfig(); // 使用Context获取modalConfig
  const [overviewData, setOverviewData] = useState<any>({});
  const [initialized, setInitialized] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 设备选择状态
  const [selectedDevices, setSelectedDevices] = useState<SelectedDevices>({});
  const [deviceOptions, setDeviceOptions] = useState<DeviceSelectOption[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // 获取PCS管理配置
  const pcsManageKey = modalConfig?.[ModalTypeKey.PCS_MANAGE_KEY];
  const pcsManageConfig = modalConfig?.DeviceParaItems?.[pcsManageKey]?.DataView;
  const pcsManageLabel = modalConfig?.DeviceParaItems?.[pcsManageKey]?.Label;
  const multiDeviceNames = pcsManageConfig?.MultiDeviceNames || [];
  const [allDevice, setAllDevice] = useState<string[]>([]);

  // 初始化设备选项
  useEffect(() => {
    if (open && modalConfig && pcsManageConfig?.DeviceSelect) {
      // 只有首次打开或设备选项发生变化时才初始化设备选择状态
      const options: DeviceSelectOption[] = [];
      const initialSelected: SelectedDevices = {};
      const device: string[] = [];

      Object.entries(pcsManageConfig.DeviceSelect).forEach(([key, value]) => {
        if (key !== 'All') {
          // 排除All选项
          options.push({
            label: value as string,
            value: key,
          });
          device.push(key);
          // 保留已有的选择状态，只初始化新增的选项
          initialSelected[key] = selectedDevices[key] || false;
        }
      });
      setAllDevice(device);
      setDeviceOptions(options);
      // 只有当设备列表发生变化时才更新选择状态
      if (
        JSON.stringify(Object.keys(initialSelected)) !==
        JSON.stringify(Object.keys(selectedDevices))
      ) {
        setSelectedDevices(initialSelected);
      }
    }
  }, [open, modalConfig, pcsManageConfig?.DeviceSelect]);

  // 初始化数据并设置定时刷新
  useEffect(() => {
    if (open && modalConfig) {
      // 初次打开时获取数据
      fetchOverviewData();

      // 设置定时刷新
      timerRef.current = setInterval(() => {
        fetchOverviewData(true); // true表示是静默刷新
      }, 5000); // 每5秒刷新一次
    }

    return () => {
      // 组件卸载或modal关闭时清除定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [open, modalConfig]);

  // 获取总览数据
  const fetchOverviewData = async (silent = false) => {
    if (!modalConfig || !pcsManageConfig) return;

    try {
      // 构建需要请求的所有字段
      let allItems: string[] = [];

      // 处理ParaInfo字段
      if (pcsManageConfig.ParaInfo?.Group) {
        const paraInfoItems = pcsManageConfig.ParaInfo.Group.map((item: any) => item.Name);
        allItems = [...allItems, ...paraInfoItems];
      }
      // 处理MultiWriteItems字段
      if (pcsManageConfig.MultiWriteItems?.Group) {
        const writeItems = pcsManageConfig.MultiWriteItems.Group.map(
          (item: ConfigItem) => item.Name,
        );
        allItems = [...allItems, ...writeItems];
      }
      // 过滤掉重复项和空项
      allItems = [...new Set(allItems)].filter(Boolean);

      if (allItems.length === 0) {
        if (!silent) {
          console.warn('PcsOverviewModal: 没有找到需要请求的数据项');
        }
        return;
      }

      // 构建请求数据
      // 并机功率比例设置取PCS1中的值
      const requestData = [...multiDeviceNames, allDevice[0]].map((deviceId: string) => ({
        deviceList: [deviceId],
        items: allItems,
      }));

      if (requestData.length === 0) {
        if (!silent) {
          console.warn('PcsOverviewModal: 没有找到设备ID');
        }
        return;
      }

      // 发送请求获取总览数据
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', requestData);

      if (res.code === 0 && res.data && res.data.length > 0) {
        // 处理返回的数据
        const formattedData: Record<string, any> = {};

        res.data.forEach((deviceData: any) => {
          const deviceCode = deviceData.device?.code || deviceData.device?.deviceID;
          if (deviceCode && deviceData.itemList) {
            formattedData[deviceCode] = {};
            deviceData.itemList.forEach((item: any) => {
              if (item.name) {
                formattedData[deviceCode][item.name] = item.value;
              }
            });
          }
        });

        if (process.env.NODE_ENV === 'development') {
          console.log('PcsOverviewModal: 获取到的数据', formattedData);
        }
        setOverviewData(formattedData);
        if (!initialized) {
          setInitialized(true);
        }
      } else {
        if (!silent) {
          console.error('PcsOverviewModal: 返回数据格式错误', res);
        }
      }
    } catch (error) {
      console.error('获取PCS总览数据失败:', error);
      if (!silent) {
        console.error(error);
      }
    }
  };

  // 处理设备选择变化
  const handleDeviceSelectChange = (deviceId: string, checked: boolean) => {
    const newSelected = { ...selectedDevices, [deviceId]: checked };
    setSelectedDevices(newSelected);

    // 检查是否全选
    const allChecked = Object.values(newSelected).every((v) => v);
    setSelectAll(allChecked);
  };

  // 处理全选/全不选
  const handleSelectAllChange = (checked: boolean) => {
    setSelectAll(checked);

    // 更新所有设备选择状态
    const newSelected = { ...selectedDevices };
    Object.keys(newSelected).forEach((key) => {
      newSelected[key] = checked;
    });
    setSelectedDevices(newSelected);
  };

  // 获取当前选中的设备ID列表
  const getSelectedDeviceIds = () => {
    return Object.entries(selectedDevices)
      .filter(([_, selected]) => selected)
      .map(([deviceId]) => deviceId);
  };

  // 确认框内容生成函数
  const getConfirmContent = useCallback(
    (action: { type: string; descript: string }) => {
      const selectedCount = getSelectedDeviceIds().length;
      return `当前操作 ${action.descript}，将对已选择的 ${selectedCount} 个设备生效，是否确认？`;
    },
    [selectedDevices],
  );

  // 自定义渲染设备选择器
  const renderDeviceSelect = () => {
    if (!pcsManageConfig?.DeviceSelect || Object.keys(pcsManageConfig.DeviceSelect).length === 0) {
      return null;
    }

    return (
      <div className="device-select-container mb-6">
        <div className="device-select-item">
          <Checkbox checked={selectAll} onChange={(e) => handleSelectAllChange(e.target.checked)}>
            {pcsManageConfig.DeviceSelect.All || '全部'}
          </Checkbox>
        </div>

        {deviceOptions.map((option, index) => (
          <div key={index} className="device-select-item">
            <Checkbox
              checked={selectedDevices[option.value]}
              onChange={(e) => handleDeviceSelectChange(option.value, e.target.checked)}
            >
              {option.label}
            </Checkbox>
          </div>
        ))}
      </div>
    );
  };

  // 渲染指令下发按钮
  const renderButtonItems = () => {
    if (!pcsManageConfig?.ButtonItems) return null;

    return (
      <CommandButtonContainer
        refreshData={fetchOverviewData}
        deviceCode={getSelectedDeviceIds()[0] || ''}
        getConfirmContent={getConfirmContent}
        beforeExec={(params) => {
          // 验证是否有选中的设备
          const selectedIds = getSelectedDeviceIds();
          if (selectedIds.length === 0) {
            message.warning('请至少选择一个设备');
            return false;
          }
          return true;
        }}
        setRequestParams={(params) => {
          // 构建多设备命令参数
          const { name, value } = params;
          const selectedIds = getSelectedDeviceIds();
          const devices = selectedIds.map((deviceId) => ({
            msgId: Date.now().toString() + Math.random().toString().substr(2, 4),
            deviceCode: deviceId,
            addresses: [
              {
                name,
                value,
              },
            ],
          }));

          return { devices };
        }}
      >
        {({ renderCommandButton }) => (
          <>
            <SectionTitle title={pcsManageConfig?.ButtonItems?.Label || '指令下发'} />
            <div className="device-select-section">{renderDeviceSelect()}</div>
            <div className="command-buttons-container mb-8">
              {pcsManageConfig?.ButtonItems?.Group?.map((item: any, index: number) => {
                // 扩展item类型以适应可能包含Api的情况
                const itemWithApi = item as typeof item & { Api?: string };

                // 获取选中设备ID
                const selectedIds = getSelectedDeviceIds();

                return renderCommandButton({
                  key: index,
                  descript: item.Descript,
                  value: item.Value, // 使用原始的value，不再修改
                  api: itemWithApi.Api,
                  name: item.Name,
                  index: index,
                  disabled: selectedIds.length === 0,
                });
              })}
            </div>
          </>
        )}
      </CommandButtonContainer>
    );
  };

  // 渲染参数信息
  const renderParaInfo = () => {
    if (!pcsManageConfig?.ParaInfo) return null;

    return (
      <>
        <SectionTitle title={pcsManageConfig.ParaInfo.Label || '参数信息'} />
        <Row gutter={[24, 14]} className="para-info-section mb-8">
          {pcsManageConfig.ParaInfo.Group?.map((item: any, index: number) => (
            <Col key={index} span={8}>
              <div className="flex items-center">
                <span className="label-text">{item.Descript}：</span>
                <span className="value-text">
                  {multiDeviceNames.length > 0 && overviewData[multiDeviceNames[0]]
                    ? overviewData[multiDeviceNames[0]][item.Name] !== undefined
                      ? overviewData[multiDeviceNames[0]][item.Name]
                      : '-'
                    : '-'}
                  {item.Unit ? ` ${item.Unit}` : ''}
                </span>
              </div>
            </Col>
          ))}
        </Row>
      </>
    );
  };
  // 渲染参数配置 - 使用MultiWriteItemsContainer
  const renderMultiWriteItems = () => {
    if (!pcsManageConfig?.MultiWriteItems) return null;
    return (
      <>
        <SectionTitle title={pcsManageConfig.MultiWriteItems.Label || '参数配置'} />
        <MultiWriteItemsContainer
          configItems={pcsManageConfig.MultiWriteItems.Group || []}
          // 并机功率比例设置取PCS1中的值
          detailData={overviewData?.[allDevice[0]]}
          deviceCode={allDevice}
          refreshData={fetchOverviewData}
          columnsPerRow={3}
        />
      </>
    );
  };
  // 渲染空状态
  const renderEmptyState = () => (
    <div className="flex h-[200px] flex-col items-center justify-center">
      <Spin tip="加载中..." />
    </div>
  );

  return (
    <>
      <Modal
        title={pcsManageLabel || 'PCS'}
        open={open}
        onCancel={() => onOpenChange(false)}
        footer={null}
        width={1128}
        className="pcs-overview-modal"
        maskClosable={false}
        closeIcon={<CloseOutlined className="modal-close-icon" />}
        centered
      >
        {!initialized && !pcsManageConfig ? (
          renderEmptyState()
        ) : (
          <div className="pcs-overview-content">
            {/* 指令下发 */}
            {renderButtonItems()}

            {/* 参数信息 */}
            {renderParaInfo()}

            {/* 参数配置 */}
            {renderMultiWriteItems()}

            {!pcsManageConfig && (
              <div className="py-8 text-center text-[#E3F8FF]">
                未找到PCS总览配置信息，请检查配置
              </div>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};
