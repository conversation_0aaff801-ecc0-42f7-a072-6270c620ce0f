@import '../../components/modalCommon.less';

/* PCS总览模态框样式 */
.pcs-overview-modal,
div.ant-modal.pcs-overview-modal {
  .device-modal-common();

  /* PCS总览模态框特定样式 */
  .pcs-overview-content {
    .modal-content();
    padding: 0;
  }

  /* 设备选择器样式 */
  .device-select-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 24px;
    gap: 46px;

    .device-select-item {
      display: flex;
      align-items: center;

      .ant-checkbox-wrapper {
        color: #E3F8FF;
        font-size: 22px;
        line-height: 1em;
        display: flex;
        align-items: center;

        /* 隐藏原生勾选框 */
        .ant-checkbox {
          width: 46px !important;
          height: 46px !important;
          margin-right: 8px;
          position: relative;

          .ant-checkbox-inner {
            opacity: 0;
          }

          /* 自定义勾选框 */
          &::before {
            content: '';
            position: absolute;
            width: 46px;
            height: 46px;
            top: 0;
            left: 0;
            background-image: url('/figma_images/checkbox_unchecked.svg');
            background-size: contain;
            background-repeat: no-repeat;
          }

          /* 选中状态 */
          &.ant-checkbox-checked::before {
            background-image: url('/figma_images/checkbox_checked.svg');
          }
        }

        /* 文本样式 */
        span {
          font-family: 'PingFang SC';
          font-weight: 400;
          font-size: 22px;
          line-height: 22px;
          color: #E3F8FF;
          padding-left: 8px;
        }
      }
    }
  }

  /* 指令下发标题 */
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    &:before {
      content: '';
      width: 4px;
      height: 20px;
      background-color: #07C6F0;
      margin-right: 8px;
    }

    .title-text {
      color: #E3F8FF;
      font-size: 22px;
      line-height: 1em;
      font-family: 'PingFang SC';
      font-weight: 400;
    }
  }

  /* 命令按钮容器样式 */
  .command-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 16px;
    margin-bottom: 32px;

    .command-button {
      flex: 1;
      min-width: 125px;
      height: 52px;
      padding: 16px 9px;
      border-radius: 6px;
      font-size: 20px;
      font-family: 'PingFang SC';
      font-weight: 400;
      text-align: center;
      color: #E3F8FF !important;
      border: 1px solid #39ABFF;
      background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);

      &:hover,
      &:focus {
        color: #6DE875 !important;
        border-color: #39ABFF;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      span {
        color: #6DE875;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 100% */
      }
    }
  }

  /* 参数信息部分样式 */
  .para-info-section {
    margin-bottom: 32px;

    .ant-row {
      row-gap: 14px !important;
    }

    .ant-col {
      .flex {
        min-height: 28px;
      }
    }

    .label-text {
      color: #9CCEFF !important;
      font-size: 20px !important;
      margin-right: 8px;
    }

    .value-text {
      color: #E3F8FF !important;
      font-size: 20px !important;
      line-height: 1em;
    }
  }

  /* 修复滚动区域样式 */
  .ant-modal-body {

    // padding: 24px;
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #3D5DAC62;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background: #3D5DAC;
    }
  }
}