import React from 'react';
import { RightOutlined } from '@ant-design/icons';

interface SectionHeaderProps {
  title: string;
  label?: string;
  onLabelClick?: () => void;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  label,
  onLabelClick
}) => {
  return (
    <div className="relative h-[43px] w-full flex justify-between items-center px-[18px]">
      <div className="flex items-center h-full">
        <div 
          className="text-[22px] font-semibold leading-[24px]"
          style={{ 
            fontFamily: "PingFang SC", 
            textShadow: "0px 0px 12px rgba(16, 124, 252, 0.40)",
            background: "linear-gradient(to bottom, #D0EDFF, #56B2F3)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            backgroundClip: "text"
          }}
        >
          {title}
        </div>
      </div>
      
      {label && (
        <div 
          className="text-[#C7F3FF] text-[16px] font-normal leading-[16px] flex items-center cursor-pointer"
          style={{ fontFamily: "PingFang SC" }}
          onClick={onLabelClick}
        >
          {label} <RightOutlined className="ml-1" />
        </div>
      )}
    </div>
  );
}; 