import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Divider } from "antd";
import { AlarmTag } from "../AlarmTag";
import { RightOutlined } from '@ant-design/icons';

// 定义充电枪属性类型
interface ChargingGunProperty {
  name: string;
  label: string;
  value: any;
  unit?: string;
}

// ChargingStationCard属性定义
export interface ChargingStationCardProps {
  id: number;
  name: string; // 充电桩名称，如"1#充电桩"
  status: string; // 在线状态："online" | "offline"
  statusText: string; // 状态文本，如"在线"
  statusColor: string; // 状态颜色
  textColor: string; // 文本颜色
  borderColor: string; // 边框颜色
  guns: ChargingGunProperty[]; // 充电枪属性列表
  hasAlarm?: boolean; // 是否有告警
  onDetailClick?: (id: number) => void; // 详情点击回调
}

export const ChargingStationCard: React.FC<ChargingStationCardProps> = ({
  id,
  name,
  status,
  statusText,
  statusColor,
  textColor,
  borderColor,
  guns = [],
  hasAlarm = false,
  onDetailClick,
}) => {
  // 根据状态确定样式
  const isOffline = status === "offline";
  const nameColor = isOffline ? "#4365A9" : "#00C8FF";
  const textStyle = isOffline ? "#4365A9" : "#C7F3FF";
  const detailColor = isOffline ? "#4365A9" : "#C7F3FF";
  
  return (
    <Card
      className={`h-[112px] w-full border-[2px] border-solid overflow-hidden ${isOffline ? "border-[#4365a9]" : "border-[#39abff]"}`}
      style={{ 
        background: "linear-gradient(180deg, rgba(33, 85, 164, 0.20) 0%, rgba(23, 63, 129, 0.00) 100%, rgba(14, 80, 188, 1.00) 0%)"
      }}
      onClick={(e) => {
        e.stopPropagation();
        !isOffline && onDetailClick?.(id);
      }}
    >
      <CardContent className="p-2 h-full flex flex-col">
        <div className="flex items-center justify-between w-full mb-1">
          {/* 充电桩名称 */}
          <div className="flex items-center gap-2.5 flex-1">
            <span
              className="text-[18px] font-normal leading-[18px] whitespace-nowrap"
              style={{ 
                fontFamily: "PingFang SC",
                color: nameColor,
                textShadow: isOffline ? "none" : "0px 0px 4px rgba(0, 200, 255, 0.4)"
              }}
            >
              {name}
            </span>
            
            {/* 告警标志 */}
            {hasAlarm && (
              <div className="mx-2">
                <AlarmTag />
              </div>
            )}
          </div>
          
          {/* 在线状态和详情按钮 */}
          <div className="flex items-center gap-3.5">
            <div className="flex items-center gap-1">
              <div
                className={`w-[6px] h-[6px] rounded-full`}
                style={{ 
                  backgroundColor: statusColor,
                  boxShadow: status === "online" ? "0px 0px 4px 1px rgba(0, 255, 80, 0.7)" : "none"
                }}
              />
              <div
                className="text-[16px] font-normal whitespace-nowrap"
                style={{ 
                  fontFamily: "PingFang SC", 
                  color: statusColor,
                  textShadow: status === "online" ? "0px 0px 6px rgba(0, 255, 80, 0.7)" : "none"
                }}
              >
                {statusText}
              </div>
            </div>
            
            {/* 分割线 */}
            <Divider type="vertical" className="h-5 m-0 border-[#2568C2]" />
            
            {onDetailClick && (
              <div 
                className="flex items-center gap-2 cursor-pointer"
                style={{ 
                  fontFamily: "PingFang SC",
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "16px",
                  color: detailColor
                }}
              >
                详情 <RightOutlined className="ml-0.5 text-xs" />
              </div>
            )}
          </div>
        </div>

        {/* 充电桩内容 */}
        {isOffline ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-[18px] font-normal"
              style={{ 
                fontFamily: "PingFang SC",
                color: "#4365A9"
              }}
            >
              关机
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-stretch gap-1.5 py-1">
            {guns.map((gun, index) => (
              <div
                key={index}
                className="flex-1 flex flex-col px-4 py-[5px] border border-solid border-transparent shadow-[inset_0px_0px_8.34px_#00a6ffcc] [border-image:linear-gradient(360deg,rgba(32,143,255,1)_0%,rgba(36,145,255,0)_100%)_1] [background:linear-gradient(0deg,rgba(17,72,173,0.6)_0%,rgba(5,48,126,0)_100%)]"
              >
                <div className="flex-none">
                  <span 
                    className="text-[14px] font-normal"
                    style={{ 
                      fontFamily: "PingFang SC",
                      color: "#00C8FF"
                    }}
                  >
                    {gun.label}
                  </span>
                </div>
                <div className="flex-1 flex items-center">
                  <span 
                    className="text-[16px] font-normal"
                    style={{ 
                      fontFamily: "PingFang SC",
                      color: textStyle
                    }}
                  >
                    {gun.value || '--'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 