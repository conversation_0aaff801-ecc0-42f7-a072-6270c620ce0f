@import '../modalCommon.less';

/* 水冷单元详情模态框样式 */
.cooling-unit-detail-modal,
div.ant-modal.cooling-unit-detail-modal {
  .device-modal-common();
  .cooling-unit-detail-content {
    .command-buttons-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .ant-col {
        width: auto !important;
        flex: none !important;
      }
      
      /* 针对ButtonGroups中的按钮特别定制 */
      .command-button {
        min-width: 108px;
        height: 42px;
        padding: 11px 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);
        border: 1px solid #39ABFF;
        
        /* 启动自循环按钮宽度单独设置 */
        &.wider-button {
          min-width: 120px;
        }
        
        span {
          color: #6DE875;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
        
        &:hover, &:focus {
          border: 1px solid #39ABFF;
          background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);
          
          span {
            color: #6DE875 !important;
          }
        }
      }
    }
    
    /* 指令下发组样式 */
    .command-item-group {
      .flex {
        display: flex;
        align-items: center;
        
        .label-text {
          font-size: 20px;
          color: #9CCEFF;
          flex-shrink: 0;
          width: 160px;
          white-space: wrap;
        }
        
        .command-buttons-container {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
        }
      }
    }
    
    .para-info-section {
      margin-bottom: 32px;
    }
    
    .param-config-section {
      margin-bottom: 32px;
      
      .ant-row {
        row-gap: 14px !important;
      }
      
      .ant-form-item {
        margin-bottom: 0;
      }
      
      .param-config-item {
        margin-bottom: 0;
        
        .param-input-wrapper {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .unit-text {
            white-space: nowrap;
          }
        }
      }
    }
    
    /* 水冷状态特定样式 */
    .cooling-status-section {
      padding-bottom: 16px;
      margin-top: 8px;
      
      .ant-col {
        .flex {
          min-height: 28px;
        }
      }
    }
  }
}