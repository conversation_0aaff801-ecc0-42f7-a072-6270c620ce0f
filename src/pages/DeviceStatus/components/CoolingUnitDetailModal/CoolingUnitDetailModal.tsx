import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Modal, Spin, Row, Col, message } from 'antd';
import { httpPost } from '@/shared/http';
import { useModalConfig } from '../../context/ModalConfigContext';
import { ModalTypeKey } from '../../types';
import { useContentDictionary } from '../../hooks/useContentDictionary';
import { DeviceType } from '../../types';
import { SectionTitle } from '../SectionTitle';
import { CommandButtonContainer } from '../CommandButton';
import { MultiWriteItemsContainer } from '../MultiWriteItems';
import './styles.less';
import { CloseOutlined } from '@ant-design/icons';

interface CoolingUnitDetailModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceCode: string;
  deviceName: string;
}

interface DetailData {
  [key: string]: any;
}

interface ConfigItem {
  Name: string;
  Descript: string;
  Unit?: string;
  ViewType?: string;
  Value?: any;
  Type?: string;
}

interface ApiItem {
  Type: string;
  Api: string;
  Value: any;
  Descript: string;
}

interface ButtonGroup {
  Label: string;
  Group: ApiItem[];
}

interface ItemGroup {
  Label: string;
  Group: ConfigItem[];
}

interface CoolingUnitConfigType {
  ButtonGroups?: ButtonGroup[];
  ButtonItems?: {
    Label?: string;
    Items?: ItemGroup[];
  };
  ParaInfo?: {
    Label?: string;
    Group?: ConfigItem[];
  };
  MultiWriteItems?: {
    Label?: string;
    Group?: ConfigItem[];
  };
  Single?: ConfigItem[];
  deviceID?: {
    Code: string;
    Name: string;
    [key: string]: string;
  }[];
}

export const CoolingUnitDetailModal: React.FC<CoolingUnitDetailModalProps> = ({
  open,
  onOpenChange,
  deviceCode,
  deviceName,
}) => {
  const modalConfig = useModalConfig();
  const { getContentValue } = useContentDictionary();
  const [detailData, setDetailData] = useState<DetailData>({});
  const [initialized, setInitialized] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取水冷机组配置
  const coolingUnitConfig = modalConfig?.DeviceParaItems?.[
    modalConfig?.[ModalTypeKey.TEMPER_CONTR_KEY]
  ]?.DataView as CoolingUnitConfigType | undefined;

  // 初始化数据
  useEffect(() => {
    if (open && deviceCode && modalConfig) {
      // 初次打开时获取数据
      fetchDetailData();

      // 设置定时刷新
      timerRef.current = setInterval(() => {
        fetchDetailData(true); // true表示是静默刷新
      }, 5000); // 每5秒刷新一次
    }

    return () => {
      // 组件卸载或modal关闭时清除定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [open, deviceCode, modalConfig]);

  const fetchDetailData = async (silent = false) => {
    if (!modalConfig || !deviceCode || !coolingUnitConfig) return;

    try {
      // 构建需要请求的所有字段
      let allItems: string[] = [];

      // 处理Single字段
      if (coolingUnitConfig.Single) {
        const singleItems = coolingUnitConfig.Single.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...singleItems];
      }

      // 处理ParaInfo字段
      if (coolingUnitConfig.ParaInfo?.Group) {
        const paraInfoItems = coolingUnitConfig.ParaInfo.Group.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...paraInfoItems];
      }

      // 处理MultiWriteItems字段
      if (coolingUnitConfig.MultiWriteItems?.Group) {
        const writeItems = coolingUnitConfig.MultiWriteItems.Group.map(
          (item: ConfigItem) => item.Name,
        );
        allItems = [...allItems, ...writeItems];
      }

      // 过滤掉重复项和空项
      allItems = [...new Set(allItems)].filter(Boolean);

      // 构建请求数据
      const requestData = [
        {
          deviceList: [deviceCode],
          items: allItems,
        },
      ];

      // 发送请求获取详情数据
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', requestData);

      if (res.code === 0 && res.data && res.data.length > 0) {
        const deviceData = res.data[0];

        // 将itemList中的数据转换成一个键值对对象
        const formattedData: DetailData = {};
        if (deviceData.itemList && Array.isArray(deviceData.itemList)) {
          deviceData.itemList.forEach((item: any) => {
            if (item.name) {
              formattedData[item.name] = item.value;
            }
          });
        }

        if (process.env.NODE_ENV === 'development') {
          console.log('CoolingUnitDetailModal: 获取到的数据', { deviceData, formattedData });
        }

        setDetailData(formattedData);
        if (!initialized) {
          setInitialized(true);
        }
      } else {
        if (!silent) {
          console.error('CoolingUnitDetailModal: 返回数据格式错误', res);
        }
      }
    } catch (error) {
      console.error('获取水冷机组详情数据失败:', error);
      if (!silent) {
        message.error('获取水冷机组详情数据失败');
      }
    }
  };

  // 确认框内容生成函数
  const getConfirmContent = useCallback((action: { type: string; descript: string }) => {
    return `当前操作 ${action.descript} 是否确认？`;
  }, []);

  // 获取字段值并处理Content类型的翻译
  const getFieldValue = useCallback(
    (name: string, viewType?: string) => {
      const value = detailData[name];

      if (viewType === 'Content') {
        return getContentValue(DeviceType.TemperContr, name, value);
      }

      return value !== undefined && value !== null ? value : '-';
    },
    [detailData, getContentValue],
  );

  // 渲染按钮组（ButtonGroups）使用CommandButtonContainer
  const renderButtonGroups = () => {
    if (!coolingUnitConfig?.ButtonGroups || coolingUnitConfig.ButtonGroups.length === 0)
      return null;

    return (
      <CommandButtonContainer
        refreshData={fetchDetailData}
        deviceCode={deviceCode}
        getConfirmContent={getConfirmContent}
      >
        {({ renderCommandButton }) => (
          <>
            {coolingUnitConfig?.ButtonGroups?.map(
              (buttonGroup: ButtonGroup, groupIndex: number) => (
                <div key={groupIndex} className="mb-[32px]">
                  <SectionTitle title={buttonGroup.Label || ''} />
                  <div className="command-buttons-container mb-8">
                    {buttonGroup.Group?.map((item: ApiItem, index: number) => {
                      // 判断是否为"启动自循环"按钮
                      const isWiderButton = item.Descript.includes('自循环');
                      // 水冷中如果存在pcsId则替换为水冷设备对应的pcsid
                      if (item.Value.pcsId) {
                        if (
                          coolingUnitConfig?.deviceID &&
                          Array.isArray(coolingUnitConfig.deviceID)
                        ) {
                          const deviceIDItem = coolingUnitConfig.deviceID.find(
                            (item: { Code: string }) => item.Code === deviceCode,
                          );
                          if (deviceIDItem) {
                            // 翻译设备类型
                            item.Value.pcsId = deviceIDItem.PCS;
                          }
                        }
                      }

                      return renderCommandButton({
                        key: index,
                        descript: item.Descript,
                        value: item.Value,
                        api: item.Api,
                        type: item.Type,
                        index: index,
                        isWiderButton: isWiderButton,
                      });
                    })}
                  </div>
                </div>
              ),
            )}
          </>
        )}
      </CommandButtonContainer>
    );
  };

  // 渲染按钮项（ButtonItems）使用CommandButtonContainer
  const renderButtonItems = () => {
    if (!coolingUnitConfig?.ButtonItems) return null;
    return (
      <CommandButtonContainer
        refreshData={fetchDetailData}
        deviceCode={deviceCode}
        getConfirmContent={getConfirmContent}
      >
        {({ renderCommandButton }) => (
          <>
            <SectionTitle title={coolingUnitConfig?.ButtonItems?.Label || '指令下发'} />
            <Row gutter={[50, 24]} className="mb-8">
              {coolingUnitConfig?.ButtonItems?.Items?.map(
                (itemGroup: ItemGroup & { DeviceType?: string }, groupIndex: number) => {
                  let useDeviceType = itemGroup.DeviceType || '';
                  if (useDeviceType) {
                    // 如果有DeviceType并且当前配置项有deviceID，则使用deviceID的Name
                    if (coolingUnitConfig?.deviceID && Array.isArray(coolingUnitConfig.deviceID)) {
                      const deviceIDItem = coolingUnitConfig.deviceID.find(
                        (item: { Code: string }) => item.Code === deviceCode,
                      );
                      if (deviceIDItem) {
                        // 翻译设备类型
                        useDeviceType = deviceIDItem[useDeviceType];
                      }
                    }
                  }
                  return (
                    <Col key={groupIndex} span={(itemGroup.Group?.length ?? 2) * 6}>
                      <div className="command-item-group">
                        <div className="flex items-center">
                          <span className="label-text mr-3 whitespace-nowrap">
                            {itemGroup.Label}
                          </span>
                          <div className="command-buttons-container">
                            {itemGroup.Group?.map((item: ConfigItem, index: number) => {
                              // 扩展ConfigItem类型以适应可能包含Api的情况
                              const itemWithApi = item as ConfigItem & { Api?: string };

                              return renderCommandButton({
                                key: index,
                                descript: item.Descript,
                                value: item.Value,
                                api: itemWithApi.Api,
                                name: item.Name,
                                index: index,
                                deviceType: useDeviceType,
                              });
                            })}
                          </div>
                        </div>
                      </div>
                    </Col>
                  );
                },
              )}
            </Row>
          </>
        )}
      </CommandButtonContainer>
    );
  };

  // 渲染参数信息
  const renderParaInfo = () => {
    if (!coolingUnitConfig?.ParaInfo) return null;

    return (
      <>
        <SectionTitle title={coolingUnitConfig.ParaInfo.Label || '水冷状态'} />
        <Row gutter={[24, 14]} className="para-info-section cooling-status-section mb-8">
          {coolingUnitConfig.ParaInfo.Group?.map((item: ConfigItem, index: number) => (
            <Col key={index} span={8}>
              <div className="flex items-center">
                <span className="label-text">{item.Descript}：</span>
                <span className="value-text">
                  {getFieldValue(item.Name, item.ViewType)}
                  {item.Unit ? ` ${item.Unit}` : ''}
                </span>
              </div>
            </Col>
          ))}
        </Row>
      </>
    );
  };

  // 渲染参数配置 - 使用MultiWriteItemsContainer
  const renderMultiWriteItems = () => {
    if (!coolingUnitConfig?.MultiWriteItems) return null;

    return (
      <>
        <SectionTitle title={coolingUnitConfig.MultiWriteItems.Label || '参数配置'} />
        <MultiWriteItemsContainer
          configItems={coolingUnitConfig.MultiWriteItems.Group || []}
          detailData={detailData}
          deviceCode={deviceCode}
          refreshData={fetchDetailData}
          columnsPerRow={3}
        />
      </>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="flex h-[200px] flex-col items-center justify-center">
      <Spin tip="加载中..." />
    </div>
  );

  return (
    <>
      <Modal
        title={`${deviceName || '冷水机组'}`}
        open={open}
        onCancel={() => onOpenChange(false)}
        footer={null}
        width={1128}
        className="cooling-unit-detail-modal device-modal-common"
        maskClosable={false}
        closeIcon={<CloseOutlined className="modal-close-icon" />}
        centered
      >
        {!initialized && !coolingUnitConfig ? (
          renderEmptyState()
        ) : (
          <div className="cooling-unit-detail-content modal-content">
            {/* 水冷状态 - ParaInfo */}
            {renderParaInfo()}
            {/* 水冷机组启停 - ButtonGroups */}
            {renderButtonGroups()}

            {/* 指令下发 - ButtonItems */}
            {renderButtonItems()}

            {/* 参数配置 - MultiWriteItems */}
            {renderMultiWriteItems()}

            {!coolingUnitConfig && (
              <div className="py-8 text-center text-[#E3F8FF]">
                未找到水冷机组配置信息，请检查配置
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 虚拟键盘和确认框已集成到MultiWriteItemsContainer组件中 */}
    </>
  );
};
