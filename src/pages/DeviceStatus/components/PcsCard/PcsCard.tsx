import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { RightOutlined } from "@ant-design/icons";
import { AlarmTag } from "../AlarmTag";

interface PcsProperty {
  name: string;
  label: string;
  value: any;
  unit?: string;
  viewType?: string;
}

interface PcsCardProps {
  name: string;
  status: "online" | "offline";
  statusText: string;
  statusColor: string;
  textColor: string;
  borderColor: string;
  properties: PcsProperty[];
  hasAlarm?: boolean;
  onDetailClick?: () => void;
  onCommIdClick?: () => void;
}

export const PcsCard: React.FC<PcsCardProps> = ({
  name,
  status,
  statusText,
  statusColor,
  textColor,
  borderColor,
  properties,
  hasAlarm = false,
  onDetailClick,
  onCommIdClick,
}) => {
  // 根据状态确定样式
  const isOffline = status === "offline";
  const nameColor = isOffline ? "#4365A9" : "#00C8FF";
  const textStyle = isOffline ? "#4365A9" : "#C7F3FF";
  const detailTextColor = isOffline ? "#4365A9" : "#C7F3FF";
  const valueColor = isOffline ? "#4365A9" : "#00C8FF";

  // 并机ID特殊处理
  // const KEYWORD = '并机ID';
  const useValue = (label: string, value: string | number) => {
    // 如果label是并机ID，且value是0，则返回 -
    // if (label === KEYWORD && Number(value) === 0) {
    //   return '-';
    // }
    return value;
  }
  
  return (
    <Card
      className={`h-[112px] flex flex-col items-start gap-1 px-4 py-2 relative w-full border-[2px] border-solid shadow-none cursor-pointer overflow-hidden`}
      style={{ 
        borderColor,
        background: "linear-gradient(180deg, rgba(0, 73, 149, 0.10) 0%, rgba(20, 109, 193, 0.10) 100%)",
        boxShadow: "0px 0px 10px 0px rgba(0, 174, 255, 0.16) inset" 
      } }
      onClick={(e) => {
        e.stopPropagation();
        !isOffline && onDetailClick?.();
      }}
    >
      {/* onClick={onCommIdClick} */}
      <div className="flex items-center gap-0.5 relative w-full flex-[0_0_auto] justify-between mb-1">
        <div className="flex items-center">
          <div
            className="text-[18px] leading-[18px] whitespace-nowrap"
            style={{ 
              fontFamily: "PingFang SC",
              color: nameColor,
              textShadow: isOffline ? "none" : "0px 0px 4px rgba(0, 200, 255, 0.4)"
            }}
          >
            {name}
          </div>
        </div>

        <div className="inline-flex items-center gap-2 relative flex-[0_0_auto]">
          {hasAlarm && (
            <div className="mr-2">
              <AlarmTag />
            </div>
          )}
          
          <div
            className="relative w-[6px] h-[6px] rounded-full"
            style={{ 
              backgroundColor: statusColor,
              boxShadow: status === "online" ? "0px 0px 4px 1px rgba(0, 255, 80, 0.7)" : "none"
            }}
          />

          <div className="inline-flex items-center relative flex-[0_0_auto]">
            <div
              className="text-[16px] font-normal whitespace-nowrap"
              style={{ 
                fontFamily: "PingFang SC", 
                color: statusColor,
                textShadow: status === "online" ? "0px 0px 6px rgba(0, 255, 80, 0.7)" : "none"
              }}
            >
              {statusText}
            </div>
          </div>
        </div>
      </div>

      <CardContent className="flex-col items-start justify-center gap-1 px-0 py-0 w-full flex relative p-0 max-h-[76px] overflow-hidden">
        {properties.map((prop, index) => (
          <div key={index} className="flex items-center justify-between w-full">
            <div
              style={{ 
                fontFamily: "PingFang SC",
                lineHeight: prop.label ? "20px" : "22px", 
                whiteSpace: "nowrap",
                textShadow: (!prop.label && !isOffline) ? "0px 0px 4px rgba(0, 200, 255, 0.4)" : "none",
                color: prop.label ? textStyle : valueColor,
                fontSize: prop.label ? "16px" : "18px"
              }}
            >
              {prop.label 
                ? <span>
                    <span className={isOffline ? "" : "opacity-80"}>{prop.label}: </span>
                    <span>{useValue(prop.label, prop.value)}{prop.unit}</span>
                  </span>
                : <span>{useValue(prop.label, prop.value)}{prop.unit}</span>
              }
            </div>
            
            {index === properties.length - 1 && onDetailClick && !isOffline && (
              <div 
                className="flex items-center text-[16px] ml-2"
                style={{ 
                  fontFamily: "PingFang SC",
                  fontWeight: 500,
                  lineHeight: "20px",
                  color: detailTextColor
                }}
              >
                详情 <RightOutlined className="ml-0.5 text-xs" />
              </div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}; 