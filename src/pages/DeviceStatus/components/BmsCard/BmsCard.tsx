import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlarmTag } from "../AlarmTag";
import { Divider } from "antd";
import { RightOutlined } from '@ant-design/icons';

// 定义电池支路属性类型
interface BatteryBranchProperty {
  name: string;
  label: string;
  value: any;
  unit: string;
  viewType: string;
  progress?: number | null;
}

// 定义电池支路类型
export interface BatteryBranch {
  name: string; // 支路名称，如"1#电池支路"
  properties: BatteryBranchProperty[]; // 支路属性
}

// BmsCard属性定义
export interface BmsCardProps {
  id: number;
  name: string; // BMS名称，如"1#BMS"
  status: string; // 在线状态："online" | "offline"
  statusText: string; // 状态文本，如"在线"
  statusColor: string; // 状态颜色
  textColor: string; // 文本颜色
  borderColor: string; // 边框颜色
  hasAlarm?: boolean; // 是否有告警
  batteryBranches: BatteryBranch[]; // 电池支路
  onDetailClick?: (id: number) => void; // 详情点击回调
}

export const BmsCard: React.FC<BmsCardProps> = ({
  id,
  name,
  status,
  statusText,
  statusColor,
  textColor,
  borderColor,
  hasAlarm = false,
  batteryBranches = [],
  onDetailClick,
}) => {
  const isOffline = status === "offline";
  return (
    <Card
      className={`w-full h-[112px] border-[2px] border-solid ${status === "online" ? "border-[#39abff]" : "border-[#4365a9]"}`}
      onClick={(e) => {
        e.stopPropagation();
        !isOffline && onDetailClick?.(id);
      }}
    >
      <CardContent className="p-2 overflow-hidden h-full flex flex-col">
        <div className="flex items-center gap-0.5 w-full mb-1">
          {/* BMS名称与告警标志 */}
          <div className="flex items-center gap-2.5 flex-1">
            <span
              className={`[font-family:'PingFang_SC-Semibold',Helvetica] font-normal ${status === "online" ? "text-[#00C8FF]" : "text-[#4365a9]"} text-base leading-[18px] whitespace-nowrap tracking-[0]`}
            >
              {name}
            </span>
            
            {/* 告警标志 */}
            {hasAlarm && (
              <div className="mx-2">
                <div className="w-[84px] h-[34px] relative">
                  <div className="absolute w-full h-full rounded-md bg-[rgba(238,31,31,0.4)] border border-[#EE1F1F]"></div>
                  <div className="absolute left-[15px] top-[8px] flex items-center">
                    <AlarmTag />
                    <span className="ml-[3px] text-white text-[16px] font-normal">告警</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 状态与详情按钮 */}
          <div className="flex items-center gap-3.5">
            <div className="flex items-center gap-0.5">
              <div
                className={`w-[6px] h-[6px] rounded-full`}
                style={{ 
                  backgroundColor: status === "online" ? "#00FF51" : "#4365a9",
                  boxShadow: status === "online" ? "0px 0px 4px 1px rgba(0, 255, 80, 0.7)" : "none"
                }}
              />
              <div
                className="text-[16px] font-normal whitespace-nowrap"
                style={{ 
                  fontFamily: "PingFang SC", 
                  color: status === "online" ? "#00FF51" : "#4365a9",
                  textShadow: status === "online" ? "0px 0px 6px rgba(0, 255, 80, 0.7)" : "none"
                }}
              >
                {statusText}
              </div>
            </div>

            {/* 分割线 */}
            <Divider type="vertical" className="h-5 m-0 border-[#2568C2]" />

            {/* 详情按钮 */}
            <div 
              className={`flex items-center gap-2 ${status === "online" ? "cursor-pointer" : "cursor-default"}`}
              style={{ 
                fontFamily: "PingFang SC",
                fontWeight: 400,
                fontSize: "16px",
                lineHeight: "16px",
                color: status === "online" ? "#C7F3FF" : "#4365a9"
              }}
            >
              详情 <RightOutlined className="ml-0.5 text-xs" />
            </div>
          </div>
        </div>

        {/* 电池支路信息 */}
        {!isOffline ? (
          <div className="flex flex-1 items-stretch gap-2 w-full mt-1 max-h-[68px] overflow-hidden">
            {batteryBranches.map((branch, branchIndex) => (
              <div
                key={branchIndex}
                className="flex flex-col justify-between gap-1 px-3 py-[5px] flex-1 border border-solid border-transparent shadow-[inset_0px_0px_8.34px_#00a6ffcc] [border-image:linear-gradient(360deg,rgba(32,143,255,1)_0%,rgba(36,145,255,0)_100%)_1] [background:linear-gradient(0deg,rgba(17,72,173,0.6)_0%,rgba(5,48,126,0)_100%)]"
              >
                <div className="flex items-start gap-0.5 w-full">
                  <div className="flex items-center gap-2.5 flex-1">
                    <span className="flex-1 [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#00C8FF] text-xs leading-[14px] tracking-[0]">
                      {branch.name}
                    </span>
                  </div>
                </div>

                {branch.properties.map((prop, propIndex) => (
                  <div key={propIndex} className="flex items-center gap-1 w-full">
                    <span className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-xs leading-3 whitespace-nowrap tracking-[0]">
                      {prop.label}
                    </span>
                    
                    {prop.viewType === 'progressAndLabel' ? (
                      <>
                        <div className="flex-1 flex items-center">
                          <div
                            className={`h-1.5 [background:linear-gradient(95deg,rgba(104,188,52,1)_0%,rgba(129,221,75,1)_56%,rgba(255,221,54,1)_100%)]`}
                            style={{ width: `${prop.progress || 0}%` }}
                          />
                          <div className="flex-1 h-1.5 bg-[#1e4d9f]" />
                        </div>
                        <span className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-xs leading-3 whitespace-nowrap tracking-[0]">
                          {prop.value}{prop.unit}
                        </span>
                      </>
                    ) : (
                      <span className="flex-1 text-right [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#c7f2ff] text-xs leading-3 tracking-[0]">
                        {propIndex === 0 ? String(prop.value).replace(/-/ig, '') : prop.value}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-[27px] w-full">
            <span className="[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#4365a9] text-base leading-[18px] whitespace-nowrap tracking-[0]">
              关机
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 