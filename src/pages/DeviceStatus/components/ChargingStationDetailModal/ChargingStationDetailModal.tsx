import React, { useEffect, useState, useCallback, useRef } from "react";
import { Modal, Spin, Row, Col, message } from "antd";
import { httpPost } from "@/shared/http";
import { useModalConfig } from "../../context/ModalConfigContext";
import { ModalTypeKey } from "../../types";
import { useContentDictionary } from "../../hooks/useContentDictionary";
import { DeviceType } from "../../types";
import { SectionTitle } from "../SectionTitle";
import { CommandButtonContainer } from "../CommandButton";
import { MultiWriteItemsContainer } from "../MultiWriteItems";
import './styles.less';
import { CloseOutlined } from "@ant-design/icons";

interface ChargingStationDetailModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceCode: string;
  deviceName: string;
}

interface DetailData {
  [key: string]: any;
}

interface ConfigItem {
  Name: string;
  Descript: string;
  Unit?: string;
  ViewType?: string;
  Value?: any;
  Type?: string;
}

interface ApiItem {
  Type: string;
  Api: string;
  Value: any;
  Descript: string;
}

interface ButtonGroup {
  Label: string;
  Group: ApiItem[];
}

interface ItemGroup {
  Label: string;
  Group: ConfigItem[];
}

interface ChargingStationConfigType {
  ButtonGroups?: ButtonGroup[];
  ButtonItems?: {
    Label?: string;
    Items?: ItemGroup[];
  };
  ParaInfo?: {
    Label?: string;
    Group?: ConfigItem[];
  };
  MultiWriteItems?: {
    Label?: string;
    Group?: ConfigItem[];
  };
  Single?: ConfigItem[];
}

export const ChargingStationDetailModal: React.FC<ChargingStationDetailModalProps> = ({
  open,
  onOpenChange,
  deviceCode,
  deviceName
}) => {
  const modalConfig = useModalConfig();
  const { getContentValue } = useContentDictionary();
  const [detailData, setDetailData] = useState<DetailData>({});
  const [initialized, setInitialized] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取充电桩配置
  const chargingStationConfig = modalConfig?.DeviceParaItems?.[modalConfig?.[ModalTypeKey.CHARGING_STATION_KEY]]?.DataView as ChargingStationConfigType | undefined;

  // 初始化数据
  useEffect(() => {
    if (open && deviceCode && modalConfig) {
      // 初次打开时获取数据
      fetchDetailData();

      // 设置定时刷新
      timerRef.current = setInterval(() => {
        fetchDetailData(true); // true表示是静默刷新
      }, 5000); // 每5秒刷新一次
    }

    return () => {
      // 组件卸载或modal关闭时清除定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [open, deviceCode, modalConfig]);

  const fetchDetailData = async (silent = false) => {
    if (!modalConfig || !deviceCode || !chargingStationConfig) return;

    try {
      // 构建需要请求的所有字段
      let allItems: string[] = [];
      
      // 处理Single字段
      if (chargingStationConfig.Single) {
        const singleItems = chargingStationConfig.Single.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...singleItems];
      }
      
      // 处理ParaInfo字段
      if (chargingStationConfig.ParaInfo?.Group) {
        const paraInfoItems = chargingStationConfig.ParaInfo.Group.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...paraInfoItems];
      }
      
      // 处理MultiWriteItems字段
      if (chargingStationConfig.MultiWriteItems?.Group) {
        const writeItems = chargingStationConfig.MultiWriteItems.Group.map((item: ConfigItem) => item.Name);
        allItems = [...allItems, ...writeItems];
      }
      
      // 过滤掉重复项和空项
      allItems = [...new Set(allItems)].filter(Boolean);

      // 构建请求数据
      const requestData = [
        {
          deviceList: [deviceCode],
          items: allItems
        }
      ];

      // 发送请求获取详情数据
      const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues', requestData);
      
      if (res.code === 0 && res.data && res.data.length > 0) {
        const deviceData = res.data[0];
        
        // 将itemList中的数据转换成一个键值对对象
        const formattedData: DetailData = {};
        if (deviceData.itemList && Array.isArray(deviceData.itemList)) {
          deviceData.itemList.forEach((item: any) => {
            if (item.name) {
              formattedData[item.name] = item.value;
            }
          });
        }
        
        if (process.env.NODE_ENV === 'development') {
          console.log('ChargingStationDetailModal: 获取到的数据', { deviceData, formattedData });
        }
        
        setDetailData(formattedData);
        if (!initialized) {
          setInitialized(true);
        }
      } else {
        if (!silent) {
          console.error('ChargingStationDetailModal: 返回数据格式错误', res);
        }
      }
    } catch (error) {
      console.error("获取充电桩详情数据失败:", error);
      if (!silent) {
        message.error("获取充电桩详情数据失败");
      }
    }
  };

  // 确认框内容生成函数
  const getConfirmContent = useCallback((action: { type: string; descript: string; }) => {
    return `当前操作 ${action.descript} 是否确认？`;
  }, []);

  // 获取字段值并处理Content类型的翻译
  const getFieldValue = useCallback((name: string, viewType?: string) => {
    const value = detailData[name];
    
    if (viewType === 'Content') {
      return getContentValue(DeviceType.DCChargingStation, name, value);
    }
    
    return value !== undefined && value !== null ? value : '-';
  }, [detailData, getContentValue]);

  // 渲染按钮组（ButtonGroups）使用CommandButtonContainer
  const renderButtonGroups = () => {
    if (!chargingStationConfig?.ButtonGroups || chargingStationConfig.ButtonGroups.length === 0) return null;
    
    return (
      <CommandButtonContainer 
        refreshData={fetchDetailData} 
        deviceCode={deviceCode}
        getConfirmContent={getConfirmContent}
      >
        {({ renderCommandButton }) => (
          <>
            {chargingStationConfig?.ButtonGroups?.map((buttonGroup: ButtonGroup, groupIndex: number) => (
              <div key={groupIndex} className="mb-[32px]">
                <SectionTitle title={buttonGroup.Label || ""} />
                <div className="command-buttons-container mb-8">
                  {buttonGroup.Group?.map((item: ApiItem, index: number) => {
                    return renderCommandButton({
                      key: index,
                      descript: item.Descript,
                      value: item.Value,
                      api: item.Api,
                      type: item.Type,
                      index: index
                    });
                  })}
                </div>
              </div>
            ))}
          </>
        )}
      </CommandButtonContainer>
    );
  };

  // 渲染按钮项（ButtonItems）使用CommandButtonContainer
  const renderButtonItems = () => {
    if (!chargingStationConfig?.ButtonItems) return null;
    
    return (
      <CommandButtonContainer 
        refreshData={fetchDetailData} 
        deviceCode={deviceCode}
        getConfirmContent={getConfirmContent}
      >
        {({ renderCommandButton }) => (
          <>
            <SectionTitle title={chargingStationConfig?.ButtonItems?.Label || "指令下发"} />
            <Row gutter={[50, 24]} className="mb-8">
              {chargingStationConfig?.ButtonItems?.Items?.map((itemGroup: ItemGroup & { DeviceType?: string }, groupIndex: number) => (
                <Col key={groupIndex} span={12}>
                  <div className="command-item-group">
                    <div className="flex">
                      <span className="label-text">{itemGroup.Label}</span>
                      <div className="command-buttons-container">
                        {itemGroup.Group?.map((item: ConfigItem, index: number) => {
                          // 扩展ConfigItem类型以适应可能包含Api的情况
                          const itemWithApi = item as ConfigItem & { Api?: string };
                          
                          return renderCommandButton({
                            key: index,
                            descript: item.Descript,
                            value: item.Value,
                            api: itemWithApi.Api,
                            name: item.Name,
                            index: index,
                            deviceType: itemGroup.DeviceType
                          });
                        })}
                      </div>
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </>
        )}
      </CommandButtonContainer>
    );
  };

  // 渲染参数信息
  const renderParaInfo = () => {
    if (!chargingStationConfig?.ParaInfo) return null;
    
    return (
      <>
        <SectionTitle title={chargingStationConfig.ParaInfo.Label || "关键参数"} />
        <Row gutter={[24, 14]} className="mb-8 para-info-section charging-status-section">
          {chargingStationConfig.ParaInfo.Group?.map((item: ConfigItem, index: number) => (
            <Col key={index} span={12}>
              <div className="flex items-center">
                <span className="label-text">{item.Descript}：</span>
                <span className="value-text">
                  {getFieldValue(item.Name, item.ViewType)}
                  {item.Unit ? ` ${item.Unit}` : ''}
                </span>
              </div>
            </Col>
          ))}
        </Row>
      </>
    );
  };

  // 渲染参数配置 - 使用MultiWriteItemsContainer
  const renderMultiWriteItems = () => {
    if (!chargingStationConfig?.MultiWriteItems) return null;

    return (
      <>
        <SectionTitle title={chargingStationConfig.MultiWriteItems.Label || "参数配置"} />
        <MultiWriteItemsContainer
          configItems={chargingStationConfig.MultiWriteItems.Group || []}
          detailData={detailData}
          deviceCode={deviceCode}
          refreshData={fetchDetailData}
          columnsPerRow={2}
        />
      </>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-[200px]">
      <Spin tip="加载中..." />
    </div>
  );

  return (
    <>
      <Modal
        title={`${deviceName || "充电桩"}`}
        open={open}
        onCancel={() => onOpenChange(false)}
        footer={null}
        width={1128}
        className="charging-station-detail-modal device-modal-common"
        maskClosable={false}
        closeIcon={<CloseOutlined className="modal-close-icon" />}
        centered
      >
        {!initialized && !chargingStationConfig ? renderEmptyState() : (
          <div className="charging-station-detail-content modal-content">
            {/* 指令下发 - ButtonGroups */}
            {renderButtonGroups()}
            
            {/* 指令下发 - ButtonItems */}
            {renderButtonItems()}
            
            {/* 关键参数 - ParaInfo */}
            {renderParaInfo()}
            
            {/* 参数配置 - MultiWriteItems */}
            {renderMultiWriteItems()}
            
            {!chargingStationConfig && (
              <div className="text-center py-8 text-[#E3F8FF]">
                未找到充电桩配置信息，请检查配置
              </div>
            )}
          </div>
        )}
      </Modal>
      
      {/* 虚拟键盘和确认框已集成到MultiWriteItemsContainer和CommandButtonContainer组件中 */}
    </>
  );
}; 