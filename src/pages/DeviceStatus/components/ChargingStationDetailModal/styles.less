/* 导入公共样式 */
@import '../modalCommon.less';

/* 充电桩详情模态框样式 */
.charging-station-detail-modal,
div.ant-modal.charging-station-detail-modal {
  .device-modal-common();
  .charging-station-detail-content {
    .label-text {
      max-width: 400px !important; /* 限制标签最大宽度 */
    }
    .param-config-item {
      .label-text {
        max-width: 200px !important; /* 限制参数标签最大宽度 */
      }
    }
    /* 参数信息部分 */
    .para-info-section {
      margin-bottom: 32px;
    }

    /* 指令下发按钮组 */
    .command-buttons-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .ant-col {
        width: auto !important;
        flex: none !important;
      }
      
      /* 针对ButtonGroups中的按钮特别定制 */
      .command-button {
        min-width: 108px;
        height: 42px;
        padding: 11px 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);
        border: 1px solid #39ABFF;
        
        span {
          color: #6DE875;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: 1em;
        }
        
        &:hover, &:focus {
          border: 1px solid #39ABFF;
          background: linear-gradient(180deg, rgba(0, 135, 255, 0.65) -3.41%, rgba(0, 135, 255, 0.08) 96.68%);
          
          span {
            color: #6DE875 !important;
          }
        }
      }
    }

    /* 指令下发组样式 */
    .command-item-group {
      .flex {
        display: flex;
        align-items: center;
        
        .label-text {
          font-size: 20px;
          color: #9CCEFF;
          flex-shrink: 0;
          width: 80px;
          margin-right: 12px;
          white-space: nowrap;
        }
        
        .command-buttons-container {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
        }
      }
    }

    /* 参数配置部分 */
    .param-config-section {
      margin-bottom: 32px;
      
      .ant-row {
        row-gap: 14px !important;
      }
      
      .ant-form-item {
        margin-bottom: 0;
      }
      
      .param-config-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 0;
      }

      .param-input-wrapper {
        display: flex;
        align-items: center;
        flex: 1;
        
        .unit-text {
          white-space: nowrap;
          margin-left: 8px;
        }
      }
    }
    
    /* 充电站状态特定样式 */
    .charging-status-section {
      padding-bottom: 16px;
      margin-top: 8px;
      
      .ant-col {
        .flex {
          min-height: 28px;
        }
      }
    }

    /* 虚拟键盘样式 */
    /* .custom-keyboard {
      max-width: 100%;
      margin: 0 auto;
      padding: 10px;
      background-color: #143F8C;
      box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.2);

      .hg-button {
        height: 60px;
        font-size: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #0478EC;
        border-radius: 10px;
        box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        margin: 5px;
        
        &.hg-functionBtn {
          background-color: #e6e6e6;
        }
        
        &:active {
          background-color: #39abff;
          color: white;
        }
      }
    } */
  }
}