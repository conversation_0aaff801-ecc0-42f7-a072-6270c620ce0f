import React, { useEffect } from 'react';
import { Form, Row, Col, Input, Button } from 'antd';
import { useParamSettings, ConfigItem } from '../../hooks/useParamSettings';
import { ConfirmModal } from '../ConfirmModal';
import Keyboard from 'react-simple-keyboard';
import 'react-simple-keyboard/build/css/index.css';

export interface MultiWriteItemsContainerProps {
  /**
   * 配置项组
   */
  configItems: ConfigItem[];
  /**
   * 设备数据，用于初始化表单和显示当前值
   */
  detailData: Record<string, any>;
  /**
   * 设备编码
   */
  deviceCode: string | string[];
  /**
   * 刷新数据的函数
   */
  refreshData: (silent?: boolean) => Promise<void>;
  /**
   * 每行显示的列数，默认为3
   */
  columnsPerRow?: number;
  /**
   * 自定义样式类
   */
  className?: string;
    // 是否调用写值接口
  WriteCommand?: boolean;
}

/**
 * 参数配置表单容器组件，提供参数输入、虚拟键盘和确认功能
 */
export const MultiWriteItemsContainer: React.FC<MultiWriteItemsContainerProps> = ({
  configItems,
  detailData,
  deviceCode,
  refreshData,
  columnsPerRow = 3,
  WriteCommand = true,
  className = ''
}) => {
  const {
    form,
    showKeyboard,
    currentEditField,
    keyboardRef,
    confirmVisible,
    confirmLoading,
    confirmAction,
    initFormValues,
    handleInputFocus,
    handleKeyboardInput,
    handleKeyPress,
    closeKeyboard,
    handleConfirm,
    handleCancel,
    createItemRules
  } = useParamSettings({
    deviceCode,
    refreshData,
    WriteCommand
  });

  // 计算Column的宽度
  const colSpan = 24 / columnsPerRow;
  
  // 根据列数计算合适的gutter
  const gutter: [number, number] = columnsPerRow === 3 ? [12, 14] : [columnsPerRow === 2 ? 72 : 24, 14];

  // 初始化表单字段
  useEffect(() => {
    if (configItems && detailData) {
      initFormValues(configItems, detailData);
    }
  }, [detailData, configItems, initFormValues]);
  const negativeAllowed = configItems.find((item:any)=>( item.Min && item.Min<0))
  
  return (
    <>
      <Form form={form} layout="horizontal" className={`w-full param-config-section mb-8 ${className}`}>
        <Row gutter={gutter}>
          {configItems?.map((item, index) => (
            <Col key={index} span={colSpan}>
              <div className="flex items-center param-config-item">
                <span className="label-text">{item.Descript}：</span>
                <div className="param-input-wrapper">
                  <Form.Item
                    name={item.Name}
                    noStyle
                    initialValue={ detailData?.[item.Name] ?? 0 }
                    rules={[...createItemRules(item)]}
                  >
                    <Input
                      className="param-input"
                      type="text" // 移动端不使用原生number类型，统一使用text类型配合自定义键盘
                      onFocus={() => handleInputFocus(item.Name)}
                      value={form.getFieldValue(item.Name)}
                      placeholder={`请输入`}
                    />
                  </Form.Item>
                  {item.Unit && <span className="label-text unit-text">{item.Unit}</span>}
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </Form>

      {/* 参数保存确认弹窗 */}
      <ConfirmModal
        open={confirmVisible}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        confirmLoading={confirmLoading}
        content={confirmAction ? `确认设置参数：${confirmAction.descript}？` : ''}
      />

      {/* 虚拟键盘 */}
      {showKeyboard && (
        <div 
          className="fixed inset-[10%] z-[9999999] flex flex-col justify-start items-center "
          onTouchStart={(e) => e.stopPropagation()}
          onTouchMove={(e) => e.preventDefault()}
        >
          <div className="w-[600px] shadow-[0_4px_12px_rgba(0,0,0,0.25)] rounded-[10px] bg-[#1B53B7]">
            <div className="flex justify-end p-2">
              <Button
                size="large"
                type="text"
                onClick={closeKeyboard}
                style={{ color: '#fff',fontSize:'22px' }}
              >
                取消
              </Button>
            </div>
            <Keyboard
              keyboardRef={(r) => (keyboardRef.current = r)}
              layout={{
                default: ["1 2 3", "4 5 6", "7 8 9", ". 0 {bksp}", negativeAllowed ? '- {enter}' : '{enter}']
              }}
              display={{
                '{bksp}': '删除',
                '{enter}': '确认'
              }}
              onChange={handleKeyboardInput}
              onKeyPress={(button) => handleKeyPress(button, configItems, detailData)}
              theme="hg-theme-default custom-keyboard"
              // useTouchEvents={true}
              useMouseEvents={true}
              disableCaretPositioning={true}
            />
          </div>
        </div>
      )}
    </>
  );
}; 