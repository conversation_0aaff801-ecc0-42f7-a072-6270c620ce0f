import RhDatePicker from '@/components/RhDatePicker';
import { RhKeyboard } from '@/components/RhKeyboard';
import RhSearchInput from '@/components/RhSearchInput';
import { ALARM_TYPES, getAlarmStatusLabel } from '@/constants/alarm';
import { httpPost } from '@/shared/http';
import { formatDate } from '@/utils/formatDate';
import { generateUUID } from '@/utils/uuid';
import { Button, message, Modal, Select, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import clsx from 'clsx';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.module.less';


// 接口类型定义
interface AlarmItem {
  deviceId: string;
  deviceName: string;
  alarmTime: number;
  alarmLevel: string;
  alarmType: number;
  alarmContent: string;
  alarmStatus: string;
  restoreTime: number;
  isConfirmed: boolean;
}

interface QueryParams {
  startTime?: string | null;
  endTime?: string | null;
  keyword: string;
  pageIndex: number;
  pageSize: number;
  alarmType: number;
}


export const Alarm: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<AlarmItem[]>([]);
  const [total, setTotal] = useState(0);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);
  const [dateValue, setDateValue] = useState<any>(null);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    startTime: "",
    endTime: "",
    keyword: '',
    pageIndex: 1,
    pageSize: 10,
    alarmType: 1
  });

  // 获取报警列表
  const fetchAlarmList = async (params?: any) => {
    const body = params || queryParams;
    setLoading(true);
    try {
      const res = await httpPost('/api/history/alarm/list', body);
      if (res.code === 0) {
        // 为每条数据添加 uuid
        const listWithUuid = (res.data?.list || []).map((item: any) => ({
          ...item,
          uuid: generateUUID()
        }));
        setData(listWithUuid);
        setTotal(res.data?.total || 0);
      }
    } finally {
      setLoading(false);
    }
  };

  // 首次加载
  useEffect(() => {
    fetchAlarmList();
  }, []);

  // 表格列定义
  const columns: ColumnsType<AlarmItem> = [
    {
      title: '发生时间',
      dataIndex: 'alarmTime',
      key: 'alarmTime',
      width: 180,
      render: (time) => formatDate(time)
    },
    {
      title: '报警级别',
      dataIndex: 'alarmLevel',
      key: 'alarmLevel',
      width: 120
    },
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      key: 'deviceName',
      width: 150
    },
    /* {
      title: '报警类型',
      dataIndex: 'alarmType',
      key: 'alarmType',
      width: 140,
      // render: getAlarmTypeLabel
    }, */
    {
      title: '报警内容',
      dataIndex: 'alarmContent',
      key: 'alarmContent',
      ellipsis: true,
      width: 200
    },
    {
      title: '状态',
      dataIndex: 'alarmStatus',
      key: 'alarmStatus',
      ellipsis: true,
      width: 100,
      render: getAlarmStatusLabel
    },
    {
      title: '恢复时间',
      dataIndex: 'restoreTime',
      key: 'restoreTime',
      width: 180,
      render: (time) => formatDate(time)
    },
  ];

  // 处理查询
  const handleSearch = () => {
    setQueryParams(prev => ({ ...prev, pageIndex: 1 }));
    fetchAlarmList();
  };

  // 处理重置
  const handleReset = () => {
    const p = {
      startTime: "",
      endTime: "",
      keyword: '',
      pageIndex: 1,
      pageSize: 10,
      alarmType: 1
    }
    setDateValue(null); // 重置日期选择器的值
    setQueryParams(p);
    fetchAlarmList(p);
    keyboardRef.current?.setInput?.("");
  };
  // 清除所有PCS告警
  const cleanPCS = useCallback(() => {
    Modal.confirm({
      title: '操作确认',
      content: '是否清除所有PCS告警？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await httpPost('/EMS/System/ClearPCSFault')
        if (res.code === 0) {
          message.success(res.message || '清除所有PCS告警成功');
        } else {
          message.error(res.message || '清除失败');
        }
      }
    });

  }, []);

  // 处理日期变化
  const handleDateChange = (dates: any) => {
    setQueryParams(prev => ({
      ...prev,
      startTime: formatDate(dates?.[0]?.valueOf()) || '',
      endTime: formatDate(dates?.[1]?.valueOf()) || ''
    }));
  };

  // 处理关键词变化
  const handleKeywordChange = (value: string) => {
    setQueryParams(prev => ({ ...prev, keyword: value }));
    if (!value) {
      keyboardRef.current?.setInput?.("");
    }
  };

  // 处理类型变化
  const handleTypeChange = (value: number) => {
    setQueryParams(prev => ({ ...prev, alarmType: value }));
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setQueryParams(prev => ({
      ...prev,
      pageIndex: pagination.current,
      pageSize: pagination.pageSize
    }));
    fetchAlarmList({
      ...queryParams,
      pageIndex: pagination.current,
      pageSize: pagination.pageSize
    });
  };


  const handleInputFocus = () => {
    setShowKeyboard(true);
  };

  const handleKeyboardInput = (input: string) => {
    setQueryParams(prev => ({ ...prev, keyword: input }));
  };

  // 修改键盘相关代码部分
  return (
    <div className={styles.container}>
      <div className={clsx('cst', styles.filterBar)}>
        <div className="flex items-center" style={{ width: "240px" }}>
          <div style={{ width: "120px", color: '#3C7DB6' }}>报警分类</div>
          <Select
            placeholder="报警分类"
            style={{ width: 200 }}
            value={queryParams.alarmType || undefined}
            onChange={handleTypeChange}
            options={ALARM_TYPES}
            title=""
          />
        </div>
        <RhDatePicker
          allowClear={true}
          value={dateValue}  // 使用受控值
          onChange={(dates: any) => {
            setDateValue(dates);  // 更新日期选择器的值
            handleDateChange(dates);
          }} />
        <RhSearchInput
          value={queryParams.keyword}
          onChange={handleKeywordChange}
          placeholder='请输入报警内容'
          onFocus={handleInputFocus}
          style={{ width: "300px" }}
        />

        <RhKeyboard
          init={(r: any) => (keyboardRef.current = r)}
          show={showKeyboard}
          onClose={() => setShowKeyboard(false)}
          onChange={handleKeyboardInput}
          layoutType={'chinese'}
        />

        <Button type="primary" onClick={handleSearch}>查询</Button>
        <Button type="primary" ghost onClick={handleReset}>重置</Button>
        <Button type="primary" onClick={cleanPCS} style={{ width: '124px' }}>清除所有PCS告警</Button>
      </div>
      <Table
        rowKey="uuid"
        loading={loading}
        columns={columns}
        dataSource={data}
        className='cst'
        size='middle'
        pagination={{
          total,
          current: queryParams.pageIndex,
          pageSize: queryParams.pageSize,
          showSizeChanger: false,  // 隐藏 
          showQuickJumper: false
        }}
        onChange={handleTableChange}
        scroll={{/*  y: 'calc(100vh - 250px)' */ }}
      />
    </div>
  );
};