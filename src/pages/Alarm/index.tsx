import RhDatePicker from '@/components/RhDatePicker';
import { RhKeyboard } from '@/components/RhKeyboard';
import RhSearchInput from '@/components/RhSearchInput';
import { ALARM_TYPES, getAlarmStatusLabel } from '@/constants/alarm';
import { httpGet, httpPost } from '@/shared/http';
import { formatDate } from '@/utils/formatDate';
import { generateUUID } from '@/utils/uuid';
import { message, Modal, Radio, Select, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import clsx from 'clsx';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import RhPagination from '@/components/RhPagination';
import { RhButton } from '@/components/ui/pagbtn';
import { CloseOutlined, DownOutlined } from '@ant-design/icons';
import RhSelectInput from '@/components/RhSelectInput';
import TableTitle from '@/components/TableTitle';
import { ConfirmModal } from '../DeviceStatus/components/ConfirmModal';
// import CustomTimeRangePicker, {
//   calculateEndTime,
//   TimeRangeValue,
// } from '@/components/CustomTimeRangePicker';

// 接口类型定义
interface AlarmItem {
  deviceId: string;
  deviceName: string;
  alarmTime: number;
  alarmLevel: string;
  alarmType: number;
  alarmContent: string;
  alarmStatus: string;
  restoreTime: number;
  isConfirmed: boolean;
  deviceType: string;
}

interface QueryParams {
  startTime?: string | null;
  endTime?: string | null;
  keyword: string;
  pageIndex: number;
  pageSize: number;
  alarmType: number;
  alarmStatus: string | number;
  deviceName: string;
  alarmLevel: string | number;
  deviceType: string;
}

const alarmType = [
  { value: 1, label: '实时告警' },
  { value: 2, label: '历史告警' },
];
const DEFAULTPAGESIZE = 8;

const alarmLevelOptions = [
  { key: '1', label: '紧急' },
  { key: '2', label: '重要' },
  { key: '3', label: '一般' },
  { key: '0', label: '提示' },
];
const getAlarmLevelLabel = (value: any) => {
  return (
    alarmLevelOptions.find((item) => `${item.key}` === `${value}`)?.label || `${value ?? '未知'}`
  );
};

export const Alarm: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<AlarmItem[]>([]);
  const [total, setTotal] = useState(0);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);
  const [dateValue, setDateValue] = useState<any>(null);
  const [type, setType] = useState(alarmType[0].value);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    startTime: '',
    endTime: '',
    keyword: '',
    pageIndex: 1,
    pageSize: DEFAULTPAGESIZE,
    alarmType: 1,
    deviceName: '',
    alarmStatus: 'all',
    alarmLevel: 'all',
    deviceType: 'all',
  });
  const [options, setOptions] = useState([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<AlarmItem>();
  const [DeviceTypeOptions, setDeviceTypeOptions] = useState<any[]>([]);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  // const [timeValue, setTimeValue] = useState<TimeRangeValue | null>({
  //   startTime: undefined,
  //   unit: 'hour',
  //   number: 0,
  // });
  // 获取报警列表
  const fetchAlarmList = async (params?: any) => {
    //alarmType  1、2区分实时数据和历史数据
    const body = params || queryParams;
    if (body?.deviceType === 'all') {
      body.deviceType = '';
    }
    if (body?.alarmStatus === 'all') {
      body.alarmStatus = '';
    }
    if (body?.alarmLevel === 'all') {
      body.alarmLevel = '';
    }

    if (body?.alarmStatus && !Number.isNaN(Number(body.alarmStatus))) {
      body.alarmStatus = Number(body.alarmStatus);
    }
    setLoading(true);
    try {
      const res = await httpPost('/api/history/alarm/list', body);
      if (res.code === 0) {
        // 为每条数据添加 uuid
        const listWithUuid = (res.data?.list || []).map((item: any) => ({
          ...item,
          uuid: generateUUID(),
        }));
        setData(listWithUuid);
        setTotal(res.data?.total || 0);
      }
    } finally {
      setLoading(false);
    }
  };

  const getDeviceItems = async () => {
    const res = await httpGet('/api/device/all');
    if (res.code === 0) {
      const deviceType: any = [];
      const items = res.data.map((item: any) => {
        deviceType.push(item.type);
        return {
          ...item,
          key: item.id,
          value: item.id,
          label: item.id,
        };
      });
      // deviceType 去重
      const deviceTypeUnique: any[] = [...new Set(deviceType)];
      setDeviceTypeOptions(deviceTypeUnique);
      setOptions(items || []);
    }
  };

  // 首次加载
  useEffect(() => {
    fetchAlarmList();
    // 获取设备
    getDeviceItems();
  }, []);
  // 表头筛选条件更新
  const handleFilterChange = ({ column, value }: any) => {
    const params = { ...queryParams, [column]: value, pageIndex: 1 };
    setQueryParams((prev) => {
      const newParams = { ...prev, [column]: value, pageIndex: 1 };
      return newParams;
    });
    fetchAlarmList(params);
  };
  // 表格列定义
  const columns: ColumnsType<AlarmItem> = [
    {
      title: '发生时间',
      dataIndex: 'alarmTime',
      key: 'alarmTime',
      width: 180,
      render: (time) => formatDate(time),
    },
    {
      title: (
        <TableTitle
          title="级别"
          column="alarmLevel"
          // 1.紧急，2重要，3一般
          width={600}
          options={alarmLevelOptions}
          value={queryParams.alarmLevel || 'all'}
          onChange={handleFilterChange}
        />
      ),
      dataIndex: 'alarmLevel',
      key: 'alarmLevel',
      width: 120,
      render: (level) => {
        return getAlarmLevelLabel(level);
      },
    },
    {
      title: (
        <TableTitle
          title="分组"
          column="deviceType"
          options={DeviceTypeOptions.map((item) => ({
            key: item,
            label: item,
          }))}
          width={600}
          value={queryParams.deviceType || 'all'}
          onChange={handleFilterChange}
        />
      ),
      dataIndex: 'deviceType',
      key: 'deviceType',
      width: 120,
    },
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      key: 'deviceName',
      width: 150,
    },
    /* {
      title: '报警类型',
      dataIndex: 'alarmType',
      key: 'alarmType',
      width: 140,
      // render: getAlarmTypeLabel
    }, */
    {
      title: '报警内容',
      dataIndex: 'alarmContent',
      key: 'alarmContent',
      ellipsis: true,
      width: 200,
    },
    {
      title: (
        <TableTitle
          title="状态"
          column="alarmStatus"
          options={[
            { key: '0', label: '激活' },
            { key: '1', label: '恢复' },
          ]}
          value={queryParams.alarmStatus || 'all'}
          onChange={handleFilterChange}
          filter={type !== alarmType[0].value}
        />
      ),
      dataIndex: 'alarmStatus',
      key: 'alarmStatus',
      ellipsis: true,
      width: 100,
      render: getAlarmStatusLabel,
    },
    {
      title: '恢复时间',
      dataIndex: 'restoreTime',
      key: 'restoreTime',
      width: 180,
      render: (time) => formatDate(time),
    },
    {
      title: '操作',
      dataIndex: '',
      key: 'x',
      width: 80,
      render: (_, item) => (
        <div
          className="h-full w-full"
          onClick={() => {
            setCurrentItem(item);
            setDetailVisible(true);
          }}
        >
          <a className="text-[#6DE875]">详情</a>
        </div>
      ),
    },
  ];

  // 处理查询
  const handleSearch = () => {
    setQueryParams((prev) => ({ ...prev, pageIndex: 1 }));
    fetchAlarmList();
  };

  // 处理重置
  const handleReset = (type: number) => {
    const p = {
      startTime: '',
      endTime: '',
      keyword: '',
      pageIndex: 1,
      pageSize: DEFAULTPAGESIZE,
      alarmType: type,
      alarmStatus: 'all',
      deviceName: '',
      alarmLevel: 'all',
      deviceType: 'all',
    };
    setDateValue(null); // 重置日期选择器的值
    setQueryParams(p);
    fetchAlarmList(p);
    keyboardRef.current?.setInput?.('');
  };
  // 清除所有PCS告警
  const cleanPCS = useCallback(() => {
    setConfirmVisible(true);
  }, []);

  // 处理日期变化
  const handleDateChange = (dates: any) => {
    setQueryParams((prev) => ({
      ...prev,
      startTime: dates?.[0]?.valueOf() ? formatDate(dates?.[0]?.valueOf()) : '',
      endTime: dates?.[1]?.valueOf() ? formatDate(dates?.[1]?.valueOf()) : '',
    }));
  };

  // 处理关键词变化
  const handleKeywordChange = (value: string) => {
    setQueryParams((prev) => ({ ...prev, keyword: value }));
    if (!value) {
      keyboardRef.current?.setInput?.('');
    }
  };

  // 处理类型变化
  const handleTypeChange = (value: number) => {
    setQueryParams((prev) => ({ ...prev, alarmType: value }));
  };
  // 告警类型变化 实时 历史
  const handleType = (value: number) => {
    setType(value);
    setQueryParams((prev) => ({ ...prev, alarmType: value }));
    // 切换类型重置条件查询
    handleReset(value);
  };
  // 处理分页变化
  const handleTableChange = (current: any) => {
    setQueryParams((prev) => ({
      ...prev,
      pageIndex: current,
    }));
    fetchAlarmList({
      ...queryParams,
      pageIndex: current,
    });
  };

  const handleInputFocus = () => {
    setShowKeyboard(true);
  };

  const handleKeyboardInput = (input: string) => {
    setQueryParams((prev) => ({ ...prev, keyword: input }));
  };

  // 自定义时间范围选择变化
  /*   const handleTimeChange = (value: TimeRangeValue | null) => {
    setTimeValue(value);
    let startTime = value?.startTime;
    let endTime: any = undefined;
    if (value && startTime) {
      endTime = calculateEndTime({
        startTime: value.startTime,
        calcNumber: value.number || 0,
        unit: value.unit,
      });
    }
    setQueryParams((prev) => ({
      ...prev,
      startTime: startTime ? formatDate(startTime?.valueOf()) : '',
      endTime: endTime ? formatDate(endTime?.valueOf()) : '',
    }));
  }; */
  const handleConfirm = async (_params?: any) => {
    setConfirmLoading(true);
    try {
      const res = await httpPost('/EMS/System/ClearPCSFault');
      if (res.code === 0) {
        message.success(res.message || '清除所有PCS告警成功');
      } else {
        message.error(res.message || '清除失败');
      }
    } catch (error) {
    } finally {
      setConfirmVisible(false);
      setConfirmLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className="relative flex">
        <Radio.Group
          onChange={(e) => handleType(e.target.value)}
          defaultValue={alarmType?.[0]?.value || 'read'}
          value={type}
          className={`custom-radio-group flex w-full justify-center`}
        >
          {(alarmType || [])?.map((item) => (
            <Radio.Button key={item?.value} value={item?.value} className={'custom-radio'}>
              {item?.label}
            </Radio.Button>
          ))}
        </Radio.Group>
      </div>
      <div className={clsx('cst mt-7 flex justify-between', styles.filterBar)}>
        {/* <div className="flex items-center" style={{ width: "240px" }}>
          <div style={{ width: "120px", color: '#3C7DB6' }}>报警分类</div>
          <Select
            placeholder="报警分类"
            style={{ width: 200 }}
            value={queryParams.alarmType || undefined}
            onChange={handleTypeChange}
            options={ALARM_TYPES}
            title=""
          />
        </div>
        <RhDatePicker
          allowClear={true}
          value={dateValue}  // 使用受控值
          onChange={(dates: any) => {
            setDateValue(dates);  // 更新日期选择器的值
            handleDateChange(dates);
          }} /> */}
        {/* <RhSearchInput
          value={queryParams.keyword}
          onChange={handleKeywordChange}
          placeholder="请输入报警内容"
          onFocus={handleInputFocus}
          style={{ width: '300px' }}
        /> */}
        <div className="relative flex items-center gap-4 text-[18px]">
          {type === alarmType[1].value && (
            // <CustomTimeRangePicker
            //   value={timeValue}
            //   onChange={handleTimeChange}
            //   defaultTimeUnit="hour"
            //   defaultNumber={2}
            //   timeUnitOptions={['hour', 'minute']}
            //   showTime={true}
            //   format = 'YYYY-MM-DD HH:mm'
            //   // placeholder="请选择开始时间"
            // />
            <>
              <span>开始时间</span>
              <RhDatePicker
                allowClear={true}
                value={dateValue} // 使用受控值
                onChange={(dates: any) => {
                  setDateValue(dates); // 更新日期选择器的值
                  handleDateChange(dates);
                }}
              />
            </>
          )}
          <div className="flex items-center gap-4">
            <span>设备名称</span>
            <RhSelectInput
              allowClear={true}
              value={queryParams.deviceName}
              onChange={(val) => {
                setQueryParams((prev) => ({ ...prev, deviceName: val }));
              }}
              options={options}
              className={'w-[250px]'}
            />
          </div>
          <RhButton
            type="primary"
            onClick={handleSearch}
            text="查询"
            style={{ width: '105px' }}
          />
        </div>

        {/* <RhKeyboard
          init={(r: any) => (keyboardRef.current = r)}
          show={showKeyboard}
          onClose={() => setShowKeyboard(false)}
          onChange={handleKeyboardInput}
          layoutType={'chinese'}
        /> */}

        {/* <RhButton type="primary" onClick={handleSearch} text="查询" /> */}
        {/* <Button type="primary" ghost onClick={handleReset}>重置</Button> */}
        {type === alarmType[0].value && (
          <RhButton
            type="primary"
            onClick={cleanPCS}
            style={{ width: 'auto' }}
            text={'清除所有PCS告警'}
          />
        )}
      </div>
      <Table
        rowKey="uuid"
        loading={loading}
        columns={columns}
        dataSource={data}
        className="cst text-[18px]"
        size="middle"
        // pagination={{
        //   total,
        //   current: queryParams.pageIndex,
        //   pageSize: queryParams.pageSize,
        //   showSizeChanger: false,  // 隐藏
        //   showQuickJumper: false
        // }}
        // onChange={handleTableChange}
        pagination={false}
        scroll={
          {
            /*  y: 'calc(100vh - 250px)' */
          }
        }
      />
      {total > DEFAULTPAGESIZE && (
        <RhPagination
          total={total}
          current={queryParams.pageIndex}
          pageSize={DEFAULTPAGESIZE}
          // onChange={handleTableChange}
          // totalPages={Math.ceil(total / DEFAULTPAGESIZE)}
          onPageChange={handleTableChange}
        />
      )}
      <Modal
        title="详情"
        open={detailVisible && !!currentItem}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        className="cst"
        centered={true}
        closeIcon={<CloseOutlined className="modal-close-icon" />}
      >
        <div className="text-xl leading-loose">
          <p className="flex items-center">
            <span className="w-[100px] flex-shrink-0 text-[#9CCEFF]">发生时间：</span>
            <span className="flex-1">
              {currentItem?.alarmTime ? formatDate(currentItem?.alarmTime) : '-'}{' '}
            </span>
          </p>
          <p className="flex items-center">
            <span className="w-[100px] flex-shrink-0 text-[#9CCEFF]">级别：</span>
            <span className="flex-1">{getAlarmLevelLabel(currentItem?.alarmLevel) || '-'}</span>
          </p>
          <p className="flex items-center">
            <span className="w-[100px] flex-shrink-0 text-[#9CCEFF]">分组：</span>
            <span className="flex-1">{currentItem?.deviceType || '-'}</span>
          </p>
          <p className="flex items-center">
            <span className="w-[100px] flex-shrink-0 text-[#9CCEFF]">设备名称：</span>
            <span className="flex-1">{currentItem?.deviceName || '-'} </span>
          </p>
          <p className="flex items-center">
            <span className="w-[100px] flex-shrink-0 text-[#9CCEFF]">报警内容：</span>
            <span className="flex-1">{currentItem?.alarmContent || '-'}</span>
          </p>
          <p className="flex items-center">
            <span className="w-[100px] flex-shrink-0 text-[#9CCEFF]">状态：</span>
            <span className="flex-1">
              {getAlarmStatusLabel(Number(currentItem?.alarmStatus)) || '-'}
            </span>
          </p>
          <p className="flex items-center">
            <span className="w-[100px] flex-shrink-0 text-[#9CCEFF]">恢复时间：</span>
            <span className="flex-1">
              {currentItem?.restoreTime ? formatDate(currentItem?.restoreTime) : '-'}{' '}
            </span>
          </p>
        </div>
      </Modal>
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => {
          setConfirmVisible(false);
        }}
        onConfirm={handleConfirm}
        confirmLoading={confirmLoading}
        content={'确认是否清除所有PCS告警？'}
      />
    </div>
  );
};
