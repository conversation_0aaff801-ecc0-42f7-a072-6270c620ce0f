.homePage {
    padding: 20px;
    height: 100%;
    background: #031c5a;
    color: #fff;
}

.topStats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.statItem {
    background: rgba(10, 38, 100, 0.5);
    padding: 12px 24px;
    border-radius: 4px;
    border: 1px solid #1d4884;

    .value {
        font-size: 24px;
        color: #fff;
        margin-bottom: 4px;

        .unit {
            font-size: 14px;
            margin-left: 4px;
            color: #76AAD9;
        }
    }

    .label {
        font-size: 14px;
        color: #76AAD9;
    }
}

.socBar {
    margin: 20px 0;
    
    .label {
        color: #3BE354;
        margin-bottom: 8px;
    }

    .progressBar {
        height: 20px;
        background: rgba(10, 38, 100, 0.3);
        border-radius: 10px;
        overflow: hidden;

        .progress {
            height: 100%;
            background: linear-gradient(90deg, #3BE354 0%, #76AAD9 100%);
            transition: width 0.3s ease;
        }
    }
}

.deviceStats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.deviceItem {
    background: rgba(10, 38, 100, 0.5);
    padding: 12px 24px;
    border-radius: 4px;
    border: 1px solid #1d4884;
    text-align: center;

    .value {
        font-size: 20px;
        color: #fff;
        margin-bottom: 4px;
    }

    .label {
        font-size: 14px;
        color: #76AAD9;
    }
}

.mainContent {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.leftPanel,
.rightPanel {
    flex: 1;
    background: rgba(10, 38, 100, 0.5);
    border: 1px solid #1d4884;
    padding: 20px;

    .title {
        color: #3BE354;
        font-size: 16px;
        margin-bottom: 20px;
    }
}