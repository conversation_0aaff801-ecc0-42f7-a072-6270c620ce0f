.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(180deg, #00328A 0%, #001958 66%);
}

.loginCard {
  width: 400px;
  padding: 40px;
  background-color: #2353A7;
  border-radius: 4px;
  box-shadow: 0px 4px 12px 0px rgba(5, 12, 50, 0.25);
  margin-bottom: 100px;
}

.logoContainer {
  display: flex;
  // flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
}

.logo {
  width: 30px;
  height: 30px;
  margin-right: 6px;
}

.title {
  color: #fff;
  font-size: 18px;
  text-align: center;
  font-weight: 600;
  margin: 0;
}

.loginForm {
  :global {
    .ant-form-item-label>label {
      color: #A5CFFF;
    }

    // 隐藏表单项的星号
    .ant-form-item-required::before {
      display: none !important;
    }

    .ant-input,
    .ant-input-password {
      background-color: #2353A7;
      border-radius: 4px;
      border: 1px solid #9EBDFA;
      color: #fff;
      // height: 40px;
      border-radius: 4px;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .ant-input-password .ant-input {
      background-color: transparent;
      border: none;
    }

    .ant-input-password-icon {
      color: #E3F8FF !important;
    }

    .anticon {
      color: #76AAD9;
    }
  }
}

.loginButton {
  width: 100%;
  height: 40px;
  background: #0478EC;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 16px;
}