import React, { useState, useRef } from 'react';
import { Form, Input, Button, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import md5 from 'md5';
import { setUserInfo } from '@/shared/auth';
import { httpPost } from '@/shared/http';
import styles from './index.module.less';
import logo from '@/assets/sany_logo.png';
import { CloseOutlined } from '@ant-design/icons';
import Keyboard from 'react-simple-keyboard';
import 'react-simple-keyboard/build/css/index.css';
import { projectType } from '@/constants';

interface LoginForm {
  username: string;
  password: string;
}

interface LoginResponse {
  code: number;
  message: string;
  ext: string;
  data: {
    token: string;
    userName: string;
    freshToken: string;
  };
}

export const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  // 添加状态
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [currentInput, setCurrentInput] = useState('username'); // 当前激活的输入框
  const keyboardRef = useRef<any>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const handleSubmit = async (values: LoginForm) => {
    setLoading(true);
    try {
      const result = await httpPost('/api/auth/login', {
        userName: values.username,
        userPwd: md5(values.password),
      });

      if (result.code === 0) {
        // 使用 sessionStore 存储登录信息
        setUserInfo(result.data);
        message.success('登录成功');
        navigate('/');
      } else {
        message.error(result.message || '登录失败');
      }
    } catch (error) {
      message.error('登录失败，请检查网络连接');
      console.error('登录错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加键盘处理函数
  const handleInputFocus = (inputName: string) => {
    if (currentInput !== inputName) {
      form.setFieldValue?.(inputName, '');
      keyboardRef.current?.setInput?.('');
    }
    setCurrentInput(inputName);
    setShowKeyboard(true);
  };

  const handleKeyboardInput: any = (input: string) => {
    form.setFieldValue(currentInput, input);
  };

  return (
    <div className={styles.loginContainer}>
      <div
        style={{ position: 'absolute', top: 0, left: 0, width: 20, height: 20 }}
        onClick={() => {
          message.info(projectType);
          message.info(navigator.userAgent, 5);
        }}
      ></div>
      <div className={styles.loginCard}>
        <div className={styles.logoContainer}>
          <img src={logo} alt="Logo" className={styles.logo} />
          <h1 className={styles.title}>SANY储能LEMS控制系统</h1>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          className={styles.loginForm}
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" onFocus={() => handleInputFocus('username')} />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password placeholder="请输入密码" onFocus={() => handleInputFocus('password')} />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className={styles.loginButton}
            >
              登 录
            </Button>
          </Form.Item>
        </Form>

        {showKeyboard && (
          <div
            className="fixed bottom-0 left-0 z-50 w-full  bg-[#1B53B7]"
            onTouchStart={(e) => e.stopPropagation()}
            onTouchMove={(e) => e.preventDefault()}
          >
            <div className="flex justify-end p-2">
              <Button
                size="large"
                type="text"
                // icon={<CloseOutlined />}
                onClick={() => setShowKeyboard(false)}
                style={{ color: '#fff',fontSize:'22px' }}
              >
                取消
              </Button>
            </div>
            <Keyboard
              keyboardRef={(r) => (keyboardRef.current = r)}
              input={form.getFieldValue(currentInput) || ''}
              onChange={handleKeyboardInput}
              theme="hg-theme-default custom-keyboard"
              layout={{
                default: [
                  '1 2 3 4 5 6 7 8 9 0',
                  'q w e r t y u i o p',
                  'a s d f g h j k l',
                  'z x c v b n m',
                  '{bksp} {space} {enter}',
                ],
              }}
              display={{
                '{space}': '空格',
                '{bksp}': '删除',
                '{enter}': '确认',
              }}
              // useTouchEvents={true}              // 启用触摸事件支持
              useMouseEvents={true} // 启用鼠标事件
              disableCaretPositioning={true} // 禁用光标定位，避免触摸冲突
              onKeyPress={(button: string) => {
                if (button === '{enter}') {
                  setShowKeyboard(false);
                }
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};
