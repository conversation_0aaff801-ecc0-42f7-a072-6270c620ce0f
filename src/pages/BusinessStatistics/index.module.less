.container {
    padding: 20px;
    background-color: transparent;
    height: 100%;

    :global {

        // 日期选择器样式
        .ant-picker {
            background-color: transparent;
            border-color: #1d4884;

            input {
                color: #fff;

                &::placeholder {
                    color: #A7BEEA;
                }
            }

            .ant-picker-suffix {
                color: #A7BEEA;
            }

            .ant-picker-clear {
                color: #A7BEEA;
            }

            &:hover {
                .ant-picker-suffix {
                    color: #A7BEEA !important;
                }
            }
        }

        // 日期选择器弹出层按钮样式
        .ant-picker-dropdown {
            .ant-picker-ok {
                .ant-btn {
                    color: #fff;
                }
            }
        }

        .ant-btn-primary {
            color: #fff !important;
            border-radius: 0;
            height: 32px;
            width: 64px;
        }

        .ant-table {
            background-color: transparent;

            .ant-table-cell::before {
                display: none !important;
            }
        }

        .ant-table-thead>tr>th {
            background-color: #0a2664;
            color: #fff;
            border-bottom: 1px solid #1d4884;

            .ant-table-column-sorter-up,
            .ant-table-column-sorter-down,
            .anticon-filter {
                color: #a8bfea;
            }
        }

        .ant-table-tbody>tr {
            &:nth-child(odd)>td {
                background-color: #0a2664;
            }

            >td {
                background-color: transparent;
                border-bottom: 1px solid #1d4884;
                color: #fff;
            }
        }

        .ant-table-tbody>tr:hover>td {
            background-color: #0a2e5c !important;
        }

        // Select 样式
        .ant-select {
            .ant-select-selection-placeholder {
                color: #A7BEEA !important;
            }
        }

        // 分页组件样式
        .ant-pagination {

            .ant-pagination-prev,
            .ant-pagination-next,
            .ant-pagination-item {
                background: transparent;
                border-color: #1d4884;

                a {
                    color: #fff;
                }

                &:hover {
                    border-color: #1677ff;

                    a {
                        color: #1677ff;
                    }
                }
            }

            .ant-pagination-item-active {
                background: transparent;
                border-color: #1677ff;

                a {
                    color: #1677ff;
                }
            }

            .ant-pagination-options {
                .ant-select-selector {
                    background: transparent !important;
                    border-color: #1d4884 !important;
                }

                .ant-select-selection-item {
                    color: #fff;
                }
            }
        }
    }
}

.filterBar {
    margin-bottom: 20px;
    display: flex;
    gap: 24px;
    align-items: center;
}