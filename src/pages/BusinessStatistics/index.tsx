import { getPowerTypeLabel } from '@/constants/alarm';
import { httpPost } from '@/shared/http';
import { formatDate } from '@/utils/formatDate';
import { generateUUID } from '@/utils/uuid';
import { Button, DatePicker, Select, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import clsx from 'clsx';
import dayjs from 'dayjs';
import React, { useEffect, useState, useRef } from 'react';
import RhSearchInput from '@/components/RhSearchInput';
import { RhKeyboard } from '@/components/RhKeyboard';
import styles from './index.module.less';
import { RhButton } from '@/components/ui/pagbtn';

const { RangePicker } = DatePicker;

interface BusinessItem {
  startTime: number;
  endTime: number;
  takeMinute: number;
  electric: number;
  powerType: string;
  chargeGunNum: string;
  deviceId: string;
  pcs: string;
  bms: string;
}

interface QueryParams {
  startTime?: string | null;
  endTime?: string | null;
  keyword: string;
  pageIndex: number;
  pageSize: number;
  chargeType: number;
}

export const BusinessStatistics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<BusinessItem[]>([]);
  const [total, setTotal] = useState(0);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    startTime: '',
    endTime: '',
    keyword: '',
    pageIndex: 1,
    pageSize: 10,
    chargeType: 1,
  });

  const fetchBusinessList = async (params?: any) => {
    const body = params || queryParams;
    setLoading(true);
    try {
      let url = '/api/history/businessstat/charge/list';
      if (queryParams.chargeType == 2) {
        url = '/api/history/businessstat/discharge/list';
      }
      const res: any = await httpPost(url, body);
      if (res.code === 0) {
        // 为每条数据添加 uuid
        const listWithUuid = (res.data?.list || []).map((item: any) => ({
          ...item,
          uuid: generateUUID(),
        }));
        setData(listWithUuid);
        setTotal(res.data?.total || 0);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBusinessList();
  }, []);

  const columns: ColumnsType<BusinessItem> = [
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 180,
      render: (time) => formatDate(time),
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 180,
      render: (time) => formatDate(time),
    },
    {
      title: '时长(min)',
      dataIndex: 'takeTime',
      key: 'takeTime',
      width: 120,
    },
    {
      title: '电量(KWH)',
      dataIndex: 'electric',
      key: 'electric',
      width: 120,
    },
    {
      title: queryParams.chargeType == 1 ? '补能类型' : '供电类型',
      dataIndex: 'powerType',
      key: 'powerType',
      width: 120,
      render: getPowerTypeLabel,
    },
    {
      title: queryParams.chargeType === 2 ? '供电口' : '补能口',
      dataIndex: 'chargeGunNum',
      key: 'chargeGunNum',
      width: 100,
    },
  ];

  const handleReset = () => {
    const p = {
      startTime: null,
      endTime: null,
      keyword: '',
      pageIndex: 1,
      pageSize: 10,
      chargeType: 1,
    };
    setQueryParams(p);
    fetchBusinessList(p);
    keyboardRef.current?.setInput?.('');
  };

  // 处理查询
  const handleSearch = () => {
    const params = {
      ...queryParams,
      pageIndex: 1,
    };
    setQueryParams(params);
    fetchBusinessList(params);
  };

  const handleDateChange = (dates: any) => {
    setQueryParams((prev) => ({
      ...prev,
      startTime: formatDate(dates?.[0]?.valueOf()) || '',
      endTime: formatDate(dates?.[1]?.valueOf()) || '',
    }));
  };

  const handleTypeChange = (value: number) => {
    setQueryParams((prev) => ({ ...prev, chargeType: value }));
  };

  const handleTableChange = (pagination: any) => {
    const params = {
      ...queryParams,
      pageIndex: pagination.current,
      pageSize: pagination.pageSize,
    };
    setQueryParams(params);
    fetchBusinessList(params);
  };

  // 添加键盘相关处理函数
  const handleInputFocus = () => {
    setShowKeyboard(true);
  };

  const handleKeywordChange = (value: string) => {
    setQueryParams((prev) => ({ ...prev, keyword: value }));
    if (!value) {
      keyboardRef.current?.setInput?.('');
    }
  };

  const handleKeyboardInput = (input: string) => {
    setQueryParams((prev) => ({ ...prev, keyword: input }));
  };

  return (
    <div className={styles.container}>
      <div className={clsx('cst', styles.filterBar)}>
        <div className="flex items-center" style={{ width: '240px' }}>
          <div style={{ width: '120px', color: '#3C7DB6' }}>充放电类型</div>
          <Select
            placeholder="充放电类型"
            style={{ width: 200, height: '48px' }}
            value={queryParams.chargeType || undefined}
            onChange={handleTypeChange}
            title=""
            options={[
              { value: 1, label: '补能', title: '' },
              { value: 2, label: '供电', title: '' },
            ]}
          />
        </div>
        <span style={{ color: '#3C7DB6' }}>日期时间点</span>
        <RangePicker
          showTime
          allowClear
          value={[
            queryParams.startTime ? dayjs(queryParams.startTime) : null,
            queryParams.endTime ? dayjs(queryParams.endTime) : null,
          ]}
          format="YYYY-MM-DD HH:mm:ss"
          onChange={handleDateChange}
          placeholder={['开始时间点', '结束时间点']}
          separator={<span style={{ color: '#A7BEEA' }}>至</span>}
          style={{ width: '330px', height: '48px' }}
        />
        <RhSearchInput
          value={queryParams.keyword}
          onChange={handleKeywordChange}
          placeholder="请输入关键字"
          onFocus={handleInputFocus}
          style={{ width: '320px',height: '48px' }}
        />
        <RhKeyboard
          init={(r: any) => (keyboardRef.current = r)}
          show={showKeyboard}
          onClose={() => setShowKeyboard(false)}
          onChange={handleKeyboardInput}
          layoutType={'chinese'}
        />
        <RhButton type="primary" onClick={handleSearch} style={{ width: 70 }}>
          查询
        </RhButton>
        <RhButton type="primary" ghost onClick={handleReset} style={{ width: 70 }}>
          重置
        </RhButton>
      </div>
      <Table
        rowKey="uuid"
        loading={loading}
        columns={columns}
        dataSource={data}
        className="cst"
        size="middle"
        pagination={{
          total,
          current: queryParams.pageIndex,
          pageSize: queryParams.pageSize,
          showSizeChanger: false,
          showQuickJumper: false,
        }}
        onChange={handleTableChange}
        scroll={
          {
            /* y: 'calc(100vh - 250px)' */
          }
        }
      />
    </div>
  );
};
