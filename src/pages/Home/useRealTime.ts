import { ConnectionFactory, WebSocketClient } from '@/shared/socket'
import parseJsonString from '@/utils/parseJson'
import { useCallback, useEffect, useRef, useState } from 'react'

type OptionType = {
  open: (msg?: any) => void
  close: (e: any) => void
  receive: (e: any) => void
}

let wsInstance: any
let wsConnectFlag = false

const { hostname, port } = window.location

function createSocket({ open, close, receive }: OptionType) {
  let wsUrl = ''
  const httpsEnabled = window.location.protocol === 'https:'
  const protocol = httpsEnabled ? 'wss://' : 'ws://'
  const protocolPort = port ? `:${port}` : ''
  const endpoint = `${protocol}${hostname}${protocolPort}`
  wsUrl = `${endpoint}/ws/ems/systemdata`

  const factory = new ConnectionFactory(wsUrl, [])
  const wt = new WebSocketClient(factory, '')
  const closer = wt.open({
    onCloseCallback: close,
    onOpenCallback: open,
    heartbeatFn: () => {
      // connection.send(JSON.stringify({ action: 'heartbeat' }));
    },
    onReceiveCallback: receive,
  })
  const unloadHandle = () => {
    try {
      closer()
    } catch (err) {
      console.log('WebSocket close error>>', err)
    }
  }
  window.addEventListener('unload', unloadHandle)
  return unloadHandle
}

export default (params?: any) => {
  const [isConnected, setIsConnected] = useState(false);
  const [data, setData] = useState<any>({})
  const ref = useRef({ socketStatus: false, ws: null, socketHandler: () => { } })

  useEffect(() => {
    return () => {
      wsConnectFlag = false
      if (wsInstance && !wsInstance?.isOpen()) {
        wsInstance.send(JSON.stringify({ action: 'stop-listen' }))
        wsInstance.send(JSON.stringify({ action: 'close-listen' }))
      }
      ref.current?.socketHandler?.()
    }
  }, [])

  /**
   * 发送属性
   */
  const sendMessage = useCallback(
    (payload: any = { action: 'All' }) => {
      // 无属性不发送
      const isOpenFlag = wsInstance?.isOpen()
      if (!isOpenFlag) return
      /*    if (payload?.notRequestWhenParamsNull && !payload?.params) {
           return
         } */
      wsInstance.send(JSON.stringify(payload))
    },
    [],
  )

  useEffect(() => {
    if (!wsConnectFlag) {
      wsConnectFlag = true
      ref.current.socketHandler = createSocket({
        open: (ws) => {
          setIsConnected(true);
          console.log('Socket 连接成功！')
          wsInstance = ws
          sendMessage(params)
        },
        close: (error) => {
          setIsConnected(false);
          wsConnectFlag = false
          console.log('Socket 断开连接！', error)
          // TODO: 识别服务异常
          // message.error(t('common.service.error', '服务异常！'));
        },
        receive: (message) => {
          const data = parseJsonString(message)
          if (data) {
            setData(data)
          }
        },
      })
    } else {
      sendMessage()
    }
  }, [JSON.stringify(params)])

  return { data, isConnected, sendMessage }
}
