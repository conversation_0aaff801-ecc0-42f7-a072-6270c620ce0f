import { ConnectionFactory, WebSocketClient } from '@/shared/socket';
import parseJsonString from '@/utils/parseJson';
import { useCallback, useEffect, useRef, useState } from 'react';

type OptionType = {
  open: (msg?: any) => void;
  close: (e: any) => void;
  receive: (e: any) => void;
  type: ProgressParams['type'];
};

const { hostname, port } = window.location;

const HEARTBEAT_CONFIG = {
  PING_INTERVAL: 60 * 1000,
  PONG_TIMEOUT: 5000,
  MAX_MISSED_HEARTBEATS: 3,
};

const RECONNECT_CONFIG = {
  MAX_RECONNECT_ATTEMPTS: 3,
  INITIAL_RECONNECT_DELAY: 1000,
  MAX_RECONNECT_DELAY: 10000,
  RECONNECT_BACKOFF_MULTIPLIER: 2,
};

function createSocket({ open, close, receive, type }: OptionType) {
  let wsUrl = '';
  const httpsEnabled = window.location.protocol === 'https:';
  const protocol = httpsEnabled ? 'wss://' : 'ws://';
  const protocolPort = port ? `:${port}` : '';
  const endpoint = `${protocol}${hostname}${protocolPort}`;
  wsUrl = `${endpoint}/ws/flowing/${type}`;

  const factory = new ConnectionFactory(wsUrl, []);
  const wt = new WebSocketClient(factory, '');

  let connectionEstablished = false;

  const closer = wt.open({
    onCloseCallback: (e) => {
      connectionEstablished = false;
      close(e);
    },
    onOpenCallback: (ws) => {
      connectionEstablished = true;
      open(ws);
    },
    heartbeatFn: () => {},
    onReceiveCallback: receive,
  });

  const safeCloser = () => {
    try {
      if (connectionEstablished) {
        closer();
      }
    } catch (err) {
      console.log('WebSocket safe close error>>', err);
    }
  };

  return { closer: safeCloser, wsInstance: wt };
}

interface ProgressParams {
  type: 'DCInput' | 'ACInput' | 'ACOut' | 'DCOut' | 'DCDC' | 'ACGridOut';
  message?: any;
}

export default (params: ProgressParams) => {
  const [isConnected, setIsConnected] = useState(false);
  const [data, setData] = useState<any>({});
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [lastHeartbeatTime, setLastHeartbeatTime] = useState<number>(Date.now());

  const ref = useRef({
    socketStatus: false,
    ws: null as any,
    socketHandler: () => {},
    type: '',
    reconnectTimer: null as NodeJS.Timeout | null,
    heartbeatTimer: null as NodeJS.Timeout | null,
    pongTimeoutTimer: null as NodeJS.Timeout | null,
    missedHeartbeats: 0,
    heartbeatInterval: HEARTBEAT_CONFIG.PING_INTERVAL,
    isConnecting: false,
    isClosing: false,
    hasConnectedSuccessfully: false,
    currentReconnectAttempts: 0, // 使用ref存储重连次数，避免依赖问题
  });

  const clearTimers = useCallback(() => {
    if (ref.current.reconnectTimer) {
      clearTimeout(ref.current.reconnectTimer);
      ref.current.reconnectTimer = null;
    }
    if (ref.current.heartbeatTimer) {
      clearInterval(ref.current.heartbeatTimer);
      ref.current.heartbeatTimer = null;
    }
    if (ref.current.pongTimeoutTimer) {
      clearTimeout(ref.current.pongTimeoutTimer);
      ref.current.pongTimeoutTimer = null;
    }
  }, []);

  const isWebSocketReady = useCallback(() => {
    return isConnected && ref.current.ws && !ref.current.isConnecting;
  }, [isConnected]);

  const handleCloseConnection = useCallback(() => {
    ref.current.isClosing = true;
    clearTimers();
    if (isWebSocketReady()) {
      try {
        ref.current.socketHandler();
      } catch (error) {
        console.warn('关闭socket连接失败:', error);
      }
    }
    setIsConnected(false);
    ref.current.isConnecting = false;
    ref.current.isClosing = false;
  }, [clearTimers, isWebSocketReady]);

  const handleHeartbeatResponse = useCallback(() => {
    ref.current.missedHeartbeats = 0;
    setLastHeartbeatTime(Date.now());
    clearTimers();
  }, [clearTimers]);

  const sendHeartbeat = useCallback(() => {
    if (!isWebSocketReady()) return;

    try {
      ref.current.ws.send(JSON.stringify({ action: 'ping' }));

      ref.current.pongTimeoutTimer = setTimeout(() => {
        ref.current.missedHeartbeats++;
        if (ref.current.missedHeartbeats >= HEARTBEAT_CONFIG.MAX_MISSED_HEARTBEATS) {
          handleCloseConnection();
        }
      }, HEARTBEAT_CONFIG.PONG_TIMEOUT);
    } catch (error) {
      console.error('发送心跳失败:', error);
    }
  }, [isWebSocketReady, handleCloseConnection]);

  const startHeartbeat = useCallback(() => {
    clearTimers();
    ref.current.missedHeartbeats = 0;

    if (isWebSocketReady()) {
      sendHeartbeat();
      ref.current.heartbeatTimer = setInterval(sendHeartbeat, HEARTBEAT_CONFIG.PING_INTERVAL);
    }
  }, [clearTimers, sendHeartbeat, isWebSocketReady]);

  const sendMessage = useCallback(
    (payload: any = { action: 'All' }) => {
      if (!isWebSocketReady()) return false;
      try {
        ref.current.ws.send(JSON.stringify(payload));
        return true;
      } catch (error) {
        console.error('发送消息失败:', error);
        return false;
      }
    },
    [isWebSocketReady],
  );

  const getReconnectDelay = useCallback(() => {
    return Math.min(
      RECONNECT_CONFIG.INITIAL_RECONNECT_DELAY *
        Math.pow(
          RECONNECT_CONFIG.RECONNECT_BACKOFF_MULTIPLIER,
          ref.current.currentReconnectAttempts,
        ),
      RECONNECT_CONFIG.MAX_RECONNECT_DELAY,
    );
  }, []);

  const connect = useCallback(() => {
    if (ref.current.isConnecting || ref.current.isClosing) {
      return;
    }

    ref.current.isConnecting = true;

    try {
      const { closer, wsInstance } = createSocket({
        open: (ws) => {
          setIsConnected(true);
          ref.current.ws = ws;
          ref.current.isConnecting = false;
          ref.current.hasConnectedSuccessfully = true;
          ref.current.currentReconnectAttempts = 0; // 重置重连计数
          setReconnectAttempts(0);

          setTimeout(() => {
            startHeartbeat();
            sendMessage(params?.message);
          }, 500);
        },
        close: (error) => {
          setIsConnected(false);
          ref.current.isConnecting = false;
          clearTimers();

          // 只有在从未成功连接过时才重连
          if (
            !ref.current.hasConnectedSuccessfully &&
            ref.current.currentReconnectAttempts < RECONNECT_CONFIG.MAX_RECONNECT_ATTEMPTS
          ) {
            const delay = getReconnectDelay();
            ref.current.currentReconnectAttempts++;
            setReconnectAttempts(ref.current.currentReconnectAttempts);

            ref.current.reconnectTimer = setTimeout(() => {
              connect();
            }, delay);
          }
        },
        receive: (message) => {
          try {
            const data = parseJsonString(message);
            if (data?.action === 'pong') {
              handleHeartbeatResponse();
            } else if (data) {
              setData(data);
              setLastHeartbeatTime(Date.now());
            }
          } catch (error) {
            console.error('处理消息失败:', error);
          }
        },
        type: params.type,
      });

      ref.current.socketHandler = closer;
    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      ref.current.isConnecting = false;
    }
  }, [
    params.type,
    params.message,
    sendMessage,
    startHeartbeat,
    clearTimers,
    handleHeartbeatResponse,
    getReconnectDelay,
  ]);

  useEffect(() => {
    // 完全重置状态
    ref.current.hasConnectedSuccessfully = false;
    ref.current.currentReconnectAttempts = 0;
    ref.current.isClosing = false;
    ref.current.isConnecting = false;

    setReconnectAttempts(0);
    clearTimers();
    handleCloseConnection();

    const connectTimer = setTimeout(connect, 100);
    return () => {
      clearTimeout(connectTimer);
      handleCloseConnection();
    };
  }, [JSON.stringify(params)]);

  const reconnect = useCallback(() => {
    // 重置所有状态，允许重新连接
    ref.current.hasConnectedSuccessfully = false;
    ref.current.currentReconnectAttempts = 0;
    ref.current.isClosing = false;
    ref.current.isConnecting = false;

    setReconnectAttempts(0);
    clearTimers();
    handleCloseConnection();

    setTimeout(connect, 500);
  }, [connect, clearTimers, handleCloseConnection]);

  return {
    data,
    isConnected,
    sendMessage,
    reconnect,
    reconnectAttempts,
    isReconnecting: ref.current.reconnectTimer !== null,
    lastHeartbeatTime,
    missedHeartbeats: ref.current.missedHeartbeats,
  };
};

export const getCurrent = (data: any) => {
  if (!data || !Array.isArray(data.StepCountta) || !data.Status?.Step) {
    return null;
    // return current;
    // // 查找第一个status=wait的索引
    // const firstWaitIndex = data?.findIndex((item:any) => item.status === 'wait');
    // // 获取前一个item（处理边界情况）
    // const index = firstWaitIndex > 0 ? firstWaitIndex - 1 : 0;
    // const current = firstWaitIndex > 0 ? data?.[index] : null;
  }

  const current = data.StepCount?.indexOf(data.Status?.Step);
  const obj: any = {
    id: current,
    title: data.Status?.Step,
    status: 'wait',
  };
  if (!!data.Status?.WarnMsg) {
    obj.status = 'alarm';
    obj.description = data.Status?.WarnMsg;
  } else if (!!data.Status?.ErrorMsg) {
    obj.status = 'error';
    obj.description = data.Status?.ErrorMsg;
  } else if (data.Status?.Status === 3) {
    obj.status = 'finish';
  } else if (data.Status?.Status === 0) {
    obj.status = 'wait';
  } else {
    obj.status = 'process';
  }
  return obj;
};
// 格式化流程状态数据
export const formateProgressData = (data: any) => {
  // 定位当前执准备开始行、执行中的节点
  const current = data?.StepCount?.indexOf(data.Status?.Step);
  const progress = data?.StepCount?.map((item: string, index: number) => {
    const obj: any = {
      id: index,
      title: item,
      status: 'wait',
    };
    if (index < current) {
      obj.status = 'finish';
    }
    // 为空或0还没执行到，为1则表示开始执行，为2执行中，为3执行完成
    if (data.Status?.Step === item) {
      if (!!data.Status?.WarnMsg) {
        obj.status = 'alarm';
        obj.description = data.Status?.WarnMsg;
      } else if (!!data.Status?.ErrorMsg) {
        obj.status = 'error';
        obj.description = data.Status?.ErrorMsg;
      } else if (data.Status?.Status === 3) {
        obj.status = 'finish';
      } else if (data.Status?.Status === 0) {
        obj.status = 'wait';
      } else {
        obj.status = 'process';
      }
    }
    return obj;
  });
  return progress;
};

// 格式化流程状态数据
export const formateProgressDataByDevice = (data: any, needDevice = false) => {
  if (needDevice) {
  }

  if (!Array.isArray(data?.StepCount) || !Array.isArray(data?.DeviceList)) return [];

  const progress = data.DeviceList.map((device: string, ind: number) => {
    const stepCount = data?.StepCount?.map((item: string, index: number) => {
      const obj: any = {
        id: ind * (data?.StepCount?.length || 0) + index,
        title: `${device || ''}${item}`,
        status: 'wait',
      };

      return obj;
    });
    return stepCount;
  })
    .flatMap((item: any) => item)
    .filter((item: any) => !!item);
  // 定位当前执准备开始行、执行中的节点
  const currentStep = `${data?.PCSID || ''}${data.Status?.Step}`;
  const currentIndex = progress.findIndex((item: any) => item?.title === currentStep);
  const res = progress.map((item: any, index: number) => {
    let obj = { ...item };
    if (index < currentIndex) {
      obj.status = 'finish';
    }
    // 为空或0还没执行到，为1则表示开始执行，为2执行中，为3执行完成

    if (currentStep === obj.title) {
      if (!!data.Status?.WarnMsg) {
        obj.status = 'alarm';
        obj.description = data.Status?.WarnMsg;
      } else if (!!data.Status?.ErrorMsg) {
        obj.status = 'error';
        obj.description = data.Status?.ErrorMsg;
      } else if (data.Status?.Status === 3) {
        obj.status = 'finish';
      } else if (data.Status?.Status === 0) {
        obj.status = 'wait';
      } else {
        obj.status = 'process';
      }
    }
    return obj;
  });

  return res;
};
/**
 * 合并进度数据
 * @param data1 旧数据
 * @param data2 新数据
 * @returns
 */
export const mergeProgressData = (data1: any[], data2: any[]) => {
  // 如果新数据中存在不在旧数据中的节点，直接返回新数据
  if (Array.isArray(data2)) {
    if (data2.some((item) => !data1.some((oldItem) => oldItem.title === item.title))) {
      return data2;
    }
  }
  // 使用id+title作为唯一键
  const mergedMap = new Map();
  if (Array.isArray(data1)) {
    data1.forEach((item) => {
      const key = `${item.id}-${item.title}`;
      mergedMap.set(key, { ...item });
    });
  }
  if (Array.isArray(data2)) {
    // 添加第二个数组的数据（会覆盖第一个数组中相同key的数据）
    data2.forEach((item) => {
      const key = `${item.id}-${item.title}`;
      // 如果存在相同key，合并属性（后者覆盖前者）
      if (mergedMap.has(key)) {
        const oldItem = mergedMap.get(key);
        // 告警和报错的状态保持
        if (oldItem.status === 'alarm' || oldItem.status === 'error') {
          mergedMap.set(key, { ...oldItem, ...item, status: oldItem.status });
        } else {
          mergedMap.set(key, { ...mergedMap.get(key), ...item });
        }
      } else {
        mergedMap.set(key, { ...item });
      }
    });
  }
  // 将映射表转换为数组并返回
  return Array.from(mergedMap.values());
};
