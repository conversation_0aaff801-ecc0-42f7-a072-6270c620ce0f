export const config = {
  ViewType: 'Tile', //Tile/Switch
  NavItems: [
    { label: '首页', ViewType: 'Show' },
    { label: '监控', ViewType: 'Hide' },
    { label: '统计', ViewType: 'Hide' },
    { label: '告警', ViewType: 'Hide' },
    { label: '运维', ViewType: 'Show' },
  ],
  Header: {
    SystemStatus: {
      Name: 'CurrentSystemStatus',
      Label: '系统状态',
      ViewType: 'Content',
    },
    RemainPower: {
      Name: '',
      Label: '剩余电量',
      Unit: 'kwh',
    },
    SOC: {
      Name: '',
      Label: 'SoC',
      Unit: '%',
      ViewType: 'progressAndLabel',
    },
    Custom: [
      {
        Name: 'Capacity',
        Label: '装机容量',
        Unit: 'kwh',
      },
      {
        Name: 'maxTemp',
        Label: '最高温度',
        Unit: '°C',
      },
      {
        Name: 'minTemp',
        Label: '最低温度',
        Unit: '°C',
      },
      {
        Name: 'CountInputCapacity',
        Label: '累计充电量',
        Unit: 'kwh',
      },
      {
        Name: 'CountOutCapacity',
        Label: '累计放电量',
        Unit: 'kwh',
      },
      {
        Name: 'BMSStatus',
        Label: 'BMS',
        Unit: '',
        device: 'BMS',
      },
      {
        Name: 'maxVoltage',
        Label: '最大电压',
        Unit: '',
        // device: 'PCS',
      },
      {
        Name: 'minVoltage',
        Label: '最小电压',
        Unit: '',
        // ViewType: 'Content',
        // device: 'TemperContr',
      },
      {
        Name: 'remainingChargingTime',
        Label: '剩余充电时间',
        Unit: '分钟',
        // device: 'ChargeGun',
      },
      {
        Name: 'AlarmCount',
        Label: '故障码',
        fontColor: 'red',
        Unit: '',
      },
    ],
  },
  // Content:{
  //   ViewType: 'Switch', //Tile/Switch
  // },
  Input: {
    Label: '补能',
    Content: {
      DCInput: {
        Label: '补能',
        ViewType: 'Show',
        // VIN: {
        //   Label: 'vin码',
        //   ViewType: 'SingleSelect',
        //   Name: 'VINCode',
        // },
        DCInputGroup: [
          {
            Label: '1#电池组',
            DeviceID: 'BMS1',
            Name: 'BMSCurrentState',
            LabelStyle: {
              maxHeight: 100,
              minHeight: 100,
            },
            LabelGoup: [
              {
                Unit: 'V',
                Label: '电压',
                ViewType: 'Label',
                DeviceID: 'BMS1',
                Name: 'BMS2VCU07_PackVolt',
              },
              {
                Unit: 'A',
                Label: '电流',
                ViewType: 'Label',
                DeviceID: 'BMS1',
                Name: 'BMS2VCU07_BusCurrent',
              },
            ],
            InputPowerStyle: {
              layout: 'column', // column | row

            },
            InputPowerGoups: [
              {
                Title: '1#',
                Unit: '',
                Label: '',
                Image: 'Smile.png',
                ViewType: 'Content',
                Name: 'BMS_DcChargeC_1',
                Button: {
                  ButtonLabel: '补能复位',
                  API: 'url',
                  Type: 'Post',
                  RequestPara: '',
                },
              },
              {
                Title: '2#',
                Unit: '',
                Label: 'Status',
                Image: 'Smile.png',
                ViewType: 'Content',
                Name: 'BMS_DcChargeC_2',
                Button: {
                  ButtonLabel: '补能复位',
                  API: 'url',
                  Type: 'Post',
                  RequestPara: '',
                },
              },
              {
                Title: '3#',
                Unit: '',
                Label: 'Status',
                Image: 'Smile.png',
                ViewType: 'Content',
                Name: 'BMS_DcChargeC_3',
                Button: {
                  ButtonLabel: '补能复位',
                  API: 'url',
                  Type: 'Post',
                  RequestPara: '',
                },
              },
            ],
          },
        ],
      },
      DCDC: [
        {
          Label: '供电',
          SubLabel: '直流供电',
          ViewType: 'Show',
          DeviceID: 'BMS1',
          LabelLayout: {
            layout: 'column',
            style: {
              maxHeight: 100,
              minHeight: 100,
            },
          },
          LabelGoup: [
            {
              Unit: 'V',
              Label: '电压',
              ViewType: 'Label',
              DeviceID: 'BMS1',
              Name: 'BMS2VCU07_PackVolt',
            },
            {
              Unit: 'A',
              Label: '电流',
              ViewType: 'Label',
              DeviceID: 'BMS1',
              Name: 'BMS2VCU07_BusCurrent',
            },
          ],
          InputPowerGoups: [
            {
              showCheck: true,
              Title: '1#',
              Unit: '',
              Label: '',
              Image: 'Smile.png',
              ViewType: 'Content',
              Name: 'BMS_DcChargeC_1',
              Button: {
                ButtonLabel: '补能复位',
                API: 'url',
                Type: 'Post',
                RequestPara: '',
              },
            },
          ],
          ButtonItems: {
            Label: '',
            Items: [
              {
                Label: '',
                // 开始补能需要的传参
                Value: {
                  // type: 'acinput',
                  // deviceID: 'Dock1',
                  // gunID: '',
                  userInputPara: '{Greater0}',
                },
                Group: [
                  {
                    Name: 'start',
                    Value: 1,
                    Descript: "开始+前面的'Title'内容",
                  },
                  {
                    Name: 'stop',
                    Value: 2,
                    Descript: "停止+前面的'Title'内容",
                  },
                ],
              },
            ],
          },
          StatusFlag: {
            Name: 'progress',
            StatusType: 'progressStep',
            ErrorMsg: '',
            WarnMsg: '',
            CurrentMsg: '',
            Status: 0,
            Step: 'PCS待机',
          },
        },
      ],
    },
  },
  Notification: {
    show: true,
    dataKey: 'AlarmCount'
  }
};
