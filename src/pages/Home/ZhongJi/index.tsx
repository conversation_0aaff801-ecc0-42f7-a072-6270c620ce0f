import React, { useState, useEffect } from 'react';
import useRealTime from '../useRealTime';
import ContentLayout from '../components/ContentLayout';
import { HeaderSection } from '../components/HeaderSection';
import { useDeviceStore } from '@/store/deviceStore';
import { httpGet, httpPost } from '@/shared/http';
import { useConfigStore } from '@/store/configStore';
import AlarmModal from '@/components/AlarmModal';
import storage from '@/shared/storage';
import { config } from './config';
import { useRequest } from 'ahooks';
import { round } from 'lodash';

export const HomeScreenPage = () => {
  // const { data } = useRealTime();
  const [data, setData] = useState<any>({});
  const { setGpsDevice, setCellularDevice, setSystemStatus } = useDeviceStore();
  const { setConfig } = useConfigStore();
  const [visible, setVisible] = useState<boolean>(false);
  const [lastClosedTime, setLastClosedTime] = useState<number>(0); // 上次关闭时间戳

  /*  const [config, setConfig] = useState<any>(null);
  
  const getConfig = async () => {
    try {      
      const res = await httpGet('/api/configs/get?fileName=MainViewRequestAddress.cfg');
      setConfig(res);
    } catch (error) {
      // message.error('获取首页配置信息失败');
    }
  };*/

  // 组件挂载时，从storage中读取上次关闭时间
  useEffect(() => {
    const savedLastClosedTime = storage.get('batteryAlarmLastClosedTime');
    if (savedLastClosedTime) {
      setLastClosedTime(savedLastClosedTime);
    }
  }, []);

  useEffect(() => {
    // 正在补能则不提示电量低
    if (data?.CurrentSystemStatus === 3 || data?.CurrentSystemStatus === 5) {
      setVisible(false);
      return;
    }
    // 检查是否需要显示弹窗：SOC < 10 并且距离上次关闭超过10分钟
    const now = Date.now();
    const tenMinutes = 10 * 60 * 1000;
    if (data.SOC < 10 && now - lastClosedTime > tenMinutes) {
      setVisible(true);
    }
  }, [data?.SOC, data?.ts, lastClosedTime]);

  const getSystemStatus = (current: number) => {
    if (current < -2) {
      return 3;
    }
    if (current > 2) {
      return 4;
    }
    return 2;
  }

  useRequest(async() => {
    const res = await httpPost('/api/RootInterface/GetEMSDeviceCurrentValues',[
        {
            "deviceList": [
                "4G", "BMS1", "GPS", "ComputerDevice1"
            ],
            "Items": []
        }
    ])
    const { data } = res;
    const gpsDevice = data.find((d: any) => d.device.name === 'GPS');
    const cellularDevice = data.find((d: any) => d.device.name === '4G');
    gpsDevice && setGpsDevice(gpsDevice);
    cellularDevice && setCellularDevice(cellularDevice);
    setConfig(config);

    const bms1 = data.find((d: any) => d.device.name === 'BMS1'); // bms1
    const soc = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU07_SOC'); // soc
    const countInputCapacity = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU18_TotDschrggEnergy');//   累计充电量
    const countOutCapacity = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU18_TotChrgEnergy');  // 累计放电
    const alarmCount = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU07_FltCode');  // 告警码
    const current = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU07_BusCurrent');  // 电流
    const voltage = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU07_PackVolt');  // 电压
    const maxVoltage = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU17_MaxUCell');  // 最大电压
    const minVoltage = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU17_MinUCell');  // 最小电压
    const maxTemp = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU14_MaxTemp');  // 最高温度
    const minTemp = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU14_MinTemp');  // 最低温度
    const remainingChargingTime = bms1?.itemList?.find((d: any) => d.name === 'BMS2VCU19_ChrgTime');

    const Capacity = 1597;  // 装机容量固定写死
    setSystemStatus(getSystemStatus(current?.value || 0))
    setData({
      SOC: soc?.value,
      CurrentSystemStatus: getSystemStatus(current?.value || 0),
      ChargePower: round(Capacity * (((soc?.value || 0) / 100)), 2),
      Capacity: Capacity,
      CountInputCapacity: countInputCapacity.value,
      CountOutCapacity: countOutCapacity.value,
      AlarmCount: alarmCount.value,
      Devices: data,
      voltage: voltage.value,
      maxVoltage: maxVoltage?.value,
      minVoltage: minVoltage?.value,
      maxTemp: maxTemp?.value,
      minTemp: minTemp?.value,
      remainingChargingTime: remainingChargingTime?.value
    })
  }, {pollingInterval: 3000})

  const handleClose = () => {
    setVisible(false);
    const currentTime = Date.now();
    setLastClosedTime(currentTime);
    storage.set('batteryAlarmLastClosedTime', currentTime);
  };
  return (
    <div className="flex w-full flex-row justify-center bg-[#001857] p-4 pb-1">
      {config ? (
        <div className="relative w-[1280px] overflow-hidden bg-[#001857]">
          <div className="relative w-full">
            <HeaderSection data={data} config={config} />
          </div>
          <ContentLayout ViewType={config.ViewType} config={config} data={data}></ContentLayout>
        </div>
      ) : (
        <div className="flex h-[700px] w-full items-center justify-center text-2xl">
          <h1>请先配置首页需要展示的内容</h1>
        </div>
      )}
      {/* <AlarmModal
        visible={visible}
        content={'电量过低，请及时充电'}
        onClose={handleClose}
        onConfirm={handleClose}
      /> */}
    </div>
  );
};
