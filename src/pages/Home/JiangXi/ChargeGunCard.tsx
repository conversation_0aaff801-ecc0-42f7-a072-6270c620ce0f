import { getGunCCCSText, getGunCCGSText } from '@/constants/device';
import { formatMilliseconds } from "@/utils/formatDate";
import { Button } from 'antd';
import { noop } from 'lodash';
import { useCallback, useMemo } from 'react';



export const ChargeGunCard = ({ gunIndex = 1, data = {}, gunOperate = noop }: any): JSX.Element => {

  const status = useMemo(() => {
    return data?.device?.status === 'Connect';
  }, [data?.device]);

  const getGunBlock = useCallback((type = 'A') => {
    const info = data[type] || {};
    const topPosition = type === 'A' ? 'top-11' : 'top-[192px]'; // B枪位置 = A枪top位置(44px) + A枪高度(143px) - 22px
    // info.status = 0; // Mock
    const borderColor = info?.status == 1 ? 'outline-sky-400' : 'outline-[#4365A9]';
    const onlineBgColor = info?.status == 1 ? 'bg-green-500' : 'bg-[#4365A9]';
    const titleColor = info?.status == 1 ? 'text-sky-400' : 'text-[#4365A9]';
    const onlineTextColor = info?.status == 1 ? 'text-green-500' : 'text-[#4365A9]';

    /*  0: 初始化状态
        1:空闲状态
        2:等待启动
        3:启动中
        4:供电中
        5:停止供电中
        6:供电结算中
        7:供电完成未拔枪
        8:启动失败
        9: 故障状态 */
    const chargeStatus = info?.CCSG;
    const canCharge = chargeStatus == 2;
    const isCharging = chargeStatus == 4; //
    const btnBorderColor = (info?.status == 1 && (canCharge || isCharging)) ? 'border-[#fff]' : 'border-[#4365A9]';
    const chargeText = getGunCCGSText(chargeStatus);

    return <div className={`absolute left-3 w-[207px] h-[143px] self-stretch self-stretch px-3.5 pt-2 pb-1 outline outline-[3px] outline-offset-[-3px]  inline-flex flex-col justify-start items-start gap-1.5 ${borderColor} ${topPosition}`}>
      <div className="self-stretch inline-flex justify-start items-center gap-1">
        <div className="flex justify-start items-center gap-2.5" />
        <div className="flex-1 flex justify-start items-center gap-2.5">
          <img src={info?.status == 1 ? "/home/<USER>" : "/home/<USER>"} alt="Icon" />
          <div className={`justify-start text-lg font-semibold font-['PingFang_SC'] leading-snug ml-[-6px] ${titleColor}`}>{type}枪</div>
        </div>
        <div className="flex justify-start items-center gap-1">
          <div className={`w-1.5 h-1.5  rounded-full ${onlineBgColor}`} />
          <div className="rounded-[10px] flex justify-start items-center gap-2.5">
            <div className={`justify-start  text-xs font-semibold font-['PingFang_SC'] leading-none ${onlineTextColor}`}>{getGunCCCSText(info?.status)}</div>
          </div>
        </div>
      </div>
      <div className="self-stretch pt-1 flex flex-col justify-start items-start gap-0.5">
        <div className="self-stretch h-3.5 inline-flex justify-start items-start">
          <div className="flex-1 flex justify-start items-end gap-[3px]">
            <div className="w-7 flex justify-start items-center gap-2.5">
              <div className="w-12 justify-start text-[#3270A5] text-xs font-medium font-['PingFang_SC'] leading-none">电压</div>
            </div>
            <div className="flex justify-start items-end">
              <div className="flex justify-start items-center gap-2.5">
                <div className="justify-start text-cyan-100 text-xs font-medium font-['PingFang_SC'] leading-none">{info.CCV}V</div>
              </div>
            </div>
          </div>
          <div className="flex-1 flex justify-start items-end gap-[3px]">
            <div className="flex justify-start items-center gap-2.5">
              <div className="w-7 justify-start text-[#3270A5] text-xs font-medium font-['PingFang_SC'] leading-none">电流</div>
            </div>
            <div className="flex justify-start items-end">
              <div className="flex justify-start items-center gap-2.5">
                <div className="justify-start text-cyan-100 text-xs font-medium font-['PingFang_SC'] leading-none">{info.CCC}A</div>
              </div>
            </div>
          </div>
        </div>
        <div className="w-32 h-3.5 inline-flex justify-start items-end gap-[3px]">
          <div className="flex justify-start items-center gap-2.5">
            <div className="w-7 justify-start text-[#3270A5] text-xs font-medium font-['PingFang_SC'] leading-none">功率</div>
          </div>
          <div className="flex justify-start items-end">
            <div className="flex justify-start items-center gap-2.5">
              <div className="justify-start text-cyan-100 text-xs font-medium font-['PingFang_SC'] leading-none">{info.CCP}kw</div>
            </div>
          </div>
        </div>
        <div className="w-32 h-3.5 inline-flex justify-start items-end gap-2">
          <div className="flex justify-start items-center gap-2.5">
            <div className="justify-start text-[#3270A5] text-xs font-medium font-['PingFang_SC'] leading-none">本次供电量</div>
          </div>
          <div className="flex justify-start items-end">
            <div className="flex justify-start items-center gap-2.5">
              <div className="justify-start text-cyan-100 text-xs font-medium font-['PingFang_SC'] leading-none">{info.CCTC}kwh</div>
            </div>
          </div>
        </div>
        <div className="h-3.5 inline-flex justify-start items-end gap-2">
          <div className="flex justify-start items-center gap-2.5">
            <div className="justify-start text-[#3270A5] text-xs font-medium font-['PingFang_SC'] leading-none">本次供电时长</div>
          </div>
          <div className="flex justify-start items-end">
            <div className="flex justify-start items-center gap-2.5">
              <div className="justify-start text-cyan-100 text-xs font-medium font-['PingFang_SC'] leading-none">{formatMilliseconds((info.CDTC || 0) * 1000)}</div>
            </div>
          </div>
        </div>
      </div>
      {/* 按钮容器 */}
      <div className="flex justify-center w-full">
        <Button
          type="primary"
          size="small"
          disabled={!canCharge && !isCharging} // 临时注释，测试环境数据不行
          style={{
            color: isCharging ? '#fe4545' : ((info?.status == 1 && canCharge) ? '#fff' : '#979797')
          }}
          className={`cst-btn text-xs px-1.5 py-1 rounded-lg outline-none inline-flex justify-center items-center gap-[3.10px] ${btnBorderColor}`}
          onClick={() => {
            gunOperate({
              status: chargeStatus,
              "type": "dcout",//直流放电 dcout
              "deviceID": data?.device?.name, //充电桩ID
              "gunID": type//A枪或B枪
            })
          }}
        >
          {isCharging ? "结束供电" : (canCharge ? "开始供电" : chargeText)}
        </Button>
      </div>
    </div>
  }, [data, gunOperate])

  return (
    <div className="relative w-[234px] h-[350px] mt-1 mx-auto">
      <div className="relative h-full">
        <div className="relative w-full h-full">
          <div className="relative h-full bg-img">
            {/* Background and corner decorations */}
            <img
              className="absolute w-[234px] h-[350px] top-0 left-0"
              alt="Rectangle"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[39px] h-[40px] top-[310px] right-0"
              alt="Bottom right corner"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[38px] h-[40px] top-0 right-0"
              alt="Top right corner"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[39px] h-[40px] top-0 left-0"
              alt="Top left corner"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[39px] h-[40px] bottom-0 left-0"
              alt="Bottom left corner"
              src="/home/<USER>"
            />

            {/* Title bar */}
            <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[149px] h-[33px]">
              <div className="relative w-[155px] h-[35px]">
                <img
                  className="absolute w-[149px] h-[33px] top-0.5 left-[3px]"
                  alt="Vector"
                  src="/home/<USER>"
                />

                <div className="absolute top-1.5 left-[33px] font-medium text-[#39abff] text-lg text-center tracking-[0] leading-normal [font-family:'PingFang_SC-Medium',Helvetica]">
                  {gunIndex}#充电桩
                </div>

                <div className="absolute w-[9px] h-2 top-[27px] left-[9px]">
                  <img
                    className="absolute w-[15px] h-3.5 -top-0.5 left-[-1px]"
                    alt="Group"
                    src="/home/<USER>"
                  />
                </div>

                <div className="absolute w-[9px] h-2 top-[27px] left-[136px] rotate-180">
                  <img
                    className="absolute w-[25px] h-3.5 -top-1 left-[-1px] -rotate-180"
                    alt="Group"
                    src="/home/<USER>"
                  />
                </div>

                <img
                  className="absolute w-[155px] h-4 top-0 left-0"
                  alt="Group"
                  src="/home/<USER>"
                />

                <div className="absolute w-[9px] h-[9px] top-[15px] left-[118px] bg-[#858585] rounded-[4.5px]" style={{ backgroundColor: status ? '#00ff50' : '#858585' }} />
              </div>
            </div>
          </div>
        </div>

        {/* A gun */}
        {getGunBlock('A')}
        {getGunBlock('B')}
      </div>
    </div>
  );
};
