import { Card, CardContent } from "@/components/ui/card";
import { Button, message, Modal } from 'antd';
import { isNil } from 'lodash';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import { getStatusIcon } from '../utils';
import { ChargeGunCard } from './ChargeGunCard';


// 定义电压电流映射关系
const parameterMap: any = {
  'Invert_PhaseA_V': { label: 'A相电压', unit: 'V' },
  'Invert_PhaseB_V': { label: 'B相电压', unit: 'V' },
  'Invert_PhaseC_V': { label: 'C相电压', unit: 'V' },
  'Invert_PhaseA_I_Group1': { label: 'Ⅰ组A相电流', unit: 'A' },
  'Invert_PhaseB_I_Group1': { label: 'Ⅰ组B相电流', unit: 'A' },
  'Invert_PhaseC_I_Group1': { label: 'Ⅰ组C相电流', unit: 'A' },
};

const getDetail = (d: any) => {
  const obj: any = { device: d.device, A: {}, B: {} };
  for (const item of d.itemList) {
    if (["CC1CS1", "CCC1", "CCV1", "CCP1", "CCTC1", "CDTC1", "CCSG1"].includes(item.name)) {
      if (item.name === 'CC1CS1') {// 状态
        obj.A['status'] = item.value;
      } else {
        obj.A[item.name.replace(/1/g, '')] = item.value;
      }
    }
    if (["CC1CS2", "CCC2", "CCV2", "CCP2", "CCTC2", "CDTC2", "CCSG2"].includes(item.name)) {
      if (item.name === 'CC1CS2') { // 状态
        obj.B['status'] = item.value;
      } else {
        obj.B[item.name.replace(/2/g, '')] = item.value;
      }
    }
  }
  return obj;
}

// 获取状态文本 // status 1:开始、2:补能中 或 0:停止 | 启动1，0为关闭，2为进行中
const getStatusText = (status: number) => {
  switch (status) {
    case 1: return '启动供电';
    case 2: return '供电中';
    case 0: return '停止供电';
    default: return '停止供电';
  }
};

function GongDianSection({ activeTab = 'buneng', data = {} }: any) {
  const [station1, setStation1] = useState<any>({});
  const [station2, setStation2] = useState<any>({});
  const [station3, setStation3] = useState<any>({});
  const [powerReadings, setPowerReadings] = useState<any>([]);
  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  const { systemStatus } = useDeviceStore();
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 判断是否可以供电
  const canGongdian = useMemo(() => {
    if (systemStatus === 2) {
      return !!systemStatus;
    } else {
      return false
    }
  }, [systemStatus]);

  // 判断直流是否可以供电
  const dcBtnDisabled = useMemo(() => {
    if (data.DCOutStatus != 0 || !canGongdian) {
      return true // 不可以供电
    } else {
      return false
    }
  }, []);

  // 判断交流是否可以供电
  const acBtnDisabled = useMemo(() => {
    if (data.ACOutStatus != 0 || !canGongdian) {
      return true // 不可以供电
    } else {
      return false
    }
  }, []);

  const isPrepareOutput = useMemo(() => {
    return data.ACOutStatus == 1
  }, [data.ACOutStatus]);


  useEffect(() => {
    if (data.Devices?.length > 0) {
      // 电池组
      for (const d of data.Devices) {
        if (d.device.deviceID === 'ChargingStation1') {
          setStation1(getDetail(d))
        }
        else if (d.device.deviceID === 'ChargingStation2') {
          setStation2(getDetail(d))
        }
        else if (d.device.deviceID === 'ChargingStation3') {
          setStation3(getDetail(d))
        }
        else if (d.device.deviceID === 'PCS6') {
          const list = [];
          if (d?.itemList?.length > 0) {
            for (const item of d.itemList) {
              if (item.name in parameterMap) {
                const { label, unit } = parameterMap[item.name];
                list.push({
                  id: item.name,
                  label,
                  value: isNil(item.value) ? '--' : item.value,
                  unit
                });
              }
            }
          }
          // list  根据label 排序
          list.sort((a, b) => a.label.localeCompare(b.label));
          setPowerReadings(list)
        }

      }
    }
  }, [data?.Devices]);

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  const dcGunOperate = useCallback(async (params: Record<string, any>) => {

    if (!canGongdian && params.status !== 4) {
      message.warning('当前状态无法供电');
      return;
    }

    if (isOperating) {
      message.warning('操作冷却中，请稍后再试');
      return;
    }

    try {
      // 枪正常充电时停止 CCSG1 和 CCSG2
      if (params.status == 4) {
        // 停止供电确认
        Modal.confirm({
          title: '停止供电确认',
          content: `确认要停止${params.gunID}枪直流供电吗？`,
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            // 清除之前的定时器（如果有的话）
            clearOperatingTimeout();
            setIsOperating(true);
            const result = await HomeScreenService.stopCurrentOperation('dc', false, params);
            if (result.success) {
              message.success(result.message || '停止供电成功');
              // 成功后3秒冷却时间
              timeoutRef.current = setTimeout(() => {
                setIsOperating(false);
                timeoutRef.current = null;
              }, 3000);
            } else {
              message.error(result.message || '停止供电失败');
              // 失败后立即可以再次点击
              setIsOperating(false);
            }
          }
        });
      } else {
        // 清除之前的定时器（如果有的话）
        clearOperatingTimeout();
        setIsOperating(true);
        // 开始供电
        const result = await HomeScreenService.startDCPowerSupply(params);
        if (result.success) {
          message.success(result.message || '开始供电');
          // 成功后3秒冷却时间
          timeoutRef.current = setTimeout(() => {
            setIsOperating(false);
            timeoutRef.current = null;
          }, 3000);
        } else {
          message.error(result.message || '启动供电失败');
          // 失败后立即可以再次点击
          setIsOperating(false);
        }
      }
    } catch (error) {
      message.error('操作异常，请检查设备状态');
      // 异常后立即可以再次点击
      setIsOperating(false);
    }
  }, [dcBtnDisabled, data.DCOutStatus, canGongdian, isOperating, clearOperatingTimeout]);

  const acOperate = useCallback(async () => {
    if (!canGongdian && data.ACOutStatus !== 2) {
      message.warning('当前状态无法供电');
      return;
    }

    if (isOperating) {
      message.warning('操作冷却中，请稍后再试');
      return;
    }

    try {
      if (data.ACOutStatus == 2) {
        if ([3, 4, 5, 6].includes(data.DCOutStatus)) {
          message.warning(`当前直流供电状态为${data.DCOutStatus}，无法停止交流供电`);
          return;
        }
        // 停止供电确认
        Modal.confirm({
          title: '停止供电确认',
          content: '确认要停止交流供电吗？',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            // 清除之前的定时器（如果有的话）
            clearOperatingTimeout();
            setIsOperating(true);
            const result = await HomeScreenService.stopCurrentOperation('ac', false);
            if (result.success) {
              message.success('停止供电成功');
              // 成功后3秒冷却时间
              timeoutRef.current = setTimeout(() => {
                setIsOperating(false);
                timeoutRef.current = null;
              }, 3000);
            } else {
              message.error(result.message || '停止供电失败');
              // 失败后立即可以再次点击
              setIsOperating(false);
            }
          }
        });
      } else {
        // 清除之前的定时器（如果有的话）
        clearOperatingTimeout();
        setIsOperating(true);
        // 开始供电
        const result = await HomeScreenService.startACPowerSupply();
        if (result.success) {
          message.success('开始供电');
          // 成功后3秒冷却时间
          timeoutRef.current = setTimeout(() => {
            setIsOperating(false);
            timeoutRef.current = null;
          }, 3000);
        } else {
          message.error(result.message || '启动供电失败');
          // 失败后立即可以再次点击
          setIsOperating(false);
        }
      }
    } catch (error) {
      message.error('操作异常，请检查设备状态');
      // 异常后立即可以再次点击
      setIsOperating(false);
    }
  }, [acBtnDisabled, data.DCOutStatus, data.ACOutStatus, canGongdian, isOperating, clearOperatingTimeout]);

  return (<>
    <div className="flex mt-2 gap-4">
      {/* Middle Column */}
      <div className="flex-1">
        <div className="flex flex-col items-center justify-center">
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center gap-[5px] p-1 rounded-[7px]">
              <img
                className="relative w-10 h-9"
                alt="Frame"
                src="/home/<USER>"
              />
              <div className="inline-flex items-center gap-2.5">
                <div className="mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#3ae353] text-[24px] leading-5 tracking-[0]">
                  直流供电
                </div>
                <div className="w-[30px] h-[30px] bg-[100%_100%]"
                  style={{
                    backgroundImage: `url(${getStatusIcon(data.ACOutStatus)})`,
                    backgroundSize: '100% 100%'
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-2 mt-2">
          <ChargeGunCard gunIndex={1} data={station1} gunOperate={dcGunOperate} />
          <ChargeGunCard gunIndex={2} data={station2} gunOperate={dcGunOperate} />
          <ChargeGunCard gunIndex={3} data={station3} gunOperate={dcGunOperate} />
        </div>
      </div>
      {/* Right Column */}
      <div className="flex flex-col gap-4 w-[430px] mr-3 pt-3">
        <div className="flex justify-center items-center gap-[5px]">
          <div className="inline-flex items-start px-2.5 py-1 rounded-[7px]">
            <img
              className="relative w-5 h-7"
              alt="Union"
              src="/home/<USER>"
            />
          </div>

          <div className=" inline-flex justify-center items-center gap-2.5">
            <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#3ae353] text-[24px] leading-5 whitespace-nowrap tracking-[0]">
              交流供电
            </div>
            <div className="w-[30px] h-[30px] bg-[100%_100%]"
              style={{
                backgroundImage: `url(${getStatusIcon(data.ACOutStatus)})`,
                backgroundSize: '100% 100%'
              }}
            />
          </div>
        </div>

        <Card className="w-full max-w-[430px] h-[340px] rounded-lg bg-gradient-to-t from-[rgba(21,94,216,0.4)] to-transparent border-0">
          <CardContent className="p-4 pt-10">
            <div className="flex flex-wrap h-[320px] overflow-y-auto">
              {powerReadings.map((reading: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center w-1/2 mb-8"
                >
                  <span className="text-[#326fa5] text-xl min-w-[100px]">
                    {reading.label}
                  </span>
                  <div className="flex items-baseline">
                    <span className="text-[#c7f2ff] text-xl">
                      &nbsp;{reading.value}
                    </span>
                    <span className="text-[#c7f2ff] text-xl ml-1">
                      {reading.unit}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>


      </div>
    </div >
    {/* 底部按钮 */}
    <Button
      className="w-full h-14 mt-3 mb-1 rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
      onClick={acOperate}
      disabled={isPrepareOutput}
    >
      <span className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#81dc4a] text-2xl leading-[18px] whitespace-nowrap tracking-[0]"
        style={{ color: data.ACOutStatus == 2 ? '#fe4545' : '' }}>
        {isPrepareOutput ? '供电启动…' : getStatusText(data.ACOutStatus == 2 ? 0 : 1)}
      </span>
    </Button>
  </>
  );
}

export default GongDianSection;
