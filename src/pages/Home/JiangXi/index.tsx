import { Separator } from "@/components/ui/separator";
import { useCallback, useEffect, useState } from 'react';
import { StatusSection } from "../WrapperScreen/StatusSection";
import BuNengSection from './BuNengSection';
import GongDianSection from './GongDianSection';
import useRealTime from '../useRealTime';
import { useDeviceStore } from '@/store/deviceStore';
import { message, Modal } from 'antd';
import { httpPost } from '@/shared/http';
import { connectParams, connectParams_0, disConnectParams, disConnectParams_0 } from './data';

export const HomeScreenPage = (): JSX.Element => {
  const [activeTab, setActiveTab] = useState<'buneng' | 'gongdian'>('buneng');
  const { data, isConnected = false } = useRealTime();
  const { setGpsDevice, setCellularDevice, setSystemStatus } = useDeviceStore()

  useEffect(() => {
    // 更新设备状态
    if (data.Devices?.length > 0) {
      const gpsDevice = data.Devices.find((d: Record<string, any>) => d.device.type === 'GPS')
      const cellularDevice = data.Devices.find((d: Record<string, any>) => d.device.type === '4G')
      setGpsDevice(gpsDevice)
      setCellularDevice(cellularDevice)
    }
    if (data) {
      setSystemStatus(data.CurrentSystemStatus);
    }
  }, [data]);

  const operateCommand = useCallback((type: string) => {
    Modal.confirm({
      title: '操作确认',
      content: `是否要操作 ${type === 'connect' ? '断路器闭合' : '断路器断开'}？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await httpPost('/api/RootInterface/WriteCommand', type === 'connect' ? connectParams : disConnectParams);
          if (res.code == 0) {
            message.info(type === 'connect' ? '断路器闭合操作成功！' : '断路器断开操作成功！');
            setTimeout(() => {
              httpPost('/api/RootInterface/WriteCommand', type === 'connect' ? connectParams_0 : disConnectParams_0);
            }, 3000)
          } else {
            message.error(type === 'connect' ? '断路器闭合操作失败！' : '断路器断开操作失败！')
          }
        } catch (error) {
          message.error('操作失败，请重试')
        }
      }
    });

  }, []);

  const tabChange = useCallback((tab: 'buneng' | 'gongdian') => {
    setActiveTab(tab);

  }, []);

  return (
    <div className="bg-[#001857] flex flex-row justify-center w-full p-4 pb-1">
      <div className="bg-[#001857] overflow-hidden w-[1280px] relative">
        {/* Header Section */}
        <div className="relative w-full">
          <StatusSection data={data} />
        </div>
        {/* Main Content */}
        <div className="w-full pb-1 mt-1 [background:linear-gradient(175deg,rgba(0,135,255,0)_2.97%,rgba(0,135,255,0.26)_94.34%)]">
          <div className="relative flex w-full items-center justify-centerpb-0 px-0 [background:linear-gradient(0deg,rgba(23,63,129,0)_0%,rgba(21,94,216,0.4)_100%)]">
            {/* 透明点击区域 */}
            <div className="absolute top-0 left-0 w-full h-[48px] flex justify-center mr-6">
              <div
                onClick={() => tabChange('buneng')}
                className="w-[155px] h-full cursor-pointer z-10"
              />
              <div
                onClick={() => tabChange('gongdian')}
                className="w-[155px] h-full cursor-pointer z-10"
              />
            </div>

            {/* 背景图层 */}
            <div
              className={`w-[1247px] h-[48px] transition-opacity duration-300 ${activeTab === 'buneng' ? 'opacity-100' : 'hidden'}`}
              style={{ backgroundImage: `url(/home/<USER>'1226px 48px' }}
            />
            <div
              className={`w-[1226px] h-[48px] transition-opacity duration-300 ${activeTab === 'gongdian' ? 'opacity-100' : 'hidden'}`}
              style={{ backgroundImage: `url(/home/<USER>'1226px 48px' }}
            />

            {/*  闭合、断开按钮  */}
            <div className="absolute top-2 right-1 w-[260px] h-full flex justify-end mr-6">
              <div style={{ width: '100px', height: '26px', padding: 6, marginRight: 6, background: 'linear-gradient(175deg, rgba(0, 135.47, 255, 0.65) 0%, rgba(0, 135.47, 255, 0.08) 100%)', borderRadius: 7.76, outline: '0.78px #39ABFF solid', outlineOffset: '-0.78px', justifyContent: 'center', alignItems: 'center', gap: 3.10, display: 'inline-flex' }}>
                <div onClick={() => operateCommand('connect')} style={{ color: '#6DE875', cursor: 'pointor', fontSize: '12px', fontWeight: '400', wordWrap: 'break-word' }}>断路器闭合</div>
              </div>
              <div style={{ width: '100px', height: '26px', padding: 6, cursor: 'pointor', background: 'linear-gradient(175deg, rgba(0, 135.47, 255, 0.65) 0%, rgba(0, 135.47, 255, 0.08) 100%)', borderRadius: 7.76, outline: '0.78px #39ABFF solid', outlineOffset: '-0.78px', justifyContent: 'center', alignItems: 'center', gap: 3.10, display: 'inline-flex' }}>
                <div onClick={() => operateCommand('disconnect')} style={{ color: '#6DE875', cursor: 'pointor', fontSize: '12px', fontWeight: '400', wordWrap: 'break-word' }}>断路器断开</div>
              </div>
            </div>
          </div>

          {activeTab === 'buneng' ? (
            <BuNengSection
              activeTab={activeTab}
              data={data}
              isConnected={isConnected}
            />
          ) : (
            <GongDianSection
              activeTab={activeTab}
              data={data}
              isConnected={isConnected}
            />
          )}
        </div>

        <Separator className="w-full mt-1 h-[3px] [background:linear-gradient(90deg,rgba(44,223,232,1)_0%,rgba(26,123,219,1)_49%,rgba(219,255,0,1)_100%),linear-gradient(0deg,rgba(217,217,217,1)_0%,rgba(217,217,217,1)_100%)]" />
      </div>
    </div>
  );
};
