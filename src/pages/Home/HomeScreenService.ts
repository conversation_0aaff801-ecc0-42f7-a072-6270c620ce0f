import {
  acGridOutPowerService,
  acGridOutPowerServiceObj,
  acInputPowerService,
  acInputPowerServiceObj,
  acOutputPowerService,
  dcdcInputPowerService,
  dcInputPowerService,
  dcOutputPowerService,
} from './service';

// 充放电状态类型
type Status = {
  isCharging: boolean;
  currentType: 'ac' | 'dc' | null;
  maxPower?: number;
  errorMsg: string;
};

export const HomeScreenService: any = {
  // 开始交流补能
  startACCharging: async (maxPower: number) => {
    // try {
    const res = await acInputPowerService.start(maxPower);
    if (res.result === 'successful') {
      return { success: true, maxPower: res.maxPower };
    }
    // return { success: false, message: res.resultInfo };
    throw new Error(res?.resultInfo || '启动失败');
    // } catch (error) {
    // return { success: false, message: '启动失败' };
    // }
  },
  // 开始并网放电
  startACGridOut: async (maxPower: number) => {
    // try {
    const res = await acGridOutPowerService.start(maxPower);

    if (res.result === 'successful') {
      return { success: true, maxPower: res.maxPower };
    }
    throw new Error(res?.resultInfo || '启动失败');

    //   return { success: false, message: res.resultInfo };
    // } catch (error) {
    //   return { success: false, message: '启动失败' };
    // }
  },

  // 开始直流充电
  startDCCharging: async () => {
    try {
      const res = await dcInputPowerService.start();
      if (res.result === 'successful') {
        return { success: true };
      }
      return { success: false, message: res.resultInfo };
    } catch (error) {
      return { success: false, message: '启动失败' };
    }
  },

  // 开始交流供电
  startACPowerSupply: async () => {
    // try {
    const res = await acOutputPowerService.start();
    if (res.result === 'successful') {
      return { success: true };
    }
    throw new Error(res?.resultInfo || '启动失败');
    //   return { success: false, message: res.resultInfo };
    // } catch (error) {
    //   return { success: false, message: '启动失败' };
    // }
  },

  // 开始直流供电
  startDCPowerSupply: async (params = {}) => {
    try {
      const res = await dcOutputPowerService.start(params);
      if (res.result === 'successful') {
        return { success: true };
      }
      return { success: false, message: res.resultInfo };
    } catch (error) {
      return { success: false, message: '启动失败' };
    }
  },

  // 开始交流放电
  startACDischarging: async () => {
    try {
      const res = await acOutputPowerService.start();
      if (res.result === 'successful') {
        return { success: true };
      }
      return { success: false, message: res.resultInfo };
    } catch (error) {
      return { success: false, message: '启动失败' };
    }
  },

  // 开始直流放电
  startDCDischarging: async (params = {}) => {
    try {
      const res = await dcOutputPowerService.start(params);
      if (res.result === 'successful') {
        return { success: true };
      }
      return { success: false, message: res.resultInfo };
    } catch (error) {
      return { success: false, message: '启动失败' };
    }
  },
  // 开始DCDC供电电
  startDCDCPowerSupply: async (params = {}) => {
    try {
      const res = await dcdcInputPowerService.start(params);
      if (res.result === 'successful') {
        return { success: true };
      }
      return { success: false, message: res.resultInfo };
    } catch (error) {
      return { success: false, message: '启动失败' };
    }
  },

  // 停止当前操作
  stopCurrentOperation: async (
    type: 'ac' | 'acOut' | 'acInput' | 'dc' | 'dcOut' | 'dcInput' | 'dcdc' | 'DCDC' | 'acGridOut',
    isCharging: boolean,
    params = {},
  ) => {
    try {
      let res: any;
      if (type === 'ac') {
        res = isCharging ? await acInputPowerService.stop() : await acOutputPowerService.stop();
      } else if (type === 'dc') {
        res = isCharging
          ? await dcInputPowerService.stop()
          : await dcOutputPowerService.stop(params);
      } else if(type === 'acOut'){
        res = await acOutputPowerService.stop();
      }else if(type === 'acInput'){
        res = await acInputPowerService.stop();
      }else if(type === 'dcOut'){
        res = await dcOutputPowerService.stop();
      }else if(type === 'dcInput'){
        res = await dcInputPowerService.stop();
      }else if (type === 'dcdc' || type === 'DCDC') {
        res = await dcdcInputPowerService.stop(params);
      } else {
        res = await acGridOutPowerService.stop();
      }
      if (res.result === 'successful') {
        return { success: true };
      }
      throw new Error(res?.resultInfo || '停止失败');
      // return { success: false, message: res.resultInfo };
    } catch (error) {
      return { success: false, message: '停止失败' };
    }
  },
  startCurrentOperation: async (
    type: 'acOut' | 'acInput' | 'dcOut' | 'dcInput' | 'DCDC' | 'acGridOut',
    params?: any | Number,
  ) => {
    // try {
      let res: any;
      if (type === 'acOut') {
        res = await acOutputPowerService.start();
      } else if (type === 'acInput') {
        res = await acInputPowerServiceObj.start(params);
      } else if (type === 'dcOut') {
        res = await dcOutputPowerService.start(params);
      } else if (type === 'dcInput') {
        res = await dcInputPowerService.start();
      } else if (type === 'DCDC') {
        res = await dcdcInputPowerService.start(params);
      } else {
        res = await acGridOutPowerServiceObj.start(params);
      }
      if (res.result === 'successful') {
        return { success: true };
      }
      throw new Error(res?.resultInfo || '启动失败');
    // } catch (error) {
    //   return { success: false, message: '启动失败' };
    // }
  },
};
