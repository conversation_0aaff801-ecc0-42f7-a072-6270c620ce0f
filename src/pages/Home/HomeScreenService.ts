import { acInputPowerService, acOutputPowerService, dcInputPowerService, dcOutputPowerService } from './service';

// 充放电状态类型
type Status = {
    isCharging: boolean;
    currentType: 'ac' | 'dc' | null;
    maxPower?: number;
    errorMsg: string;
};

export const HomeScreenService: any = {
    // 开始交流充电
    startACCharging: async (maxPower: number) => {
        try {
            const res = await acInputPowerService.start(maxPower);
            if (res.result === 'successful') {
                return { success: true, maxPower: res.maxPower };
            }
            return { success: false, message: res.resultInfo };
        } catch (error) {
            return { success: false, message: '启动失败' };
        }
    },

    // 开始直流充电
    startDCCharging: async () => {
        try {
            const res = await dcInputPowerService.start();
            if (res.result === 'successful') {
                return { success: true };
            }
            return { success: false, message: res.resultInfo };
        } catch (error) {
            return { success: false, message: '启动失败' };
        }
    },

    // 开始交流供电
    startACPowerSupply: async () => {
        try {
            const res = await acOutputPowerService.start();
            if (res.result === 'successful') {
                return { success: true };
            }
            return { success: false, message: res.resultInfo };
        } catch (error) {
            return { success: false, message: '启动失败' };
        }
    },

    // 开始直流供电
    startDCPowerSupply: async (params = {}) => {
        try {
            const res = await dcOutputPowerService.start(params);
            if (res.result === 'successful') {
                return { success: true };
            }
            return { success: false, message: res.resultInfo };
        } catch (error) {
            return { success: false, message: '启动失败' };
        }
    },

    // 开始交流放电
    startACDischarging: async () => {
        try {
            const res = await acOutputPowerService.start();
            if (res.result === 'successful') {
                return { success: true };
            }
            return { success: false, message: res.resultInfo };
        } catch (error) {
            return { success: false, message: '启动失败' };
        }
    },

    // 开始直流放电
    startDCDischarging: async (params = {}) => {
        try {
            const res = await dcOutputPowerService.start(params);
            if (res.result === 'successful') {
                return { success: true };
            }
            return { success: false, message: res.resultInfo };
        } catch (error) {
            return { success: false, message: '启动失败' };
        }
    },

    // 停止当前操作
    stopCurrentOperation: async (type: 'ac' | 'dc', isCharging: boolean, params = {}) => {
        try {
            let res;
            if (type === 'ac') {
                res = isCharging
                    ? await acInputPowerService.stop()
                    : await acOutputPowerService.stop();
            } else {
                res = isCharging
                    ? await dcInputPowerService.stop()
                    : await dcOutputPowerService.stop(params);
            }

            if (res.result === 'successful') {
                return { success: true };
            }
            return { success: false, message: res.resultInfo };
        } catch (error) {
            return { success: false, message: '停止失败' };
        }
    }
};