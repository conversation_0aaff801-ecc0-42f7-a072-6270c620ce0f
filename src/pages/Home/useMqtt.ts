/* 目前未使用 */
import { useEffect, useState } from 'react';
import mqtt from 'mqtt';
import { mqttUrl } from '@/constants';

export interface SystemData {
    SOC: number;
    ChargePower: number;
    DCInputStatus: number;
    ACInputStatus: number;
    DCOutStatus: number;
    ACOutStatus: number;
    Capacity: number;
    TodayChargePower: number;
    TodayDisChargePower: number;
    AllDisChargePower: number;
    AllChargePower: number;
    AlarmCount: number;
    CurrentSystemStatus: number;
    Devices: Array<{
        Device: {
            Name: string;
            DeviceID: string;
            Type: string;
            Status: string;
            LastStatus: string;
        };
        ItemList: Array<{
            Name: string;
            Description: string;
            Value: number;
        }>;
    }>;
}

const useMqtt = () => {
    const [client, setClient] = useState<mqtt.MqttClient | null>(null);
    const [data, setData] = useState<SystemData | null>(null);
    const [isConnected, setIsConnected] = useState(false);

    useEffect(() => {
        // MQTT连接选项
        const options: any = {
            clientId: `mqtt_client_${Math.random().toString(16).substring(2, 10)}`,
            clean: true,
            connectTimeout: 10000,
            reconnectPeriod: 3000,
            keepalive: 60,
            protocol: 'ws',
            hostname: mqttUrl,
            port: 8083,           // Node-RED WebSocket端口
            path: '/mqtt'           // Node-RED WebSocket 路径
        };

        // 连接MQTT服务器
        const mqttClient = mqtt.connect(options);

        mqttClient.on('connect', () => {
            console.log('MQTT Connected');
            setIsConnected(true);
            // 订阅主题，添加QoS级别
            mqttClient.subscribe('/ems/systemdata', { qos: 2 });
        });

        mqttClient.on('reconnect', () => {
            console.log('MQTT正在重连...');
            setIsConnected(false);
        });

        mqttClient.on('message', (topic, message) => {
            if (topic === '/ems/systemdata') {
                try {
                    const parsedData = JSON.parse(message.toString());
                    console.log('收到MQTT消息:', parsedData);
                    setData(parsedData);
                } catch (error) {
                    console.error('MQTT消息解析错误:', error);
                }
            }
        });

        mqttClient.on('error', (err) => {
            console.error('MQTT错误:', err);
            setIsConnected(false);
        });

        mqttClient.on('close', () => {
            console.log('MQTT连接关闭');
            setIsConnected(false);
        });

        setClient(mqttClient);

        return () => {
            if (mqttClient) {
                console.log('断开MQTT连接');
                mqttClient.unsubscribe('/ems/systemdata');
                mqttClient.end(true);
            }
        };
    }, []);

    const publish = (topic: string, message: string) => {
        if (client && isConnected) {
            client.publish(topic, message);
        }
    };

    return { data, isConnected, publish };
};

export default useMqtt;