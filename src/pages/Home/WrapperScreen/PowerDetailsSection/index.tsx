import { Card, CardContent } from "@/components/ui/card";
import { useMemo } from 'react';
import { isNil } from 'lodash';

export const PowerDetailsSection = ({ data = {} }: any): JSX.Element => {

  const powerReadings = useMemo(() => {
    const list: any = [];
    if (data?.itemList?.length > 0) {
      for (let item of data?.itemList) {

        let unit = item.unit || "";
        if (item.description?.indexOf('电压') !== -1) {
          unit = 'V'
        } else if (item.description?.indexOf('电流') !== -1) {
          unit = 'A'
        }
        list.push({
          id: item.name,
          label: item.description || item.name,
          value: isNil(item.value) ? '--' : item.value,
          unit
        })
      }
    }
    list.sort((a: any, b: any) => a.label.localeCompare(b.label));
    return list;
  }, [data?.itemList]);

  /*   const powerReadings = [
      { id: 1, label: "A相电压", value: "400", unit: "V" },
      { id: 2, label: "A相电压", value: "400", unit: "V" },
      { id: 3, label: "A相电压", value: "400", unit: "V" },
      { id: 4, label: "A相电压", value: "400", unit: "V" },
      { id: 5, label: "A相电压", value: "400", unit: "V" },
      { id: 6, label: "A相电压", value: "400", unit: "V" },
      { id: 7, label: "A相电压", value: "400", unit: "V" },
      { id: 8, label: "A相电压", value: "400", unit: "V" },
    ]; */

  return (
    <Card className="w-full max-w-[430px] rounded-lg bg-gradient-to-t from-[rgba(21,94,216,0.4)] to-transparent border-0">
      <CardContent className="p-4">
        <div className="flex flex-wrap max-h-[160px] overflow-y-auto">
          {powerReadings.map((reading: Record<string, any>, index: number) => (
            <div
              key={reading.id}
              className="flex items-center w-1/2 mb-1"
            >
              <span className="text-[#326fa5] text-xl min-w-[100px]">
                {reading.label}
              </span>
              <div className="flex items-baseline">
                <span className="text-[#c7f2ff] text-xl">
                  &nbsp;{reading.value}
                </span>
                <span className="text-[#c7f2ff] text-xl ml-1">
                  {reading.unit}
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
