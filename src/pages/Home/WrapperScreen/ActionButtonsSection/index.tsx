import { useMemo } from 'react';
import { formatMilliseconds } from "@/utils/formatDate";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from 'antd';
import { noop, isNil } from 'lodash';

// Data for the charging information
/* const chargingData = [
    { label: "电压", value: "400V" },
    { label: "电流", value: "400V" },
    { label: "功率", value: "32kw" },
    { label: "本次补能量", value: "24kwh" },
    { label: "本次补能时长", value: "02:12:13" },
]; */

const unitMap: any = {
    VoltageOutputValue: 'V',
    CurrentOutputValue: 'A',
    OutputEnergy: 'kwh'
}

export const ActionButtonsSection = ({ activeTab = 'buneng', data = {}, dcOperate = noop }: any): JSX.Element => {

    const chargingData = useMemo(() => {
        const list: any = [];
        if (data?.itemList?.length > 0) {
            for (let item of data?.itemList) {
                let value = isNil(item.value) ? '--' : item.value;
                if (item.name === 'CumulativeChargingTime') {
                    value = formatMilliseconds(item.value * 1000);
                }
                const unit = item.unit || unitMap[item.name] || "";
                list.push({
                    id: item.name,
                    label: item.description || item.name,
                    value,
                    unit
                })
            }
        }
        return list;
    }, [data?.itemList]);

    const status = useMemo(() => {
        return data?.device?.status === 'Connect';
    }, [data?.device])

    return (
        <div className="relative w-full max-w-[430px] h-[397px] mt-4 mx-auto">
            <div className="relative h-full">
                <div className="relative w-full h-full">
                    <div className="relative h-full">
                        {/* Background and corner decorations */}
                        <img
                            className="absolute w-[419px] h-[390px] top-[3px] left-1.5"
                            alt="Rectangle"
                            src="/home/<USER>"
                        />
                        <img
                            className="absolute w-[69px] h-[46px] top-[350px] right-0"
                            alt="Bottom right corner"
                            src="/home/<USER>"
                        />
                        <img
                            className="absolute w-[68px] h-[46px] top-0 right-0"
                            alt="Top right corner"
                            src="/home/<USER>"
                        />
                        <img
                            className="absolute w-[69px] h-[46px] top-0 left-0"
                            alt="Top left corner"
                            src="/home/<USER>"
                        />
                        <img
                            className="absolute w-[69px] h-[46px] bottom-0 left-0"
                            alt="Bottom left corner"
                            src="/home/<USER>"
                        />

                        {/* Title bar */}
                        <div className="absolute w-[260px] h-[37px] top-[3px] left-[87px]">
                            <div className="w-[262px] h-[37px]">
                                <div className="relative w-[266px] h-[39px] -top-0.5 left-[-3px]">
                                    <div className="absolute w-[149px] top-[7px] left-[42px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#39abff] text-xl text-center tracking-[0] leading-[normal]">
                                        充电枪
                                    </div>
                                    <img
                                        className="absolute w-[259px] h-[37px] top-0.5 left-1"
                                        alt="Vector"
                                        src="/home/<USER>"
                                    />
                                    <div className="absolute w-[19px] h-[9px] top-[30px] left-3.5">
                                        <img
                                            className="absolute w-[25px] h-[15px] -top-0.5 left-[-3px]"
                                            alt="Group"
                                            src="/home/<USER>"
                                        />
                                    </div>
                                    <div className="absolute w-[19px] h-[9px] top-[30px] left-[232px] rotate-180">
                                        <img
                                            className="absolute w-[25px] h-[15px] -top-1 left-[-3px] -rotate-180"
                                            alt="Group"
                                            src="/home/<USER>"
                                        />
                                    </div>
                                    <img
                                        className="absolute w-[115px] h-px top-0.5 left-[22px]"
                                        alt="Group"
                                        src="/home/<USER>"
                                    />
                                    <img
                                        className="absolute w-[115px] h-px top-[38px] left-[117px]"
                                        alt="Group"
                                        src="/home/<USER>"
                                    />
                                    <img
                                        className="absolute w-[266px] h-[17px] top-0 left-0"
                                        alt="Group"
                                        src="/home/<USER>"
                                    />
                                    <div className="absolute w-[13px] h-[13px] top-[15px] left-[205px] bg-[#00ff50] rounded-[6.74px]" style={{ backgroundColor: status ? '#00ff50' : '#fe4545' }} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main content card */}
                <Card className="absolute top-[52px] left-[30px] w-[371px] border-none bg-transparent">
                    <CardContent className="flex flex-col items-start gap-1.5 pt-3 pb-4 px-3.5 border-[3px] border-solid border-[#39abff]  max-h-[320px]">
                        {/* Header with status */}
                        <div className="flex w-full items-center gap-1 mb-4">
                            <div className="inline-flex items-center gap-2.5">
                                <img
                                    className="w-5 h-[18.86px]"
                                    alt="Union"
                                    src="/home/<USER>"
                                />
                            </div>

                            <div className="flex items-center gap-2.5 flex-1">
                                <div className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#39abff] text-lg leading-[22.0px]">
                                    充电枪
                                </div>
                            </div>

                            <div className="inline-flex items-center gap-1">
                                <div className={`w-1.5 h-1.5 bg-[#fe4545] rounded-[3px]`} style={{ backgroundColor: status ? '#00ff50' : '#858585' }} />
                                <Badge className="bg-transparent p-0">
                                    <span className={`[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[${status ? '#00ff50' : '#fe4545'}] text-xl leading-[16.0px]`}>
                                        {status ? "已连接" : "离线"}
                                    </span>
                                </Badge>
                            </div>
                        </div>

                        {/* Charging information */}
                        <div className={`flex flex-col w-full  pt-3 ${activeTab === 'gongdian' ? 'gap-2' : 'gap-6'} flex-1  ${activeTab === 'gongdian' ? 'max-h-[180px] overflow-y-auto' : 'max-h-[280px]'}`}>
                            {chargingData.map((item: any, index: number) => (
                                <div key={index} className="flex items-start w-full">
                                    <div className={`flex items-end gap-[4.76px] flex-1`}>
                                        <div className="flex items-center">
                                            <div className="[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#326fa5] text-xl leading-[22.2px]">
                                                {item.label}
                                            </div>
                                        </div>
                                        <div className="inline-flex items-end">
                                            <div className="inline-flex items-center">
                                                <div className="[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#c7f2ff] text-[19.1px] leading-[22.2px]">
                                                    {item.value}{item.unit}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        {/*  */}
                        {activeTab === 'gongdian' && <Button
                            className="w-full h-12 mt-4 rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important]  [&:hover,&:focus,&:active]:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
                            onClick={dcOperate}
                        >
                            <span className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#81dc4a] text-xl leading-[18px] whitespace-nowrap tracking-[0]" style={{ color: data.DCOutStatus === 2 ? '#fe4545' : '' }}>
                                {data.DCOutStatus === 2 ? '停止供电' : '开始供电'}
                            </span>
                        </Button>}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};
