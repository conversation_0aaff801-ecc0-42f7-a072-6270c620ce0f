import React, { useState, useMemo, useEffect } from 'react';

import type { StepProps, StepsProps } from 'antd';
import { Modal, Spin, Steps } from 'antd';
import { ShrinkOutlined } from '@ant-design/icons';
import styles from './index.module.less';

const customLargeDot: StepsProps['progressDot'] = (dot, { status, index }) => {
  return (
    <div style={{ width: 28, height: 28, marginTop: '-8px' }}>
      {/* @ts-ignore */}
      {status === 'alarm' ? (
        <>
          <div
            style={{
              width: 28,
              height: 28,
              marginTop: '-8px',
              backgroundColor: '#FFCA1C',
              borderRadius: '50%',
            }}
            className="alarm-dot"
          ></div>
        </>
      ) : (
        dot
      )}
    </div>
  );
};

interface ExtendedStepProps extends StepProps {
  id: number;
}

interface ProgressStatusProps {
  current?: { id: any; title?: any; [key: string]: any } | null;
  stepData?: ExtendedStepProps[] | undefined;
  title?: any;
  openModal?: boolean;
  delay?: number;
  border?: boolean;
}

const ProgressStatus: React.FC<ProgressStatusProps> = ({
  current,
  stepData = [],
  title,
  openModal = false,
  border = true,
}) => {
  const [largeModal, setLargeModal] = useState<boolean>(openModal);
  const [currentInfo, setCurrentInfo] = useState<any>(current);

  useEffect(() => {
    setLargeModal(openModal);
  }, [openModal]);

  const currentIndex = useMemo(() => {
    if (stepData?.length <= 0 || !Array.isArray(stepData)) {
      return undefined;
    }
    if (!current || Object.keys(current).length === 0) {
      // 查找第一个status=wait的索引
      const firstWaitIndex = stepData?.findIndex((item) => item.status === 'wait');
      // 获取前一个item（处理边界情况）
      const index = firstWaitIndex > 0 ? firstWaitIndex - 1 : 0;
      const _current = firstWaitIndex > 0 ? stepData?.[index] : null;
      setCurrentInfo(_current);
      return index;
    } else {
      setCurrentInfo(current);
      return (
        stepData?.findIndex((item) => item.id === current?.id && item.title === current?.title) || 0
      );
    }
  }, [stepData, current]);

  const minStepData = Array.isArray(stepData)
    ? stepData?.map((item) => {
        return {
          ...item,
          title: '',
          description: '',
          status: item.status,
        };
      })
    : [];
  return (
    <div className="h-full min-h-[48px] w-full">
      <div
        className={`relative flex h-full w-full cursor-pointer flex-col items-start justify-center rounded-[10px] px-[12px] ${border ? 'border-[3px] border-solid border-[#39ABFF] bg-gradient-to-b from-[rgba(0,135,255,0.65)] to-[rgba(0,135,255,0.08)]' : ''}`}
        onClick={() => {
          setLargeModal(true);
        }}
      >
        {minStepData.length <= 0 ? (
          <div className="flex h-full w-full items-center justify-center">
            <div className="pointer-events-none">
              <Spin spinning={minStepData.length <= 0} />
            </div>
          </div>
        ) : (
          <>
            <span className="absolute top-0 text-[16px] leading-4">{currentInfo?.title || ''}</span>
            <div
              className="flex w-full items-center justify-start"
              style={{ width: `calc(100% + 100%/${minStepData?.length} - 10px)` }}
            >
              <Steps
                current={currentIndex}
                // progressDot={customDot}
                // @ts-ignore
                items={minStepData}
                className={`cst ${styles['mini-step']}`}
                //   size="small"
                type="inline"
              />
            </div>
          </>
        )}
      </div>
      <Modal
        className={styles['launched-modal']}
        title={title || ''}
        width={1130}
        centered
        open={largeModal}
        onCancel={() => {
          setLargeModal(false);
        }}
        onOk={() => {
          setLargeModal(false);
        }}
        footer={null}
        closeIcon={<ShrinkOutlined className={styles['close-icon']} />}
      >
        <Spin spinning={stepData.length <= 0}>
          <div className="flex h-[300px] items-center justify-center overflow-auto">
            <Steps
              className="cst"
              current={currentIndex}
              progressDot={customLargeDot}
              items={stepData}
            />
          </div>
        </Spin>
      </Modal>
    </div>
  );
};

export default ProgressStatus;
