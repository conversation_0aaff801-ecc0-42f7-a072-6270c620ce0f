.mini-step {
  width: 100% !important;
  :global {
    .ant-steps-item {
      flex: 1 !important;
    }

    .ant-steps-item-tail:after {
      height: 4px !important;
    }

    .ant-steps-item-icon .ant-steps-icon .ant-steps-icon-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      top: -2px;
    }
    .ant-steps-item:first-child .ant-steps-item-tail {
      width: 100% !important;
    }
    .ant-steps-item:last-child .ant-steps-item-tail {
      width: 0 !important;
    }
    .ant-steps-item-tail {
      margin-inline-start: calc(-50% + 12px) !important;
      width: 100%;
      left: 50%;
    }
    .ant-steps-item-icon {
      margin-inline-start: 0 !important;
    }
    .ant-steps-item-process {
      .ant-steps-item-icon .ant-steps-icon .ant-steps-icon-dot {
        box-shadow: 0 0 0 2px #0eda2c4d !important;
      }
    }
  }
}
.launched-modal {
  .close-icon {
    color: #e3f8ff;
    font-size: 32px;
  }
  :global {
    .ant-modal-content {
      background-color: #143f8c;
      border: 1px solid #0060be;
      border-radius: 4px;
      box-shadow: 0px 0px 10px rgba(0, 174, 255, 0.3);
      .ant-steps-item-title {
        white-space: nowrap;
      }
    }
    .ant-modal-header {
      background-color: #143f8c;
      .ant-modal-title {
        color: #e3f8ff;
        font-size: 24px;
      }
    }
    .ant-steps-item-tail:after {
      height: 8px !important;
      margin-inline-start: 22px !important;
    }
    /* 自定义步骤条样式 */
    // .ant-steps-item {
    //   position: relative;
    // }

    /* 偶数步骤标签居上 */
    .ant-steps-item:nth-child(even) .ant-steps-item-content {
      position: absolute;
      bottom: 12px;
      text-align: center;
      display: flex;
      flex-direction: column-reverse;
      margin-bottom: 12px;
      margin-top: 0;
    }
    /* 奇数步骤标签居下 */
    .ant-steps-item:nth-child(odd) .ant-steps-item-content {
      position: absolute;
      top: 12px;
      text-align: center;
      display: flex;
      flex-direction: column;
      margin-bottom: 0px;
      margin-top: 12px;
    }
    .ant-steps-item-container .ant-steps-item-content .ant-steps-item-description {
      white-space: pre-wrap;
      word-break: break-all;
      word-wrap: break-word;
    }
    .ant-steps-item.ant-steps-item-finish,
    .ant-steps-item.ant-steps-item-process {
      .ant-steps-item-content .ant-steps-item-description {
        color: #e3f8ff;
        font-size: 18px;
        white-space: pre-wrap;
        word-break: break-all;
        word-wrap: break-word;
        width: 100%;
        min-width: 200px;
      }
    }
    .ant-steps-item.ant-steps-item-error .ant-steps-item-content .ant-steps-item-description {
      color: #ff4545;
      font-size: 18px;
    }
    .ant-steps-item-alarm .ant-steps-item-content .ant-steps-item-description {
      color: #ffca1c;
      font-size: 18px;
    }
    .ant-steps .ant-steps-item:last-child {
      flex: 0.5;
      right: 0;
    }
  }
}
