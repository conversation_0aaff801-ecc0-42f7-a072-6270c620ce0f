import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { formatNullValue } from '@/utils';
import { EchartsProgress } from '@/components/EchartsProgress';
import { toFixed } from '@/utils/number';
import { projectType } from '@/constants';

interface StatusSectionProps {
  data?: any;
}

// 系统状态文本映射
const systemStatusMap: any = {
  1: '关机',
  2: '待机',
  3: '补能',
  4: '供电',
  5: '补能并供电',
  6: '供电',
  7: '供电',
};

const getGunTypeByProject = () => {
  if (projectType === 'hangzhou') {
    return 'ChargeGun';
  }
  return 'DCChargingStation';
};

const gunType = getGunTypeByProject();

export const StatusSection = ({ data = {} }: StatusSectionProps): JSX.Element => {
  // Data for energy metrics
  const energyMetrics = [
    { label: '装机容量', value: formatNullValue(data?.Capacity), unit: 'kwh' },
    { label: '今日补能量', value: formatNullValue(data?.TodayChargePower), unit: 'kwh' },
    { label: '今日供电量', value: formatNullValue(data?.TodayDisChargePower), unit: 'kwh' },
    { label: '累计补能量', value: formatNullValue(data?.AllChargePower), unit: 'kwh' },
    { label: '累计供电量', value: formatNullValue(data?.AllDisChargePower), unit: 'kwh' },
  ];

  // 获取设备在线数量
  const getConnectCount = (type: string) => {
    return (
      data?.Devices?.filter?.(
        (d: Record<string, any>) => d?.device?.type === type && d?.device?.status === 'Connect',
      ).length || 0
    );
  };

  const temperContrs = data?.Devices?.filter?.(
    (d: Record<string, any>) => d?.device?.type === 'TemperContr',
  );
  const tempOnline = temperContrs?.some?.(
    (d: Record<string, any>) => d?.device?.status === 'Connect',
  );

  const statusIndicators = [
    {
      label: 'BMS',
      value: String(getConnectCount('BMS')),
      total: String(
        data?.Devices?.filter?.((d: Record<string, any>) => d?.device?.type === 'BMS').length || 0,
      ),
    },
    {
      label: 'PCS',
      value: String(getConnectCount('PCS')),
      total: String(
        data?.Devices?.filter?.((d: Record<string, any>) => d?.device?.type === 'PCS').length || 0,
      ),
    },
    {
      label: '水冷机组',
      hideDivider: true,
      value: tempOnline ? '在线' : '离线',
      status: tempOnline ? 'online' : 'offline',
    },
    {
      label: '充电桩',
      value: String(getConnectCount(gunType)),
      total: String(
        data?.Devices?.filter?.((d: Record<string, any>) => d?.device?.type === gunType).length ||
          0,
      ),
    },
    {
      label: '告警',
      value: String(data?.AlarmCount || 0),
      status: 'alarm',
    },
  ];

  // Common gradient styles
  const gradientBg =
    '[background:linear-gradient(180deg,rgba(0,135,255,0)_0%,rgba(0,135,255,0.26)_100%)]';
  const gradientLine =
    '[background:linear-gradient(90deg,rgba(44,223,232,1)_0%,rgba(26,123,219,1)_49%,rgba(219,255,0,1)_100%),linear-gradient(0deg,rgba(217,217,217,1)_0%,rgba(217,217,217,1)_100%)]';
  const headerBg =
    '[background:linear-gradient(0deg,rgba(21,94,216,0.4)_0%,rgba(23,63,129,0)_100%)]';

  return (
    <div className="flex w-full items-center gap-2">
      {/* System Status Card */}
      <Card className="w-[130px] border-0 bg-transparent">
        <CardContent className={`p-0 ${gradientBg} h-[146px]`}>
          <div className="relative h-full w-full">
            <div className={`flex items-end gap-1 rounded-[10px] px-5 py-3 ${headerBg}`}>
              <div className="mt-[-2.00px] flex flex-1 grow items-center gap-2.5">
                <div className="mt-[-1.00px] w-fit whitespace-nowrap text-xl font-medium leading-[22px] tracking-[0] text-white [font-family:'PingFang_SC-Medium',Helvetica]">
                  系统状态
                </div>
              </div>
              <div className="relative inline-flex flex-[0_0_auto] items-end gap-1">
                <div className="relative h-4 w-[27px]" />
              </div>
            </div>

            <div
              className="absolute left-[25px] top-[78px] w-[76px] text-[32px] font-medium leading-[22px] tracking-[0] text-[#67bc34] [font-family:'PingFang_SC-Medium',Helvetica]"
              style={{ fontSize: data?.CurrentSystemStatus == 5 ? '22px' : '32px' }}
            >
              {systemStatusMap[data?.CurrentSystemStatus] || '--'}
            </div>
          </div>
        </CardContent>
        <Separator className={`h-[3px] ${gradientLine}`} />
      </Card>

      {/* Remaining Power Card */}
      <Card className="h-[142px] w-[282px] border-0 bg-transparent">
        <CardContent className={`h-[142px] p-0 ${gradientBg}`}>
          <div className={`flex h-11 items-end gap-1 rounded-[10px] px-5 py-3 ${headerBg}`}>
            <div className="mt-[-2.00px] flex flex-1 grow items-center gap-2.5">
              <div className="mt-[-1.00px] w-fit whitespace-nowrap text-xl font-medium leading-[22px] tracking-[0] text-white [font-family:'PingFang_SC-Medium',Helvetica]">
                剩余电量
              </div>
            </div>

            <div className="mt-[-12.00px] inline-flex items-end gap-1">
              <div className="inline-flex items-center gap-2.5">
                <div className="mt-[-1.00px] w-fit whitespace-nowrap text-[32px] font-medium leading-8 tracking-[0] text-white [font-family:'PingFang_SC-Medium',Helvetica]">
                  {formatNullValue(data?.ChargePower)}
                </div>
              </div>

              <div className="inline-flex items-center gap-2.5">
                <div className="mt-[-1.00px] w-fit whitespace-nowrap text-sm font-medium leading-4 tracking-[0] text-[#7b8fb6] [font-family:'PingFang_SC-Medium',Helvetica]">
                  kwh
                </div>
              </div>
            </div>
          </div>

          <div className="flex w-full flex-col items-center gap-2 p-2">
            <div className="flex w-full items-end gap-0.5">
              <div className="inline-flex items-center gap-2.5">
                <div className="mt-[-1.00px] w-fit whitespace-nowrap text-lg font-medium leading-5 tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]">
                  SoC
                </div>
              </div>

              <div className="inline-flex flex-col items-start gap-2">
                <div className="inline-flex items-end gap-0.5">
                  <div className="mt-[-1.00px] w-fit whitespace-nowrap text-center text-[28px] font-normal leading-[30px] tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Semibold',Helvetica]">
                    {formatNullValue(toFixed(data?.SOC))}
                  </div>

                  <div className="relative w-3.5 text-sm font-medium leading-[22px] tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]">
                    %
                  </div>
                </div>
              </div>
            </div>

            <div
              className="flex w-full items-center justify-between gap-[3px] overflow-hidden"
              style={{ height: '46px' }}
            >
              <div className="w-full flex-shrink-0">
                <EchartsProgress value={Number(data?.SOC) || 0} height={20} />
              </div>
            </div>
          </div>
        </CardContent>
        <Separator className={`h-[3px] ${gradientLine}`} />
      </Card>

      {/* Energy Metrics and Status Indicators */}
      <div className="flex flex-1 flex-col gap-2">
        {/* Energy Metrics Row */}
        <div className="flex w-full items-center gap-1">
          {energyMetrics.map((metric, index) => (
            <Card key={index} className="w-40 border-0 bg-transparent">
              <CardContent
                className={`flex flex-col items-center justify-center gap-2 px-3 py-[9px] ${gradientBg}`}
              >
                <div className="inline-flex flex-col items-start gap-2">
                  <div className="inline-flex items-end gap-0.5">
                    <div className="mt-[-1.00px] w-fit whitespace-nowrap text-center text-2xl font-normal leading-[22px] tracking-[0] text-white [font-family:'PingFang_SC-Semibold',Helvetica]">
                      {metric.value}
                    </div>
                    <div className="w-fit whitespace-nowrap text-xs font-medium leading-[14px] tracking-[0] text-[#7b8fb6] [font-family:'PingFang_SC-Medium',Helvetica]">
                      {metric.unit}
                    </div>
                  </div>
                </div>
                <div className="inline-flex items-center gap-2.5">
                  <div className="mt-[-1.00px] w-fit whitespace-nowrap text-base font-medium leading-[22px] tracking-[0] text-[#7b8fb6] [font-family:'PingFang_SC-Medium',Helvetica]">
                    {metric.label}
                  </div>
                </div>
              </CardContent>
              <Separator className={`h-[3px] ${gradientLine}`} />
            </Card>
          ))}
        </div>

        {/* Status Indicators Row */}
        <div className="flex w-full items-center gap-1">
          {statusIndicators.map((indicator, index) => (
            <Card key={index} className="w-40 border-0 bg-transparent">
              <CardContent
                className={`flex flex-col items-center justify-center gap-1 px-3 py-[9px] ${gradientBg}`}
              >
                <div className="inline-flex flex-col items-start gap-2">
                  <div className="inline-flex items-end gap-0.5">
                    {indicator.status === 'online' ? (
                      <div className="mt-[-1.00px] w-fit whitespace-nowrap text-center text-xl font-normal leading-[22px] tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Semibold',Helvetica]">
                        在线
                      </div>
                    ) : indicator.status === 'alarm' ? (
                      <div className="mt-[-1.00px] w-fit whitespace-nowrap text-center text-2xl font-normal leading-[22px] tracking-[0] text-[#fe4545] [font-family:'PingFang_SC-Semibold',Helvetica]">
                        {indicator.value}
                      </div>
                    ) : (
                      <div className="mt-[-1.00px] w-fit whitespace-nowrap text-center text-2xl font-normal leading-[22px] tracking-[0] text-transparent [font-family:'PingFang_SC-Regular',Helvetica]">
                        <span className="text-white">{indicator.value}</span>
                        <span className="text-[#7b8fb6]">
                          {indicator.hideDivider ? '' : '/'}
                          {indicator.total}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="inline-flex items-center gap-2.5">
                  <div className="mt-[-1.00px] w-fit whitespace-nowrap text-base font-medium leading-[22px] tracking-[0] text-[#7b8fb6] [font-family:'PingFang_SC-Medium',Helvetica]">
                    {indicator.label}
                  </div>
                </div>
              </CardContent>
              <Separator className={`h-[3px] ${gradientLine}`} />
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
