import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { formatNullValue } from '@/utils';
import { EchartsProgress } from '@/components/EchartsProgress';
import { toFixed } from '@/utils/number';
import { projectType } from '@/constants';

interface StatusSectionProps {
  data?: any;
}

// 系统状态文本映射
const systemStatusMap: any = {
  1: '关机',
  2: '待机',
  3: '补能',
  4: '供电',
  5: '补能并供电'
};

const getGunTypeByProject = () => {
  if (projectType === 'hangzhou') {
    return 'ChargeGun';
  }
  return 'DCChargingStation'
}

const gunType = getGunTypeByProject();

export const StatusSection = ({ data = {} }: StatusSectionProps): JSX.Element => {
  // Data for energy metrics
  const energyMetrics = [
    { label: "装机容量", value: formatNullValue(data?.Capacity), unit: "kwh" },
    { label: "今日补能量", value: formatNullValue(data?.TodayChargePower), unit: "kwh" },
    { label: "今日供电量", value: formatNullValue(data?.TodayDisChargePower), unit: "kwh" },
    { label: "累计补能量", value: formatNullValue(data?.AllChargePower), unit: "kwh" },
    { label: "累计供电量", value: formatNullValue(data?.AllDisChargePower), unit: "kwh" },
  ];

  // 获取设备在线数量
  const getConnectCount = (type: string) => {
    return data?.Devices?.filter?.((d: Record<string, any>) => d?.device?.type === type && d?.device?.status === 'Connect').length || 0;
  };

  const temperContrs = data?.Devices?.filter?.((d: Record<string, any>) => d?.device?.type === 'TemperContr');
  const tempOnline = temperContrs?.some?.((d: Record<string, any>) => d?.device?.status === 'Connect');


  const statusIndicators = [
    {
      label: "BMS",
      value: String(getConnectCount('BMS')),
      total: String(data?.Devices?.filter?.((d: Record<string, any>) => d?.device?.type === 'BMS').length || 0)
    },
    {
      label: "PCS",
      value: String(getConnectCount('PCS')),
      total: String(data?.Devices?.filter?.((d: Record<string, any>) => d?.device?.type === 'PCS').length || 0)
    },
    {
      label: "水冷机组",
      hideDivider: true,
      value: tempOnline ? "在线" : "离线",
      status: tempOnline ? "online" : "offline"
    },
    {
      label: "充电桩",
      value: String(getConnectCount(gunType)),
      total: String(data?.Devices?.filter?.((d: Record<string, any>) => d?.device?.type === gunType).length || 0)
    },
    {
      label: "告警",
      value: String(data?.AlarmCount || 0),
      status: "alarm"
    },
  ];

  // Common gradient styles
  const gradientBg =
    "[background:linear-gradient(180deg,rgba(0,135,255,0)_0%,rgba(0,135,255,0.26)_100%)]";
  const gradientLine =
    "[background:linear-gradient(90deg,rgba(44,223,232,1)_0%,rgba(26,123,219,1)_49%,rgba(219,255,0,1)_100%),linear-gradient(0deg,rgba(217,217,217,1)_0%,rgba(217,217,217,1)_100%)]";
  const headerBg =
    "[background:linear-gradient(0deg,rgba(21,94,216,0.4)_0%,rgba(23,63,129,0)_100%)]";

  return (
    <div className="flex items-center gap-2 w-full">
      {/* System Status Card */}
      <Card className="w-[130px] border-0 bg-transparent">
        <CardContent className={`p-0 ${gradientBg} h-[146px]`}>
          <div className="relative w-full h-full">
            <div
              className={`flex items-end gap-1 px-5 py-3 rounded-[10px] ${headerBg}`}
            >
              <div className="flex items-center gap-2.5 flex-1 grow mt-[-2.00px]">
                <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-white text-xl leading-[22px] whitespace-nowrap tracking-[0]">
                  系统状态
                </div>
              </div>
              <div className="gap-1 inline-flex items-end relative flex-[0_0_auto]">
                <div className="relative w-[27px] h-4" />
              </div>
            </div>

            <div className="absolute w-[76px] top-[78px] left-[25px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#67bc34] text-[32px] tracking-[0] leading-[22px]"
              style={{ fontSize: data?.CurrentSystemStatus == 5 ? '22px' : '32px' }}
            >
              {systemStatusMap[data?.CurrentSystemStatus] || '--'}
            </div>
          </div>
        </CardContent>
        <Separator className={`h-[3px] ${gradientLine}`} />
      </Card>

      {/* Remaining Power Card */}
      <Card className="w-[282px] border-0 bg-transparent h-[142px]">
        <CardContent className={`p-0  h-[142px] ${gradientBg}`}>
          <div
            className={`flex h-11 items-end gap-1 px-5 py-3 rounded-[10px] ${headerBg}`}
          >
            <div className="flex items-center gap-2.5 flex-1 grow mt-[-2.00px]">
              <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-white text-xl leading-[22px] whitespace-nowrap tracking-[0]">
                剩余电量
              </div>
            </div>

            <div className="gap-1 mt-[-12.00px] inline-flex items-end">
              <div className="inline-flex items-center gap-2.5">
                <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-white text-[32px] leading-8 whitespace-nowrap tracking-[0]">
                  {formatNullValue(data?.ChargePower)}
                </div>
              </div>

              <div className="inline-flex items-center gap-2.5">
                <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#7b8fb6] text-sm leading-4 whitespace-nowrap tracking-[0]">
                  kwh
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2 w-full flex-col p-2">
            <div className="flex items-end gap-0.5 w-full">
              <div className="inline-flex items-center gap-2.5">
                <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#3ae353] text-lg leading-5 whitespace-nowrap tracking-[0]">
                  SoC
                </div>
              </div>

              <div className="inline-flex flex-col items-start gap-2">
                <div className="inline-flex items-end gap-0.5">
                  <div className="[font-family:'PingFang_SC-Semibold',Helvetica] text-[#3ae353] text-[28px] leading-[30px] w-fit mt-[-1.00px] font-normal text-center tracking-[0] whitespace-nowrap">
                    {formatNullValue(toFixed(data?.SOC))}
                  </div>

                  <div className="relative w-3.5 [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#3ae353] text-sm tracking-[0] leading-[22px]">
                    %
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between gap-[3px] w-full overflow-hidden"
              style={{ height: '46px' }}>
              <div className="flex-shrink-0 w-full">
                <EchartsProgress value={Number(data?.SOC) || 0} height={20} />
              </div>
            </div>
          </div>
        </CardContent>
        <Separator className={`h-[3px] ${gradientLine}`} />
      </Card>

      {/* Energy Metrics and Status Indicators */}
      <div className="flex flex-col gap-2 flex-1">
        {/* Energy Metrics Row */}
        <div className="flex items-center gap-1 w-full">
          {energyMetrics.map((metric, index) => (
            <Card key={index} className="w-40 border-0 bg-transparent">
              <CardContent
                className={`flex flex-col items-center justify-center gap-2 px-3 py-[9px] ${gradientBg}`}
              >
                <div className="inline-flex flex-col items-start gap-2">
                  <div className="inline-flex items-end gap-0.5">
                    <div className="[font-family:'PingFang_SC-Semibold',Helvetica] text-white text-2xl leading-[22px] w-fit mt-[-1.00px] font-normal text-center tracking-[0] whitespace-nowrap">
                      {metric.value}
                    </div>
                    <div className="w-fit [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#7b8fb6] text-xs leading-[14px] whitespace-nowrap tracking-[0]">
                      {metric.unit}
                    </div>
                  </div>
                </div>
                <div className="inline-flex items-center gap-2.5">
                  <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#7b8fb6] text-base leading-[22px] whitespace-nowrap tracking-[0]">
                    {metric.label}
                  </div>
                </div>
              </CardContent>
              <Separator className={`h-[3px] ${gradientLine}`} />
            </Card>
          ))}
        </div>

        {/* Status Indicators Row */}
        <div className="flex items-center gap-1 w-full">
          {statusIndicators.map((indicator, index) => (
            <Card key={index} className="w-40 border-0 bg-transparent">
              <CardContent
                className={`flex flex-col items-center justify-center gap-1 px-3 py-[9px] ${gradientBg}`}
              >
                <div className="inline-flex flex-col items-start gap-2">
                  <div className="inline-flex items-end gap-0.5">
                    {indicator.status === "online" ? (
                      <div className="[font-family:'PingFang_SC-Semibold',Helvetica] text-[#3ae353] text-xl leading-[22px] w-fit mt-[-1.00px] font-normal text-center tracking-[0] whitespace-nowrap">
                        在线
                      </div>
                    ) : indicator.status === "alarm" ? (
                      <div className="[font-family:'PingFang_SC-Semibold',Helvetica] text-[#fe4545] text-2xl leading-[22px] w-fit mt-[-1.00px] font-normal text-center tracking-[0] whitespace-nowrap">
                        {indicator.value}
                      </div>
                    ) : (
                      <div className="[font-family:'PingFang_SC-Regular',Helvetica] text-transparent text-2xl leading-[22px] w-fit mt-[-1.00px] font-normal text-center tracking-[0] whitespace-nowrap">
                        <span className="text-white">{indicator.value}</span>
                        <span className="text-[#7b8fb6]">
                          {indicator.hideDivider ? '' : '/'}{indicator.total}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="inline-flex items-center gap-2.5">
                  <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#7b8fb6] text-base leading-[22px] whitespace-nowrap tracking-[0]">
                    {indicator.label}
                  </div>
                </div>
              </CardContent>
              <Separator className={`h-[3px] ${gradientLine}`} />
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
