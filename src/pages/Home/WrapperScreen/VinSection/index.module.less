.custom-modal {
  background: #143f8c;
  color: #fff;
  top: 20px;

  :global {

    .ant-form,
    .ant-form-item {
      color: #fff;
    }

    .ant-modal-body {
      font-size: 16px;
      max-height: 450px;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .ant-modal-header {
      margin-bottom: 32px;
    }

    .ant-modal-content,
    .ant-modal-header,
    .ant-select-outlined.ant-select-status-error:not(.ant-select-customize-input) .ant-select-selector {
      background: #143f8c;
      background-color: #143f8c;
      box-shadow: none;
      color: #fff;
    }

    .ant-modal-title,
    .ant-modal-close {
      color: #fff;
      font-size: 20px;

      svg {
        height: 20px;
        width: 20px;
      }
    }

    .ant-modal-body .ant-select-selector {
      gap: 8px;
      opacity: 1;
      padding-right: 8px;
      padding-left: 8px;
      border-radius: 6px;
      border-width: 1px;
      background: linear-gradient(173.82deg,
          rgba(0, 135, 255, 0.125) 25.25%,
          rgba(0, 135, 255, 0.26) 93.59%);
      border: 1px solid #39abff;
      color: #fff;

      .ant-select-selection-placeholder {
        color: rgb(255 255 255 / 25%);
      }
    }

    .ant-modal-body .ant-select-dropdown {
      width: calc(100% - 10px) !important;
    }

    .ant-modal-footer {
      display: flex;
      justify-content: space-around;
      margin-top: 48px;

      .ant-btn-color-primary {
        background: #0478ec;
      }

      button {
        flex: 1;
      }
    }
  }
}

.custom-check {
  :global {
    .ant-checkbox-inner {
      border-radius: 50%;
      background-color: transparent;
      width: 20px;
      height: 20px;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: #6de875 !important;
    }
  }
}

.custom-autoSelect {
  :global {
    .ant-select-arrow {
      height: 48px;
      padding: 0;
      top: 0;
      right: -25px;
      margin: 0;
      width: 95px;
      padding-left: 8px;
    }
  }
}