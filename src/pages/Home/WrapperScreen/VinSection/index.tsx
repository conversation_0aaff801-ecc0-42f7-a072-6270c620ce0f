import { Button, Checkbox, Form, message, Modal } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import VinItem from './vinItem';
import { httpGet, httpPut } from '@/shared/http';

const VinSection = ({
  VINCode2 = false,
  border = true,
}: {
  VINCode2?: boolean;
  border?: boolean;
}) => {
  const [vinSetVisible, setVinSetVisible] = useState<boolean>(false);
  const [vinChecked, setVinChecked] = useState<Boolean | undefined>(false);
  const [currentKeyboard, setCurrentKeyboard] = useState('');
  const [form] = Form.useForm();
  const [options, setOptions] = useState<any>([]);

  // 可下拉选择的vin码数据
  useEffect(() => {
    const getVinCode = async () => {
      try {
        let res = await httpGet('/api/configs/vin/get');
        const _options: { [key: string]: { value: string }[] } = {};
        const initData: any = {};
        Object.keys(res?.data || {}).forEach((key) => {
          if (res.data[key]?.vinItems) {
            _options[key] = res.data[key].vinItems.map((item: any) => ({ value: item.code }));
          }
          if (!!res?.data?.[key]?.currentSelectCode) {
            initData[key] = res.data[key].currentSelectCode;
          }
        });
        if (Object.keys(initData).length > 0) {
          form.setFieldsValue(initData);
          setVinChecked(Object.keys(initData)?.length > 0);
        }
        setOptions(_options);
      } catch (error) {}
    };
    getVinCode();
  }, [vinSetVisible]);
  const validator = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    const pattern = /^[A-Z0-9]{17}$/;
    if (pattern.test(value)) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('请输入17位大写字母或数字'));
  };
  const handleClose = () => {
    form.resetFields();
    setVinSetVisible(false);
  };

  const handleOk = () => {
    try {
      form.validateFields().then(async (values) => {
        const vins: any = [];
        // 无值传空字符串
        Object.keys(values)?.map((key) => {
          vins.push({ key: key, value: values?.[key] || '' });
        });
        await httpPut(`/api/configs/vin/save/code`, { items: vins });
        message.success('vin码设置成功');
        // 存在有效值设置认为设置了VIN码
        setVinChecked(vins.some((item: any) => item.value && item.value.trim() !== ''));
        setVinSetVisible(false);
      });
    } catch (error) {}
  };

  return (
    <>
      <div
        className="to-blue-500/12 absolute left-4 flex h-10 w-[110px] items-center justify-center gap-2.5 rounded-lg border-2 border-blue-400 from-blue-500/75 text-xl opacity-100"
        style={{
          border: border ? '2px solid #60a5fa' : 'none',
          backgroundImage: border
            ? 'linear-gradient(to bottom right, var(--tw-gradient-stops))'
            : 'transparent',
        }}
        onClick={() => {
          setVinSetVisible(true);
        }}
      >
        vin码
        <Checkbox checked={!!vinChecked} className={styles['custom-check']}></Checkbox>
      </div>

      {/* vin码设置弹窗 */}
      <Modal
        className={styles['custom-modal']}
        open={vinSetVisible}
        title="vin码"
        onCancel={handleClose}
        maskClosable={false}
        footer={[
          <Button key="cancel" type="default" ghost size="large" onClick={handleClose}>
            取消
          </Button>,
          <Button
            type="primary"
            key="submit"
            size="large"
            // loading={loading}
            onClick={handleOk}
          >
            确定
          </Button>,
        ]}
        destroyOnClose
      >
        <Form form={form} onFinish={handleOk}>
          <Form.Item name={'VINCode1'} validateTrigger="onBlur" rules={[{ validator }]}>
            <VinItem
              key="VINCode1-stable"
              keyValue="VinItem1"
              label="充电座1"
              options={options?.['VINCode1'] || []}
              currentKeyboard={currentKeyboard}
              setCurrentKeyboard={setCurrentKeyboard}
            />
          </Form.Item>
          {VINCode2 && (
            <Form.Item name={'VINCode2'} validateTrigger="onBlur" rules={[{ validator }]}>
              <VinItem
                key="VINCode2-stable"
                keyValue="VinItem2"
                label="充电座2"
                options={options?.['VINCode2'] || []}
                currentKeyboard={currentKeyboard}
                setCurrentKeyboard={setCurrentKeyboard}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </>
  );
};

export default VinSection;
