import { useState, useRef, useEffect } from 'react';
import { AutoComplete, Button } from 'antd';
import { CloseOutlined, DownOutlined } from '@ant-design/icons';
import Keyboard from 'react-simple-keyboard';
import styles from './index.module.less';

interface VinItemProps {
  keyValue: string;
  value?: string;
  onChange?: (value: string) => void;
  options?: any[];
  label?: React.ReactNode;
  currentKeyboard?: string;
  setCurrentKeyboard?: (keyboard: string) => void;
  noKeyBoard?: boolean;
}
const VinItem = ({
  keyValue,
  value = '',
  onChange,
  options: _options = [],
  label,
  currentKeyboard = '',
  setCurrentKeyboard = () => {},
  noKeyBoard = false,
}: VinItemProps) => {
  const [vin, setVin] = useState(value);
  const [inputValue, setInputValue] = useState(value);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [selectOpen, setSelectOpen] = useState(false);
  const isSelectingRef = useRef(false); // 用于标记是否正在选择

  const keyboardRef = useRef<any>(null);

  // 同步外部 value 到内部
  useEffect(() => {
    setInputValue(value);
    keyboardRef.current?.setInput(value);
  }, [value]);

  // 触发外部 onChange
  const handleChange = (val: string) => {
    const limitedVal = val.slice(0, 17);
    setInputValue(limitedVal);
    // setSelectOpen(false);
    onChange?.(limitedVal);
  };
  // 避免键盘重叠
  useEffect(() => {
    if (showKeyboard) {
      setCurrentKeyboard(keyValue);
    } else {
      setCurrentKeyboard('');
    }
  }, [keyValue, showKeyboard]);

  useEffect(() => {
    // 只在其他组件激活时关闭当前键盘，不影响下拉框
    if (!!currentKeyboard && currentKeyboard !== keyValue && showKeyboard) {
      setShowKeyboard(false);
    }
  }, [currentKeyboard, keyValue, showKeyboard]);

  return (
    <>
      {label && <p className="color-[#fff] mb-5 text-xl">{label}</p>}
      <p className="color-[#fff] relative mb-5 text-xl">
        当前VIN码: {vin}
        {vin && (
          <span
            className="absolute right-0 top-[-20px] cursor-pointer p-5"
            onClick={() => {
              handleChange('');
              setVin('');
              keyboardRef.current?.clearInput();
            }}
          >
            <img className="h-[30px] w-[30px]" alt="clear" src="/home/<USER>" />
          </span>
        )}
      </p>

      <AutoComplete
        size="large"
        value={inputValue}
        options={_options}
        open={selectOpen}
        placeholder="请输入VIN码"
        onFocus={(e) => {
          // webkit环境下优化：确保focus行为正确触发
          if (isSelectingRef.current) {
            isSelectingRef.current = false;
            // e.target.blur(); // 立即移除focus状态
            return;
          }
          setShowKeyboard(true);
          setSelectOpen(true);
        }}
        onSelect={(val) => {
          // webkit环境下关键修复：确保所有实例的选择事件正常触发
          isSelectingRef.current = true;

          // 立即更新内部状态
          setVin(val);
          setInputValue(val);
          setSelectOpen(false);
          handleChange(val);

          // 关键：立即重置选择标记，确保下次交互正常
          setTimeout(() => {
            isSelectingRef.current = false;
          }, 100);
        }}
        onChange={(val) => {
          setInputValue(val);
          onChange?.(val);
        }}
        className={styles['custom-autoSelect']}
        suffixIcon={
          <div
            className="flex h-full w-full cursor-pointer items-center justify-center"
            style={{ minWidth: '52px', minHeight: '52px' }}
            onMouseDown={(e) => {
              e.preventDefault(); // 阻止onFocus触发
              e.stopPropagation();
              setSelectOpen((prev) => !prev);
            }}
          >
            <DownOutlined className="flex h-full w-full cursor-pointer items-center justify-center" />
          </div>
        }
        getPopupContainer={(triggerNode) => {
          const container = triggerNode.parentElement;
          if (container) {
            container.style.position = 'relative';
          }
          return container || document.body;
        }}
      />

      {showKeyboard && !noKeyBoard && (
        <div className="fixed bottom-0 left-0 z-[999] w-full bg-[#1B53B7]">
          <div className="flex justify-end p-2">
            <Button
              size="large"
              type="text"
              onClick={() => {
                setShowKeyboard(false);
                keyboardRef.current?.clearInput(); // 清除键盘状态
                keyboardRef.current?.destroy(); // 销毁键盘实例
              }}
              style={{ color: '#fff', fontSize: '22px' }}
            >
              取消
            </Button>
          </div>

          <Keyboard
            keyboardRef={(r) => (keyboardRef.current = r)}
            input={inputValue}
            onChange={handleChange}
            onKeyPress={(button) => {
              if (button === '{bksp}') {
                const newVal = inputValue.slice(0, -1);
                handleChange(newVal);
              }
            }}
            useMouseEvents={true}
            theme="hg-theme-default custom-keyboard"
            layout={{
              default: [
                '1 2 3 4 5 6 7 8 9 0',
                'Q W E R T Y U I O P',
                'A S D F G H J K L',
                'Z X C V B N M',
                '{bksp} {space} {enter}',
              ],
            }}
            display={{
              '{space}': '空格',
              '{bksp}': '删除',
              '{enter}': '确认',
            }}
          />
        </div>
      )}
    </>
  );
};

export default VinItem;
