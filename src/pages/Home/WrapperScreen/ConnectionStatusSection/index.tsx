import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

export const ConnectionStatusSection = (): JSX.Element => {
  // Data for the connection status tabs
  const connectionOptions = [
    { id: "charging", label: "补 能", active: true },
    { id: "power", label: "供 电", active: false },
  ];

  return (
    <div className="flex w-full items-center justify-center pt-4 pb-0 px-0 [background:linear-gradient(0deg,rgba(23,63,129,0)_0%,rgba(21,94,216,0.4)_100%)]">
      <Tabs defaultValue="charging" className="w-fit">
        <TabsList className="p-0 bg-transparent border-none">
          {connectionOptions.map((option) => (
            <TabsTrigger
              key={option.id}
              value={option.id}
              className={`
                px-12 py-2.5 h-auto
                [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[22px] leading-6 tracking-[0]
                ${option.id === "charging"
                  ? "rounded-[10px_0px_0px_10px] text-[#6ce775] [background:linear-gradient(175deg,rgba(0,135,255,0.75)_0%,rgba(0,135,255,0.12)_100%)]"
                  : "rounded-[0px_10px_10px_0px] text-[#7d98ce] [background:linear-gradient(180deg,rgba(0,135,255,0.13)_0%,rgba(0,135,255,0.26)_100%)]"
                }
                data-[state=active]:bg-transparent data-[state=active]:shadow-none
              `}
            >
              {option.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
};
