import { Card, CardContent } from '@/components/ui/card';
// import { Input } from "@/components/ui/input";
import { Button, Input } from 'antd';
import { useEffect, useState, useRef, useCallback } from 'react';
import Keyboard from 'react-simple-keyboard';
import 'react-simple-keyboard/build/css/index.css';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';

interface Props {
  value: number;
  label?: string;
  maxPower?: number;
  disabled?: boolean;
  onPowerChange?: (power: number) => void;
  style?: React.CSSProperties;
  negativeAllowed?: boolean;//补能功率是否允许输入负数
  type?:any[]; //用于区分并网放电和交流补能
}

export const ChargingStatusSection = ({
  value,
  label='补能功率(KW)',
  maxPower = 120,
  onPowerChange,
  disabled,
  style = {},
  negativeAllowed=true,
  type=[],
}: Props): JSX.Element => {
  const [power, setPower] = useState<any>(value);
  const [error, setError] = useState<string>('');
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  
// 查找功率所属的范围
const getDescriptByPower=(powerValue:number, typeArray:any[])=> {
  if(typeArray.length<=0){
    return "补能功率";
  }

  // 遍历所有范围配置
  for (const item of typeArray) {
    // 检查功率是否在当前范围的[Min, Max]区间内
    if (powerValue >= item.Min && powerValue <= item.Max) {
      return item.Descript;
    }
  }
  // 如果没有找到匹配的范围，返回提示信息
  return "补能功率";
}
  useEffect(() => {
    setPower?.(value);
  }, [value]);

  // 默认值在value中初始化
  // useEffect(() => {
  //   onPowerChange?.(maxPower);
  // }, [maxPower]);

  const handlePowerChange = useCallback(
    (value: string) => {
      const numValue = Number(value);
      setPower(value);
      if (isNaN(numValue)) {
        setError('请输入有效的数字');
        return;
      }
      if (numValue > maxPower) {
        setError(`最大功率不能超过 ${maxPower}KW`);
        setPower(value);
        // onPowerChange?.(maxPower);
        return;
      }
      if (!negativeAllowed && numValue <= 0) {
        setError('功率必须大于0');
        return;
      }
      if (numValue === 0) {
        setError('功率不允许为0');
        return;
      }
      setError('');

      // onPowerChange?.(numValue);
    },
    [maxPower],
  );

  const handleInputFocus = () => {
    if (!disabled) {
      setShowKeyboard(true);
      keyboardRef.current?.setInput('');
    }
  };

  const handleKeyboardInput = useCallback(
    (input: string) => {
      handlePowerChange(input);
    },
    [handlePowerChange],
  );

  const handleConfirm = () => {
    if (error) {
      return;
    }
    setConfirmLoading(false);
    setConfirmVisible(false);
    onPowerChange?.(Number(power));
  };

  const handleCancel = () => {
    setConfirmLoading(false);
    setConfirmVisible(false);
    onPowerChange?.(value);
    setPower(value);
    setShowKeyboard(false);
  };
  return (
    <Card
      className="cst w-full rounded-[9px] border-[3px] border-solid border-[#39abff] p-4 shadow-[none] box-shadow-[none]" 

      style={style}
    >
      <div className=" flex items-center gap-1 *:h-full" >
        <div className="flex flex-1 items-center gap-2.5 h-full">
          <h3 className="text-lg font-semibold leading-[22px] tracking-[0] text-[#39abff] w-[120px]">
            {label}
          </h3>
        </div>
      </div>

      <CardContent className="flex flex-col gap-2 p-0 flex-1 h-full">
        <div className="flex items-center gap-2 h-full">
          <div className="flex-1 h-full min-h-[38px]">
            <Input
              className="cst border-input file:text-foreground placeholder:text-muted-foreground focus-visible:ring-ring flex h-full w-full rounded-md border border-solid border-[#9ebdfa] px-3 py-1 text-base text-[#e3f8ff] shadow-sm transition-colors [background:transparent!important] file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:text-[#e3f8ff] disabled:opacity-50 md:text-sm"
              value={power}
              onFocus={handleInputFocus}
              disabled={disabled}
              allowClear
              placeholder="请输入补能功率"
              onChange={(e) => {
                const v = e.target.value;
                handlePowerChange(String(v));
              }}
              style={{
                color: '#e3f8ff',
              }}
            />
          </div>
          {/* <Button
            ghost
            icon={<img src='/home/<USER>' title="setting"/>}
            className="h-14 w-[60px] border-none"
          /> */}
        </div>
        {error && <div className="z-1 mt-[-8px] text-sm text-red-500">{error}</div>}
      </CardContent>

      {showKeyboard && (
        <div
          className="absolute left-[-20%] top-[-20%] z-50 w-[600px] shadow-[0_4px_12px_rgba(0,0,0,0.25)] rounded-[10px] bg-[#1B53B7]"
          onTouchStart={(e) => e.stopPropagation()}
          onTouchMove={(e) => e.preventDefault()}
        >
          <div className="flex justify-end p-2">
            <Button
              size="large"
              type="text"
              // icon={<CloseOutlined />}
              onClick={handleCancel}
              style={{ color: '#fff',fontSize:'22px' }}
            >
              取消
            </Button>
          </div>
          <Keyboard
            keyboardRef={(r) => (keyboardRef.current = r)}
            layout={{
              default: ['1 2 3', '4 5 6', '7 8 9', '. 0 {bksp}', negativeAllowed ? '- {enter}' : '{enter}'],
            }}
            display={{
              '{bksp}': '删除',
              '{enter}': '确认',
            }}
            onChange={handleKeyboardInput}
            onKeyPress={(button) => {
              // 处理删除按钮
              if (button === '{bksp}') {
                let newInput = power?.toString()?.slice(0, -1) || '';
                handlePowerChange(newInput);
              }
              if (button === '{enter}') {
                // 回车保存设置 无接口 仅关闭键盘
                if(error){
                  return;
                }
                setShowKeyboard(false);
                setConfirmVisible(true);
              }
            }}
            theme="hg-theme-default custom-keyboard"
            // useTouchEvents={true}              // 启用触摸事件支持
            useMouseEvents={true} // 启用鼠标事件
            disableCaretPositioning={true} // 禁用光标定位，避免触摸冲突
          />
        </div>
      )}
      {/* 操作确认 */}
      <ConfirmModal
        open={confirmVisible}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        confirmLoading={confirmLoading}
        content={`是否确认设置${getDescriptByPower(Number(power), type)}为 ${power}KW？`}
      />
    </Card>
  );
};
