import { Card, CardContent } from "@/components/ui/card";
// import { Input } from "@/components/ui/input";
import { Button, Input } from 'antd';
import { useEffect, useState, useRef, useCallback } from 'react';
import Keyboard from 'react-simple-keyboard';
import 'react-simple-keyboard/build/css/index.css';
import { CloseOutlined } from '@ant-design/icons';

interface Props {
  value: number;
  maxPower?: number;
  disabled?: boolean;
  onPowerChange?: (power: number) => void;
  style?: React.CSSProperties;
}

export const ChargingStatusSection = ({ value, maxPower = 120, onPowerChange, disabled, style = {} }: Props): JSX.Element => {
  const [power, setPower] = useState<any>(value);
  const [error, setError] = useState<string>('');
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);

  useEffect(() => {
    setPower?.(value);
  }, [value]);

  useEffect(() => {
    onPowerChange?.(maxPower);
  }, [maxPower]);

  const handlePowerChange = useCallback((value: string) => {
    const numValue = Number(value);
    setPower(value);
    if (isNaN(numValue)) {
      setError('请输入有效的数字');
      return;
    }
    if (numValue > maxPower) {
      setError(`最大功率不能超过 ${maxPower}KW`);
      setPower(value);
      onPowerChange?.(maxPower);
      return;
    }
    if (numValue <= 0) {
      setError('功率必须大于0');
      return;
    }
    setError('');

    onPowerChange?.(numValue);
  }, [maxPower]);

  const handleInputFocus = () => {
    if (!disabled) {
      setShowKeyboard(true);
      keyboardRef.current?.setInput("");
    }
  };

  const handleKeyboardInput = useCallback((input: string) => {
    handlePowerChange(input);
  }, [handlePowerChange]);


  return (
    <Card className="cst w-full rounded-[9px] border-[3px] border-solid border-[#39abff] p-4" style={style}>
      <div className="flex items-center gap-1 mb-3">
        <div className="flex items-center gap-2.5 flex-1">
          <h3 className="font-semibold text-[#39abff] text-lg leading-[22px] tracking-[0]">
            补能功率(KW)
          </h3>
        </div>
      </div>

      <CardContent className="p-0 flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <div className="flex-1">
            <Input
              className="cst flex h-9 w-full rounded-md border border-input [background:transparent!important] px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm border border-solid border-[#9ebdfa] text-[#e3f8ff] disabled:text-[#e3f8ff] h-[38px]"
              value={power}
              onFocus={handleInputFocus}
              disabled={disabled}
              allowClear
              placeholder="请输入补能功率"
              onChange={(e) => {
                const v = e.target.value;
                handlePowerChange(String(v));
              }}
              style={{
                color: '#e3f8ff',
              }}
            />
          </div>
          <Button
            ghost
            icon={<img src='/home/<USER>' title="setting"/>}
            className="h-14 w-[60px] border-none"
          />
        </div>
          {error && <div className="z-1 mt-[-18px] text-sm text-red-500">{error}</div>}
      </CardContent>

      {showKeyboard && (
        <div className="absolute top-0 left-0 w-full z-50 bg-gray-100"
          onTouchStart={(e) => e.stopPropagation()}
          onTouchMove={(e) => e.preventDefault()}
        >
          <div className="flex justify-end p-2">
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => setShowKeyboard(false)}
              style={{ color: '#000' }}
            />
          </div>
          <Keyboard
            keyboardRef={(r) => (keyboardRef.current = r)}
            layout={{
              default: ["1 2 3", "4 5 6", "7 8 9", ". 0 {bksp}"]
            }}
            onChange={handleKeyboardInput}
            onKeyPress={(button) => { 
               // 处理删除按钮
              if (button === '{bksp}') {
                let newInput = power?.toString()?.slice(0, -1)||'';
                handlePowerChange(newInput);
              }
            }}
            theme="hg-theme-default custom-keyboard"
            useTouchEvents={true}              // 启用触摸事件支持
            // useMouseEvents={true}              // 启用鼠标事件
            disableCaretPositioning={true}     // 禁用光标定位，避免触摸冲突
          />
        </div>
      )}
    </Card>
  );
};
