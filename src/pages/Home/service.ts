// https://doc.weixin.qq.com/doc/w3_ANsABwaoAD8NFby4x8RTGm7Z9vz97?scode=AMgArwcGAAYKmEmA1CANsABwaoAD8&version=4.1.10.6015&platform=win

import { httpGet, httpPost } from '@/shared/http';

// 接口返回类型定义
interface PowerResponse {
    type: 'acinput' | 'dcinput' | 'acout' | 'dcout';
    result: 'successful' | 'failed';
    maxPower?: number;  // 成功时返回
    resultInfo: string;
}

// 交流补能相关接口
export const acInputPowerService = {
    // 启动交流补能
    start: (userInputPara: number): Promise<PowerResponse> => {
        return httpPost('/EMS/AC/InputPower/Start', { userInputPara });
    },
    // 停止
    stop: (): Promise<PowerResponse> => {
        return httpPost('/EMS/AC/InputPower/Stop', {}, { timeout: 360000 });  // 6分钟 = 360000ms
    }
};

// 交流放电相关接口
export const acOutputPowerService = {
    // 启动交流放电
    start: (): Promise<PowerResponse> => {
        return httpPost('/EMS/AC/OutPower/Start');
    },
    // 停止
    stop: (): Promise<PowerResponse> => {
        return httpPost('/EMS/AC/OutPower/Stop', {}, { timeout: 360000 });  // 6分钟 = 360000ms
    }
};

// 直流放电相关接口
export const dcOutputPowerService = {
    // 启动直流放电
    start: (params = {}): Promise<PowerResponse> => {
        return httpPost('/EMS/DC/OutPower/Start', params);
    },
    // 停止
    stop: (params = {}): Promise<PowerResponse> => {
        return httpPost('/EMS/DC/OutPower/Stop', params);
    }
};

// 直流补能相关接口
export const dcInputPowerService = {
    // 启动直流补能
    start: (): Promise<PowerResponse> => {
        return httpPost('/EMS/DC/InputPower/Start');
    },
    // 停止
    stop: (): Promise<PowerResponse> => {
        return httpPost('/EMS/DC/InputPower/Stop');
    }
};