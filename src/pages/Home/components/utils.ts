/**
 * 替换apiParams中的变量占位符为实际值
 * @param apiParams 包含变量占位符的参数对象，可以是任意类型
 * @param values 包含实际值的对象，键为变量名，值为实际值
 * @returns 替换后的参数对象，保持原始类型结构
 * @throws {Error} 当values参数不是有效对象时
 */
export function replaceApiParams<T extends Record<string, any>, V extends Record<string, any>>(
  apiParams: T,
  values: V
): T {
  // 递归处理值的函数
  const processValue = (value: any): any => {
    // 处理对象类型
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      const result: Record<string, any> = {};
      for (const key in value) {
        if (Object.prototype.hasOwnProperty.call(value, key)) {
          result[key] = processValue(value[key]);
        }
      }
      return result;
    }

    // 处理数组类型
    if (Array.isArray(value)) {
      return value.map(item => processValue(item));
    }

    // 处理字符串类型，替换占位符
    if (typeof value === 'string') {
      return value.replace(/\{(\w+)\}/g, (match, key) => {
        return Object.prototype.hasOwnProperty.call(values, key) ? values[key] : match;
      });
    }

    // 其他类型直接返回
    return value;
  };

  // 深拷贝原始对象并处理
//   return processValue(JSON.parse(JSON.stringify(apiParams))) as T;
  try {
    // 深拷贝
    const clonedParams =
      typeof structuredClone === 'function'
        ? structuredClone(apiParams)
        : JSON.parse(JSON.stringify(apiParams));

    return processValue(clonedParams);
  } catch (error) {
    console.error('参数处理失败:', error);
    throw new Error('无法处理提供的参数对象，请检查数据格式');
  }
}
