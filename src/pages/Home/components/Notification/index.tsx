import { useEffect, useState } from 'react';
import './index.less';
import alarmIcon from '@/assets/alarm-icon.png'
import Icon from '@ant-design/icons';
interface IProps {
  show?: boolean;
  data?: any;
  dataKey?: string;
}
export function Notification(props: IProps) {
  const [show, setShow] = useState(props.show);
  const getContent = () => {
    if (!props.data || !props.dataKey) return '';
    
    return props.data[props.dataKey];
  }
  useEffect(() => {
    console.log('props.data', props);
    setShow(props.data && props.dataKey);
  }, [props.dataKey, JSON.stringify(props.data)]);
  if (!show) return null;

  return (
    <div className="notification-content w-full">
      <div><img src={alarmIcon} className='w-[26px] h-[26px] mr-[10px]' /></div>
      <div>报警内容：{getContent()}</div>
    </div>
  );
}
