import { Radio, Spin } from 'antd';
import clsx from 'clsx';
import { ReactNode, useEffect, useState } from 'react';
import styles from '../CustomHome/index.module.less';
import OutputCard from './RightCard';
import OptButton from './OptButton';
import VinSection from '../WrapperScreen/VinSection';
import LeftCard from './LeftCard';
import { InfoCircleFilled } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { typeMap, useGlobalError } from '@/contexts/GlobalErrorContext';
import { Notification } from './Notification';
import { WakeButton } from './WakeButton';

interface ContentLayoutProps {
  ViewType: string;
  children?: ReactNode;
  config?: any;
  data?: any;
}
const ContentLayout = (props: ContentLayoutProps) => {
  const { ViewType, children, config, data = {} } = props;
  const navigate = useNavigate();
  const { alarm } = useGlobalError();

  const [type, setType] = useState<string | null>('Input');
  const [options, setOptions] = useState<any[]>([]);
  const [detailData, setDetailData] = useState<any>({});

  useEffect(() => {
    const options: any[] = [];
    if (ViewType === 'Switch') {
      Object.keys(config).forEach((item: string) => {
        // @ts-ignore
        if (
          !!config[item] &&
          typeof config[item] === 'object' &&
          config[item].Label &&
          config[item].Content &&
          config[item].ViewType !== 'Hide'
        ) {
          // @ts-ignore
          options.push({ value: item, label: config[item].Label });
        }
      });
      setType(options?.[0]?.value);
      setOptions(options);
    }
  }, [ViewType, config]);

  useEffect(() => {
    // const deviceIDs = getAllDeviceIDs(config);
    if (data.Devices?.length > 0) {
      // 电池组
      for (const d of data.Devices) {
        const formattedData: any = {};
        if (d.itemList && Array.isArray(d.itemList)) {
          d.itemList.forEach((item: any) => {
            if (item.name) {
              formattedData[item.name] = item.value;
            }
          });
        }
        setDetailData((prev: any) => ({ ...prev, [d.device.deviceID]: formattedData }));
      }
    }
  }, [data?.Devices]);

  const handleTypeChange = (value: string) => {
    setType(value);
  };
  const goRunLogs = () => {
    navigate('/system-maintenance/run-logs');
  };
  const renderContent = (type: string = 'Input', contentConfig: any) => {
    if (!contentConfig?.[type]) return <Spin spinning={true} className="h-full" />;
    const mainContent = contentConfig?.[type]?.Content || {};
    const keys = Object.keys(mainContent);
    if (!keys || !keys.length) return <Spin spinning={true} className="h-full" />;
    let leftContent: any = {};
    let rightContent: any = {};
    keys?.forEach((item) => {
      if (mainContent[item]?.ViewType === 'Hide') return;
      if (Array.isArray(mainContent[item])) {
        // rightContent.push(mainContent[item]);
        rightContent[item] = mainContent[item];
      } else {
        leftContent[item] = mainContent[item];
      }
    });

    let rightContentClass = 'text-[20px]';
    return (
      <div className="flex h-full flex-1 flex-col">
        <div className="mt-3 flex w-full flex-1 flex-row">
          <div className="flex h-full flex-1 flex-row gap-4">
            {/* 左侧内容 */}
            {Object.keys(leftContent).map((item) => {
              // @ts-ignore
              if (leftContent?.[item].ViewType === 'Hide' || !leftContent?.[item]?.[`${item}Group`])
                return null;
              const minRightContent =
                leftContent?.[item]?.[`${item}Group`] &&
                leftContent?.[item]?.[`${item}Group`]?.length > 3;
              {
                /* 左侧超过3个右侧字号缩小 */
              }
              rightContentClass = minRightContent ? 'text-[15px]' : rightContentClass;
              return (
                <>
                  <div
                    key={`${type}-${item}`}
                    className={clsx(
                      'relative flex h-full flex-col gap-4 p-4',
                      styles['card-bg'],
                      styles['left-content'],
                      `${leftContent?.[item]?.[`${item}Group`] && leftContent?.[item]?.[`${item}Group`].length > 1 ? 'flex-[2]' : 'flex-[1]'}`,
                    )}
                  >
                    <div className="relative">
                      <div className="title flex w-full items-center justify-center text-2xl font-semibold text-[#3BE354]">
                        {leftContent?.[item]?.Label}
                        {/* @ts-ignore */}
                        {alarm?.[typeMap[item]] ? (
                          <InfoCircleFilled
                            style={{ color: '#FFC337', marginLeft: '8px' }}
                            onClick={goRunLogs}
                          />
                        ) : null}
                      </div>
                      {leftContent?.[item].VIN && (
                        <div className="absolute right-[110px] top-0">
                          <VinSection VINCode2={true} border={false} />
                        </div>
                      )}
                    </div>
                    <div className="mt-[-2px] flex h-full flex-row gap-4">
                      {leftContent?.[item]?.[`${item}Group`]?.map((card: any) => (
                        <div key={`${type}-${card.Label}`} className="h-full flex-1">
                          <LeftCard
                            data={data}
                            cardInfoConfig={card}
                            type={item}
                            detailData={detailData}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              );
            })}
            {/* 右侧内容 */}
            {Object.keys(rightContent).map((item) => {
              const _item = rightContent[item];
              return _item.map((card: any) => {
                if (card.ViewType === 'Hide') return null;
                return (
                  <div
                    key={`${type}-${card.Label}`}
                    className={clsx(
                      'relative flex flex-1 p-4',
                      styles['card-bg'],
                      styles['right-content'],
                    )}
                  >
                    <OutputCard
                      cardInfoConfig={card}
                      className={rightContentClass}
                      type={item}
                      data={data}
                      detailData={detailData}
                      goRunLogs={goRunLogs}
                    />
                  </div>
                );
              });
            })}
          </div>
        </div>
        {(contentConfig?.[type]?.ButtonItems?.Items || [])?.length > 0 && (
          <div className="mb-[20px] mt-[0px] h-[52px] min-h-[52px] w-full">
            {contentConfig?.[type].ButtonItems.Items.map((item: any) => (
              <OptButton
                data={data}
                key={item.Name}
                // @ts-ignore
                type={contentConfig?.[type].ButtonItems?.optionType || 'acOut'}
                progressStatusProps={null}
                withProgress={contentConfig?.[type]?.StatusFlag}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderMainContent = (type: any) => {
    if (ViewType !== 'Switch') {
      // 左边数组 右边对象
      const newConfig: any = { Input: {} };
      let leftContent: any = {};
      let rightContent: any = {};
      let Content: any = {};
      Object.keys(config)?.forEach((item) => {
        if (config[item]?.Content && config[item]?.ViewType !== 'Hide') {
          Content = { ...Content, ...config[item].Content };
        }
        // if (Array.isArray(config?.Input?.[item])) {
        //   rightContent[item] = config?.Input?.[item];
        // } else {
        //   leftContent[item] = config?.Input?.[item];
        // }
      });

      Object.keys(Content)?.forEach((item) => {
        if (Content[item].ViewType === 'Hide') {
          return;
        }
        if (Array.isArray(Content?.[item])) {
          rightContent[item] = Content[item];
        } else {
          leftContent[item] = Content[item];
        }
      });
      newConfig.Input = {
        Content: {
          ...leftContent,
          ...rightContent,
        },
      };
      return renderContent('Input', newConfig);
    } else {
      return renderContent(type, config);
    }
  };

  return (
    <div className="main-content flex h-[550px] flex-col">
      {ViewType === 'Switch' && (
        <div className="mt-[4px] w-full bg-[linear-gradient(360deg,rgba(23,63,129,0)_0%,rgba(21,94,216,0.4)_100%)] pt-3">
          <Radio.Group
            onChange={(e) => handleTypeChange(e.target.value)}
            defaultValue={type}
            value={type}
            className={`custom-radio-group flex w-full justify-center`}
          >
            {(options || [])?.map((item) => (
              <Radio.Button key={item?.value} value={item?.value} className={'custom-radio'}>
                {item?.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
      )}
      <div className='flex gap-4 items-center mt-[12px]'>
        <Notification {...(config?.Notification || {})} data={data}/>
        <WakeButton />
      </div>
      {renderMainContent(type)}
    </div>
  );
};

export default ContentLayout;
