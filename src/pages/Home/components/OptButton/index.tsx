import { useCallback, useEffect, useMemo, useState } from 'react';
import ProgressStatus from '../../WrapperScreen/ProgessStatus';
import { Button, message } from 'antd';
import {
  errorTypeMap,
  typeStatusMap,
  useGlobalError,
  typeMap,
} from '@/contexts/GlobalErrorContext';
import { formateProgressData, getCurrent, mergeProgressData } from '../../useProgress';
import storage from '@/shared/storage';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../../HomeScreenService';
import { replaceApiParams } from '../utils';
import { httpPost } from '@/shared/http';
/**
 * 带启动流程和报错信息的按钮
 * 供电 补能适用
 */
interface OptButtonProps {
  data: any;
  btnText?: string;
  disabled?: boolean;
  isAlarm?: boolean;
  isPrepare?: boolean;
  withProgress?: boolean;
  progressStatusProps?: any;
  onClick?: () => void;
  type: keyof typeof errorTypeMap;
  openProgressModal?: boolean;
  writeValue?: any; //写入的值
  apiParams?: any; //接口传参
}

// 非运行中的状态
const notProgressStatusMap = [0, 2, undefined];
//  0-初始状态 1-启动中 2-供电启动成功（不允许停止，继续显示启动流程）3-停止中  4-启动成功（允许停止，不再显示启动流程）
//  仅交流补能和并网放电
const notProgressStatusACMap = [0, 4, undefined];
const notProgressStatus = {
  acOut: notProgressStatusMap,
  dcInput: notProgressStatusMap,
  acInput: notProgressStatusACMap,
  acGridOut: notProgressStatusACMap,
  DCDC: notProgressStatusMap,
  dcOut: notProgressStatusMap,
};
const OptButton = ({
  btnText,
  disabled = false,
  data,
  withProgress = true,
  progressStatusProps,
  type,
  writeValue,
  apiParams,
}: OptButtonProps) => {
  const { err, setError, setAlarm, progressAll } = useGlobalError();
  const [progressData, setProgressData] = useState<any>([]);
  const [currentProgressNode, setCurrentProgressNode] = useState<any>({});
  const currentProgressInfo = progressAll[type];
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmContent, setConfirmContent] = useState('');
  const [confirmFuncParams, setConfirmFuncParams] = useState<any>({});
  const [openProgressModal, setOpenProgressModal] = useState(false);
  const { systemStatus } = useDeviceStore();

  const clearErrorMsg = (type: string) => {
    // @ts-ignore
    setError((prev: any) => ({ ...prev, [type]: '', content: '' }));
  };
  const refresStorageProgressData = (type: string, data: any) => {
    storage.set(`progress${type}`, data);
  };
  const processData = (type: string, pdata: any) => {
    const _progressData = formateProgressData(pdata);
    const currentNode = getCurrent(pdata);
    setCurrentProgressNode(currentNode);
    setProgressData((prev: any[]) => {
      const merged = mergeProgressData(prev, _progressData);
      storage.set(type, merged);
      refresStorageProgressData(type, merged);
      return merged;
    });
  };
  // 当前按钮是否处于进行中的状态
  const isPrepare = useMemo(() => {
    const currentStatus = data[typeStatusMap[type]];
    if (currentStatus !== undefined && notProgressStatus[type].includes(currentStatus)) {
      setProgressData([]);
      refresStorageProgressData(type, []);
    }
    return !notProgressStatus[type]?.includes(currentStatus);
  }, [data[typeStatusMap[type]], type]);

  const optType = useMemo(() => {
    const currentStatus = data[typeStatusMap[type]];
    const voltage = data.voltage || 0;
    console.log(voltage, 'voltage')
    if (voltage > 650) {
      return 'stop';
    }
    if (['acInput', 'acGridOut'].includes(type) && currentStatus === 4) {
      return 'stop';
    }
    if ([0, undefined].includes(currentStatus)) {
      return 'start';
    }
    if (currentStatus === 2) {
      return 'stop';
    }
    return 'stop';
  }, [data[typeStatusMap[type]], data.voltage, type]);

  // 判断是否可以开始启动
  const canStart = useMemo(() => {
    // 仅待机可以开始启动
    if (data.voltage < 650) return true;
    if (systemStatus == 2) {
      return !!systemStatus;
    } else {
      return false;
    }
  }, [systemStatus]);

  // 判断是否可以结束启动
  const canStop = useMemo(() => {
    const currentStatus = data[typeStatusMap[type]];
    if (data.voltage > 650) return true;
    // 仅当前状态为完全启动完成状态可以结束
    const canStop = notProgressStatus[type]?.filter((item) => !!item).includes(currentStatus);
    return canStop;
  }, [data[typeStatusMap[type]], data.voltage, type]);

  useEffect(() => {
    if (currentProgressInfo && Object.keys(currentProgressInfo).length === 0) return;
    processData(type, currentProgressInfo);
  }, [currentProgressInfo]);

  const handleClick = () => {
    // 根据当前卡片类型和类型状态判断是开始\结束
    // 1、当前无法开始 非待机状态
    // 当前无法结束 非运行中状态
    console.log('====start', optType, canStart, canStop)
    if ((optType === 'start' && !canStart) || (optType === 'stop' && !canStop)) {
      message.error(`当前状态无法${errorTypeMap[type]}`);
      return;
    }
    // 替换配置中的api参数占位符
    const res = replaceApiParams(apiParams, writeValue);
    // 用户写的值报存再value中，其他参数为others
    let params: any = {
      optType: optType, //启动/停止
      type: type, // 交流补能 交流供电 直流补能 直流供电 DCDC ACGridOut 等
      params: res,
    };
    // TODO DCDC 类型需要额外的参数 加入配置文件，待联调
    // if (type === 'DCDC') {
    //   params.others = {
    //     type: 'dcout',
    //     deviceId: 'PCS1',
    //     gunId: 'A',
    //   };
    // }

    setConfirmVisible(true);
    setConfirmFuncParams(params);
  };

  useEffect(() => {
    if (optType === 'stop') {
      setConfirmContent(`确定是否停止${errorTypeMap[type]}吗？`);
    } else {
      setConfirmContent(`确定是否开始${errorTypeMap[type]}吗？`);
    }
  }, [optType, type]);

  // 清理操作状态
  const cleanupOperation = () => {
    setConfirmLoading(false);
    setConfirmVisible(false);
  };

  // 停止补能、供电等

  const handleStopOperation = async (type: keyof typeof errorTypeMap) => {
    try {
      const msgId = Date.now().toString();
      const result = await httpPost('/api/RootInterface/WriteCommand', {
        devices: [
          {
            msgId: msgId, // 消息ID
            deviceCode: 'BMS1', // 设备ID
            addresses: [
              {
                name: 'EMS_CMD_HV_OnOff', // 点位名称
                value: 2, //点位值，输入为准，可以是字符串，数字，浮点型
              },
            ],
          },
        ],
      });
      const isSuccess = result.data.devices.find((item: any) => item.msgId === msgId)?.addresses?.find((item: any) => item.isSuc)
      const successMessage = `停止${errorTypeMap[type]}成功`;
      const errorMessage = `停止${errorTypeMap[type]}失败`;

      if (isSuccess) {
        message.success(successMessage);
        refresStorageProgressData(type, []);
        setProgressData([]);
      } else {
        message.error(errorMessage);
      }
    } catch (error: any) {
      handleOperationError(error.message, type);
    }
  };
  // const handleStopOperation = async (type: keyof typeof errorTypeMap) => {
  //   try {
  //     const result = await HomeScreenService.stopCurrentOperation(type);
  //     const successMessage = `停止${errorTypeMap[type]}成功`;
  //     const errorMessage = `停止${errorTypeMap[type]}失败`;

  //     if (result.success) {
  //       message.success(result.message || successMessage);
  //       refresStorageProgressData(type, []);
  //       setProgressData([]);
  //     } else {
  //       message.error(result.message || errorMessage);
  //     }
  //   } catch (error: any) {
  //     handleOperationError(error.message, type);
  //   }
  // };
  // 开始补能、供电等
  // 重机写死版本
  const handleStartOperation = async (type: keyof typeof errorTypeMap, params: any) => {
    // 开始的时候清空当前类型之前的流程信息 报错信息
    refresStorageProgressData(type, []);
    setProgressData([]);
    clearErrorMsg(type);
    try {
      const msgId = Date.now().toString();
      const result = await httpPost('/api/RootInterface/WriteCommand', {
        devices: [
          {
            msgId: msgId, // 消息ID
            deviceCode: 'BMS1', // 设备ID
            addresses: [
              {
                name: 'EMS_CMD_HV_OnOff', // 点位名称
                value: 1, //点位值，输入为准，可以是字符串，数字，浮点型
              },
            ],
          },
        ],
      });
      const isSuccess = result.data.devices.find((item: any) => item.msgId === msgId)?.addresses?.find((item: any) => item.isSuc)
      const successMessage = `开始${errorTypeMap[type]}成功`;
      const errorMessage = `开始${errorTypeMap[type]}失败`;

      if (isSuccess) {
        message.success(successMessage);
      } else {
        message.error(errorMessage);
      }
    } catch (error: any) {
      handleOperationError(error.message, type);
    }
  };
  // 灵活配置版本
  // const handleStartOperation = async (type: keyof typeof errorTypeMap, params: any) => {
  //   // 开始的时候清空当前类型之前的流程信息 报错信息
  //   refresStorageProgressData(type, []);
  //   setProgressData([]);
  //   clearErrorMsg(type);
  //   try {
  //     const result = await HomeScreenService.startCurrentOperation(type, params);
  //     const successMessage = `开始${errorTypeMap[type]}成功`;
  //     const errorMessage = `开始${errorTypeMap[type]}失败`;

  //     if (result.success) {
  //       message.success(result.message || successMessage);
  //     } else {
  //       message.error(result.message || errorMessage);
  //     }
  //   } catch (error: any) {
  //     handleOperationError(error.message, type);
  //   }
  // };
  const handleOperationError = (error: string, operationType: string) => {
    const errorMessage =
      error ??
      (operationType === 'ac'
        ? '补能异常，请检查设备状态'
        : operationType === 'acGridOut'
          ? '放电异常，请检查设备状态'
          : '请检查设备状态');

    message.error(errorMessage || '操作异常，请检查设备状态');
  };

  const handleConfirm = useCallback(
    async (operationData: any) => {
      const { type, params, optType } = operationData;
      setConfirmLoading(true);
      try {
        setOpenProgressModal(true);
        // 启动前清空 Alarm 和 Error
        // @ts-ignore
        setAlarm((prev: any) => ({ ...prev, [type]: '', content: '' }));
        // @ts-ignore
        setError((prev: any) => ({ ...prev, [type]: '', content: '' }));
        if (optType === 'start') {
          handleStartOperation(type, params);
        }
        if (optType === 'stop') {
          // 停止
          handleStopOperation(type);
        }
      } finally {
        cleanupOperation();
      }
    },
    [handleStartOperation, handleStopOperation, cleanupOperation],
  );
  return (
    <>
      <div className="flex h-full w-full flex-col">
        {!isPrepare && (
          <Button
            disabled={disabled || isPrepare}
            onClick={handleClick}
            className="flex h-full w-full items-center rounded-[10px] border-[2px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
          >
            <span
              className="whitespace-nowrap text-2xl font-normal [font-family:'PingFang_SC-Semibold',Helvetica]"
              style={{
                color: '#81dc4a',
              }}
            >
              {btnText ??
                `${optType === 'start' ? '开始' : '停止'}${errorTypeMap[type].includes('补能') ? '补能' : errorTypeMap[type].includes('放电') ? '放电' : '供电'}`}
            </span>
            {optType === 'stop' && (
              <img className="h-[28px] w-[17px]" alt="Union" src="/home/<USER>" />
            )}
          </Button>
        )}
        {withProgress && isPrepare && (
          <div className="h-full w-full">
            <ProgressStatus
              stepData={progressData || []}
              current={currentProgressNode}
              // title={
              //   getPorcessTitle('ACInputStatus', data.ACInputStatus) ||
              //   getPorcessTitle('ACGridOutPowerStatus', data.ACGridOutPowerStatus)
              // }
              openModal={isPrepare && openProgressModal}
              {...progressStatusProps}
            />
          </div>
        )}
        {/* 错误信息 */}
        {type && err?.[type] && (
          <div className="text-md mt-2 whitespace-pre-wrap break-all text-[#FF4545]">
            {err?.[type]}
          </div>
        )}
      </div>
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => {
          setConfirmVisible(false);
        }}
        onConfirm={() => handleConfirm(confirmFuncParams)}
        confirmLoading={confirmLoading}
        content={<div className="leading-7">{confirmContent}</div>}
      />
    </>
  );
};

export default OptButton;
