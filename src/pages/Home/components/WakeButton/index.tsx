import { ConfirmModal } from "@/pages/DeviceStatus/components/ConfirmModal";
import { httpPost } from "@/shared/http";
import { useRequest } from "ahooks";
import { Button, message } from "antd";
import { useState } from "react";

export function WakeButton() {
  const [isWaked, setIsWaked] = useState(false);
  const [confirmVisible, setConfirmVisible] = useState(false);

  async function start() {
    // TODO: 添加唤醒逻辑
    setIsWaked(true);
    setConfirmVisible(false);
  }
  async function stop() {
    // TODO: 添加停止唤醒逻辑
    const msgId = Date.now().toString();
    const result = await httpPost('/api/RootInterface/WriteCommand', {
      devices: [
        {
          msgId: msgId, // 消息ID
          deviceCode: 'BMS1', // 设备ID
          addresses: [
            {
              name: 'EMS_CMD_HV_OnOff', // 点位名称
              value: 1, //点位值，输入为准，可以是字符串，数字，浮点型
            },
          ],
        },
      ],
    });      
    const isSuccess = result.data.devices.find((item: any) => item.msgId === msgId)?.addresses?.find((item: any) => item.isSuc)
    const successMessage = `开始唤醒成功`;
    const errorMessage = `开始唤醒失败`;

    if (isSuccess) {
      message.success(successMessage);
      setIsWaked(false);
    } else {
      message.error(errorMessage);
    }
    setConfirmVisible(false);
  }
  const { loading, run: runAction } = useRequest(() => isWaked ? stop() : start(), { manual: true });
  const handleClick = () => {
    if (loading) return;
    setConfirmVisible(true);
  }

  return (
    <div className="flex">
      <Button
        onClick={handleClick}
        className="flex h-[60px] w-full items-center px-9 rounded-[10px] border-[2px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
      >
        <span
          className="whitespace-nowrap text-[24px] font-normal [font-family:'PingFang_SC-Semibold',Helvetica]"
          style={{
            color: '#81dc4a',
          }}
        >
          { isWaked ? '停止唤醒' : '开始唤醒'}
        </span>
      </Button>
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => {
          setConfirmVisible(false);
        }}
        onConfirm={() => runAction()}
        confirmLoading={loading}
        content={<div className="leading-7">{ isWaked ? '确定要停止唤醒吗？' : '确定要开始唤醒吗？'}</div>}
      />
    </div>
  )
}