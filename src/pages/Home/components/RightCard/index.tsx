import { useCallback, useEffect, useState } from 'react';
import OptButton from '../OptButton';
import WriteItems from '../WriteItems';
import { typeMap, useGlobalError } from '@/contexts/GlobalErrorContext';
import { InfoCircleFilled } from '@ant-design/icons';
import { BatterySlotsSimple } from '@/components/BatterySlotsSimple';
import clsx from 'clsx';

const RightCard = ({
  data = {},
  title = '直流供电',
  cardInfoConfig,
  progressStatusProps,
  className = '',
  type = '',
  detailData = {},
  goRunLogs,
}: any) => {
  const { alarm } = useGlobalError();
  const [bottomButton, setBottomButton] = useState(false);
  const [currentCardData, setCurrentCardData] = useState<any>({});
  const [batteryStatus, setBatteryStatus] = useState<boolean[] | undefined>(undefined);

  // 获取字段值并处理Content类型的翻译
  const getFieldValue = useCallback(
    (name: string, id: string = 'PCSOverview') => {
      const value = detailData?.[id]?.[name];
      const res = value !== undefined && value !== null ? value : '-';
      //  res是小数时保留两位小数，整数则原样返回
      if (typeof res === 'number' && res % 1 !== 0) {
        return res.toFixed(2);
      }
      return res;
    },
    [detailData],
  );
  const handleChange = (value: any) => {
    setCurrentCardData((prev: any) => ({ ...prev, ...value }));
  };

  useEffect(() => {
    setBottomButton(cardInfoConfig?.ButtonItems?.Items?.length > 0);
  }, [cardInfoConfig]);


  useEffect(() => {
    if (!cardInfoConfig?.InputPowerGoups?.length) return;
    const status: boolean[] = [];
    cardInfoConfig?.InputPowerGoups?.forEach((battery: any, index:number) => {
      const current = detailData[battery.DeviceID || cardInfoConfig.DeviceID]?.[battery.Name];
      // const _status = !!current && current < 18;
      const _status = current == 1;
      status.push(_status);
    });
    setBatteryStatus(status);
  }, [cardInfoConfig, cardInfoConfig?.InputPowerGoups]);
  return (
    <div className="output-card flex-1 flex flex-col gap-4">
      {/* 顶部标题区域 */}
      <div className="flex flex-col items-center justify-center">
        <div className="mt-[-1.00px] text-[24px] font-medium text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]">
          {cardInfoConfig.Label || title}
          {/* @ts-ignore */}
          {alarm?.[typeMap[type]] ? (
            <InfoCircleFilled style={{ color: '#FFC337', marginLeft: '8px' }} onClick={goRunLogs} />
          ) : null}
        </div>
      </div>
      <div className="flex h-[calc(100%-32px)] flex-col justify-between rounded-[10px] border-[2px] border-solid border-[#1C5DBC80] bg-transparent px-[10px] pb-[10px]">
        {/* 数据显示区域 max-h-[232px] */}
        <div className={'relative flex items-center justify-center mb-[5px]'}>
          <div className="card-title">
            <h3 className="text-xl text-[#39ABFF]">{cardInfoConfig?.SubLabel}</h3>
          </div>
        </div>
        <div className={clsx("flex w-full flex-wrap justify-around", cardInfoConfig?.LabelLayout?.layout == 'column' ? 'flex-col' : 'flex-row')} style={cardInfoConfig?.LabelLayout?.style}>
          {cardInfoConfig?.LabelGoup?.map((item: any) => {
            if (!item.Children || !item.Children?.length) {
              const value = getFieldValue(
                item.Name || '',
                item.DeviceID || cardInfoConfig.DeviceID,
              );
              const unit = item.Unit || '';
              // const valueLength = `${value}${unit}`.length;
              return (
                <div className="flex w-full items-center md:w-1/2" key={item.Label}>
                  <span
                    className={`tracking-[0] text-[#9CCEFF] [font-family:'PingFang_SC-Medium',Helvetica] ${className}`}
                  >
                    {item.Label}:
                  </span>
                  <div className={`ml-[2px] tracking-[0] text-[#E3F8FF] ${className}`}>
                    {value}
                    {unit ? <span className="ml-[2px]">{unit}</span> : ''}
                  </div>
                </div>
              );
            }
            return (
              <div className="w-full" key={item.Label}>
                <span
                  className={`tracking-[0] text-[#9CCEFF] [font-family:'PingFang_SC-Medium',Helvetica] ${className}`}
                >
                  {item.Label}:
                </span>
                <div className="flex justify-between">
                  {item.Children?.map((child: any) => {
                    const value = getFieldValue(
                      child.Name || '',
                      item.DeviceID || cardInfoConfig.DeviceID,
                    );
                    const unit = child.Unit || '';
                    // const valueLength = `${value}${unit}`.length;
                    return (
                      <div key={child.Label} className="flex items-center">
                        <span
                          className={`tracking-[0] text-[#E3F8FF] [font-family:'PingFang_SC-Medium',Helvetica] ${className}`}
                        >
                          {child.Label}:
                        </span>
                        <div className={`ml-[2px] tracking-[0] text-[#E3F8FF] ${className}`}>
                          {value}
                          {unit ? <span className="ml-[2px]">{unit}</span> : ''}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="flex flex-row gap-4 flex-1 ">
          <BatterySlotsSimple status={batteryStatus} config={cardInfoConfig} rechargeReset={(e) => console.log(e)}>
            
          </BatterySlotsSimple>
        </div>

        {cardInfoConfig?.MultiWriteItems &&
          cardInfoConfig?.MultiWriteItems?.Group.map((item: any) => {
            if (!item.Name) return null;
            return (
              <WriteItems
                configItems={[item]}
                deviceCode={cardInfoConfig?.MultiWriteItems?.DeviceID}
                onChange={handleChange}
              />
            );
          })}
        {bottomButton && (
          <div className="mt-5 min-h-[52px] w-full">
            {cardInfoConfig?.ButtonItems?.Items?.map((item: any) => {
              return (
                <OptButton
                  // @ts-ignore
                  type={typeMap[type]}
                  data={data}
                  progressStatusProps={progressStatusProps}
                  writeValue={currentCardData}
                  withProgress={cardInfoConfig?.StatusFlag}
                  apiParams={item.Value}
                />
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default RightCard;
