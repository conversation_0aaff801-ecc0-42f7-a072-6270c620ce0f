import { MultiWriteItemsContainer } from '@/pages/DeviceStatus/components/MultiWriteItems';
import storage from '@/shared/storage';
import { Divider } from 'antd';
import { useEffect, useState } from 'react';
import './index.less'

const WriteItems = ({ configItems, deviceCode, onChange }: any) => {
  const [value, setValue] = useState<any>();
  const [detailData, setDetailData] = useState<any>({
    [configItems?.[0]?.Name]: configItems?.[0]?.Value?.userInputPara,
  });
  const fetchDetailData = async (silent?: boolean, params?: Record<string, any>) => {
    const _power =
      params?.devices?.[0]?.addresses?.[0]?.value || storage.get(configItems?.[0]?.['Name']);
    storage.set(configItems?.[0]?.['Name'], _power);
    setValue(_power);
  };
  useEffect(() => {
    setDetailData({
      [configItems?.[0]?.Name]: value,
    });
    onChange?.({
      [configItems?.[0]?.Name]: value,
    });
  }, [value]);

  useEffect(() => {
    const _power = storage.get(configItems?.[0]?.['Name']) || configItems?.[0]?.DefaultValue;
    setValue(_power);
  }, []);

  return (
    <div className="device-modal-common home-page">
      <Divider className="m-0 mb-5 mt-5 bg-[#1C4DA0]" />
      <MultiWriteItemsContainer
        configItems={configItems}
        detailData={detailData}
        deviceCode={deviceCode}
        refreshData={fetchDetailData}
        columnsPerRow={1}
        className="mb-[0px!important] h-full"
        WriteCommand={configItems?.[0]?.Api === "/api/RootInterface/WriteCommand"}
      />
    </div>
  );
};

export default WriteItems;
