import { useCallback, useEffect, useMemo, useState } from 'react';
import ProgressStatus from '../../WrapperScreen/ProgessStatus';
import { BatterySlotsSimple } from '@/components/BatterySlotsSimple';
import { Divider, message } from 'antd';
import WriteItems from '../WriteItems';
import OptButton from '../OptButton';
import { notProgressStatusMap, typeMap, typeStatusMap, useGlobalError } from '@/contexts/GlobalErrorContext';
import { formatMilliseconds } from '@/utils/formatDate';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import { httpPost } from '@/shared/http';
import storage from '@/shared/storage';
import './index.less';

interface InputCardProps {
  data: any;
  cardInfoConfig: any;
  progressStatusProps?: any;
  isPrepare?: any;
  type: string;
  detailData: any;
}

// 左侧卡片
const LeftCard = ({
  data = {},
  cardInfoConfig,
  progressStatusProps,
  // isPrepare = false,
  type = '',
  detailData = {},
}: InputCardProps) => {
  const [openProgressModal, setOpenProgressModal] = useState(false);
  const [progressData, setProgressData] = useState<any>([]);
  const { setError } = useGlobalError();

  const [batteryStatus, setBatteryStatus] = useState<boolean[] | undefined>(undefined);
  const [bottomButton, setBottomButton] = useState(false);
  const [currentCardData, setCurrentCardData] = useState<any>({});
  // 操作确认
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmContent, setConfirmContent] = useState('');
  const [confirmFuncParams, setConfirmFuncParams] = useState<any>(null);

  const [isOperating, setIsOperating] = useState<boolean>(false);
  // 当前卡片状态
  // @ts-ignore
  const currentStatusKey = typeStatusMap[typeMap[type]];
  const currentStatus = data[currentStatusKey];
  // 针对直流补能
  const [isPrepare, setIsPrepare] = useState<boolean>(false);

  // 是否底部按钮
  useEffect(() => {
    setBottomButton(cardInfoConfig?.ButtonItems?.Items?.length > 0);
  }, [cardInfoConfig]);

  useEffect(() => {
    if (!cardInfoConfig?.InputPowerGoups?.length) return;
    const status: boolean[] = [];
    cardInfoConfig?.InputPowerGoups?.forEach((battery: any, index:number) => {
      const current = detailData[battery.DeviceID || cardInfoConfig.DeviceID]?.[battery.Name];
      // const _status = !!current && current < 18;
      const _status = current == 2;
      status.push(_status);
    });
    setBatteryStatus(status);
  }, [cardInfoConfig, cardInfoConfig?.InputPowerGoups]);

  // 获取字段值并处理Content类型的翻译
  const getFieldValue = useCallback(
    (name: string, id: string = 'PCSOverview') => {
      console.log('getFieldValue', name, id, detailData);
      const value = detailData?.[id]?.[name];
      const res = value !== undefined && value !== null ? value : '-';
      //  res是小数时保留两位小数，整数则原样返回
      if (typeof res === 'number' && res % 1 !== 0) {
        return res.toFixed(2);
      }
      return res;
    },
    [detailData],
  );

  const handleChange = (value: any) => {
    setCurrentCardData((prev: any) => ({ ...prev, ...value }));
  };

  // 充电桩card A枪 B枪
  const renderChargingGun = (data: any) => {
    const itemStatus = data.Children.find((child: any) => child.Label === '连接状态');
    const itemStatusValue = ![0, '-'].includes(
      getFieldValue(
        itemStatus.Name,
        itemStatus.DeviceID || data.DeviceID || cardInfoConfig.DeviceID,
      ),
    );
    const textStyle = type !== 'DCDCOut' ? 'text-[14px] w-full' : 'text-[20px] w-[calc(50%-8px)] ';
    return (
      <div className="chargingGun-card flex flex-1 flex-col">
        {/* chargingGun charging */}
        <div
          className={`mb-2 flex items-center gap-2 font-medium text-[#6BADE5] ${type !== 'DCDCOut' ? 'text-[16px]' : 'text-[22px]'}`}
        >
          <img src={'/home/<USER>'} alt="充电枪" className="h-[14px] w-[16px]" />
          <span className={`mr-2 font-semibold`}>{data?.Label}</span>
          <Divider
            type="vertical"
            className={`m-0 mt-0 bg-[#2655AA] ${type !== 'DCDCOut' ? 'h-[12px]' : 'h-[22px]'}`}
          />
          <div
            className={`h-[6px] w-[6px] rounded-full bg-[#00ff50]`}
            style={{
              backgroundColor: itemStatusValue ? '#00ff50' : '#858585',
            }}
          ></div>
          <span className={`${type !== 'DCDCOut' ? 'text-[14px]' : 'text-[20px]'}`}>
            {itemStatusValue ? '已插枪' : '未插枪'}
          </span>
        </div>
        {!itemStatusValue ? (
          <div className="flex flex-1">
            <div className="flex flex-1 flex-wrap gap-1">
              {data.Children.map((child: any) => {
                if (child.Label === '连接状态') {
                  return <></>;
                }
                return (
                  <div
                    key={`${child.DeviceID || cardInfoConfig.DeviceID}-${child.Name || child.Label}`}
                    className={`flex h-[18px] items-center ${textStyle}`}
                  >
                    <div className={`text-nowrap font-medium leading-6 text-[#6BADE5]`}>
                      {child.Label}:
                    </div>
                    <div className={`ml-1 font-medium text-[#C7F3FF]`}>
                      {getFieldValue(child.Name, child.DeviceID || cardInfoConfig.DeviceID)}
                      {child.Unit ? <span className="ml-[2px]">{child.Unit}</span> : ''}
                    </div>
                  </div>
                );
              })}
            </div>
            {/* 执行中是文本 */}
            {/* DCDC 直流供电没有这个部分 */}
            {type !== 'DCDCOut' && (
              <div className="relative flex h-[80px] w-[70px] flex-col items-center justify-center rounded-xl border border-[rgba(54,117,219,1)] border-opacity-100 bg-[linear-gradient(0deg,rgba(0,135,255,0.05)_0%,rgba(0,135,255,0.15)_100%)] shadow-lg">
                <div>
                  <img src={'/home/<USER>'} alt="充电" className="h-5 w-[15px]" />
                </div>
                <div className="text-center">
                  <div className="mt-1 text-[12px] font-normal leading-[1.45em] text-[#C7F3FF]">
                    开启充电
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex h-full w-full items-center justify-center text-[18px] text-[#2B679A]">
            待机中...
          </div>
        )}
        <Divider className="bottom-divider m-0 my-[11px] bg-[#1C4DA0]" />
      </div>
    );
  };
  // 充电桩卡片
  const renderChargingCard = () => {
    return (
      <div className="flex h-full w-full flex-1 flex-col content-around justify-around">
        {cardInfoConfig?.LabelGoup?.map((item: any) => {
          // 充电桩
          if (item.Children) {
            return renderChargingGun(item);
          }
        })}
      </div>
    );
  };
  // 顶部连接状态
  const status = useMemo(() => {
    const device = cardInfoConfig.DeviceID;
    if(!device) return false;
    const deviceData = data?.Devices?.find((item: any) => item.device.name === device);
    return deviceData?.device?.status === 'Connect';
  }, [data?.Devices, cardInfoConfig.DeviceID]);

  // 电池组
  const renderBatteryCard = () => {
    const style = cardInfoConfig?.LabelStyle;
    return (
      <div className="flex h-full min-h-[180px] w-full flex-1 flex-col content-around justify-around" style={style}>
        {cardInfoConfig?.LabelGoup?.map((item: any) => {
          let value = getFieldValue(item.Name, item.DeviceID || cardInfoConfig.DeviceID);
          //时长
          if (item.type === 'duration' && !Number.isNaN(Number(value))) {
            value = formatMilliseconds(Number(value) * 60 * 1000);
          }
          if (item.Label === 'SoC' || item.ViewType === 'progressAndLabel') {
            return (
              <div className="flex h-[18px] flex-row items-center gap-3">
                <div className="text-xl font-medium leading-6 tracking-[0] text-[#6BADE5]">
                  {item.Label}
                </div>
                <div className="h-[12px] w-full max-w-[185px] rounded-full bg-[#1F4E9F]">
                  <div
                    className="h-[12px] rounded-full bg-[linear-gradient(90.02deg,#979797_0.03%,#DEDEDE_99.49%)]"
                    style={{ width: '49%' }}
                  ></div>
                </div>
                <span className="text-xl font-medium text-[#C7F3FF]">49%</span>
              </div>
            );
          }
          // 电池组
          return (
            <div
              key={`${item.DeviceID || cardInfoConfig.DeviceID}-${item.Name || item.Label}`}
              className="flex h-[18px] items-center"
            >
              <div className="text-xl font-medium leading-6 tracking-[0] text-[#6BADE5]">
                {item.Label}:
              </div>
              <div className="ml-1 text-xl font-medium text-[#C7F3FF]">
                {value}
                {item.type !== 'duration' && item.Unit ? (
                  <span className="ml-[2px]">{item.Unit}</span>
                ) : (
                  ''
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };
  // 补能状态
  const dcCharging = useMemo(() => {
    return currentStatus == 2 || currentStatus == 1;
  }, [currentStatus]);

  // 当前电池组是否充电中
  const batteryCharging = useMemo(() => {
    const device = cardInfoConfig.DeviceID;
    return device ? detailData?.[device]?.[cardInfoConfig.Name] === 3 : false;
  }, [detailData, cardInfoConfig]);

  useEffect(() => {
    const DCInputStatus = data.DCInputStatus;
    const currentIsPrepare = !notProgressStatusMap.includes(DCInputStatus);
    if (currentIsPrepare) {
      setConfirmVisible(false);
      setIsPrepare(currentIsPrepare);
      // 状态更新为直流补能开始时清空补能错误信息
      // @ts-ignore
      setError((prev: any) => ({ ...prev, [typeMap[type]]: '', content: '' }));
    }

    if (DCInputStatus !== undefined && notProgressStatusMap.includes(DCInputStatus)) {
      // 延迟5秒执行
      const timer = setTimeout(() => {
        setProgressData([]);
        storage.set(`progress${type}`, []);
        setIsPrepare(currentIsPrepare);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [data.DCInputStatus, notProgressStatusMap, setProgressData]);

  // 补能复位
  const rechargeReset = (deviceId: string) => {
    if (!dcCharging) {
      message.warning('当前状态不是补能中，无法补能复位');
      return;
    }
    if (isOperating) {
      message.error('操作冷却中，请稍后再试');
      return;
    }
    setConfirmVisible(true);
    setConfirmContent(`确认是否要操作补能复位？`);
    // 存储当前操作的参数，而不是函数
    const currentParams = {
      type: 'reset',
      deviceId,
    };
    setConfirmFuncParams(currentParams);
  };

  const handleDCRest = async (deviceId: string) => {
    try {
      setIsOperating(true);
      const res = await httpPost('EMS/DCInputPower/Stop', {
        chargingDockId: deviceId || 'DcChargingDock1', //充电座的充电口id
      });
      if (res.result == 'successful') {
        message.info('补能复位操作成功！');
        setTimeout(() => {
          setIsOperating(false);
        }, 3000);
      } else {
        message.error(res?.resultInfo || '补能复位操作失败！');
        // 失败后立即可以再次点击
        setIsOperating(false);
      }
    } catch (error) {
      message.error('操作失败，请重试');
      // 异常后立即可以再次点击
      setIsOperating(false);
    }
  };
  // 补能复位确认
  const handleConfirm = async (operationData: any) => {
    const { type, deviceId } = operationData;

    setConfirmLoading(true);
    try {
      setConfirmVisible(false);
      if (type === 'reset') {
        await handleDCRest(deviceId);
      }
    } catch (error) {
    } finally {
      setConfirmLoading(false);
      setConfirmVisible(false);
    }
  };

  return (
    <div className="input-card flex h-full min-w-[210px] flex-1 flex-col overflow-hidden rounded-[10px] border-[2px] border-solid border-[#1C5DBC80] bg-transparent">
      {/* 顶部标题区域 */}
      <div className={'relative flex items-center justify-center'}>
        <div className="card-title">
          <h3 className="text-xl text-[#39ABFF]">{cardInfoConfig?.Label}</h3>
          <div
            className="ml-2 h-[9px] w-[9px] rounded-[50%] bg-[#00ff50]"
            style={{
              backgroundColor: `${status}` === '0' || !status ? '#858585' : '#00ff50',
            }}
          />
        </div>
      </div>

      <div className="flex h-full flex-col p-[10px]">
        {/* 数据显示区域 */}
        <div className="flex h-full items-center" style={cardInfoConfig?.LabelStyle}>
          {cardInfoConfig?.Label.includes('电池组')
            ? // {/* 电池组 */}
              renderBatteryCard()
            : // {/* 充电桩 */}
              renderChargingCard()}
        </div>
        {/* 设置参数 */}
        {/* {renderMultiWriteItems()} */}
        {cardInfoConfig?.MultiWriteItems &&
          cardInfoConfig?.MultiWriteItems?.Group.map((item: any) => {
            if (!item.Name) return null;
            return (
              <WriteItems
                configItems={[item]}
                deviceCode={cardInfoConfig?.MultiWriteItems?.DeviceID}
                onChange={handleChange}
              />
            );
          })}
        {/* 电池组状态 */}
        {/* 补能复位 */}
        {!!batteryStatus && batteryStatus.length > 0 && (
          <div className="flex flex-row gap-4 flex-1">
            <BatterySlotsSimple status={batteryStatus} config={cardInfoConfig} rechargeReset={rechargeReset}>
              
            </BatterySlotsSimple>
          </div>
        )}
        {/* 按钮(动态带流程) */}
        {bottomButton && (
          <div className="mt-5 min-h-[52px] w-full">
            {cardInfoConfig?.ButtonItems?.Items?.map((item: any) => {
              return (
                <OptButton
                  // @ts-ignore
                  type={typeMap[type]}
                  data={data}
                  progressStatusProps={progressStatusProps}
                  withProgress={cardInfoConfig?.StatusFlag}
                  writeValue={currentCardData}
                  apiParams={item.Value}
                />
              );
            })}
          </div>
        )}
        {/* 流程日志（无操作按钮）目前仅直流补能情况 */}
        {/*  非流程中且状态=2时需要显示充电中 */}
        {!bottomButton && cardInfoConfig?.StatusFlag && !batteryCharging && (
          <div className="mt-2 h-[48px] w-full px-2">
            {isPrepare && progressData && (
              <div className="h-full w-full">
                <ProgressStatus
                  // @ts-ignore
                  stepData={progressData || []}
                  // current={currentProgressACNode}
                  // title={
                  //   progressStatusProps.title
                  // }
                  {...progressStatusProps}
                  border={false}
                  // openModal={isPrepare && openProgressModal}
                />
              </div>
            )}
            {batteryCharging && (
              // 充电中
              <div className="flex w-full items-center justify-center gap-[10px]">
                <div className="border-image-slice-1 h-[2px] flex-1 border-solid bg-[linear-gradient(270deg,#3BE354_0%,rgba(59,227,84,0)_100%)]"></div>
                <span className="inline-flex items-center text-2xl font-medium text-[#3BE354]">
                  充电中
                </span>
                <img className="h-[28px] w-[17px]" alt="Union" src="/home/<USER>" />
                <div className="border-image-slice-1 h-[2px] flex-1 border-solid bg-[linear-gradient(30deg,#3BE354_0%,rgba(59,227,84,0)_100%)]"></div>
              </div>
            )}
          </div>
        )}
      </div>
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => {
          setConfirmVisible(false);
        }}
        onConfirm={() => handleConfirm(confirmFuncParams)}
        confirmLoading={confirmLoading}
        content={<div className="leading-7">{confirmContent}</div>}
      />
    </div>
  );
};

export default LeftCard;
