@import '../../../DeviceStatus/components/modalCommon.less';
.input-card{
  // .modal-content();
  .device-modal-common();
}
.card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('@/assets/card-title.png') no-repeat center;
  background-size: contain;
  width: 190px;
  height: 42px;
}

.chargingGun-card:last-child {
  .ant-divider.bottom-divider {
    display: none;
  }
}