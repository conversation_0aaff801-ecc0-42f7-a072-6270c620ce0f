import { Card, CardContent } from '@/components/ui/card';
import { ChargingStatusSection } from '../WrapperScreen/ChargingStatusSection';
import { PowerDetailsSection } from '../WrapperScreen/PowerDetailsSection';
import { ActionButtonsSection } from '../WrapperScreen/ActionButtonsSection';
import { useMemo, useCallback, useState, useEffect } from 'react';
import { Button, message, Modal } from 'antd';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import useRecharge from '../useRecharge';
import VinSection from '../WrapperScreen/VinSection';

function BuNengSection({ activeTab = 'buneng', data = {} }: any) {
  const [chargingPower, setChargingPower] = useState<number>(220);
  const [isChecking, setIsChecking] = useState(false);
  const { data: wsData } = useRecharge();

  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  // 补能可以供电 供电不能补能
  const { systemStatus, acInputMaxPower = 380, acInputDefaultPower = 220 } = useDeviceStore();
  // 默认值
  useEffect(() => {
    setChargingPower(acInputDefaultPower);
  }, [acInputDefaultPower]);

  const handlePowerChange = (power: number) => {
    setChargingPower(power);
  };
  // 获取状态文本 // status 1:开始、2:补能中，0:停止 | 启动1，0为关闭，2为进行中
  const getStatusText = (status: number) => {
    switch (status) {
      case 1:
        return '开始补能';
      case 2:
        return '补能中';
      case 0:
        return '停止补能';
      default:
        return '停止补能';
    }
  };

  // 获取图标
  const getStatusIcon = (status: number) => {
    return status == 0 || !status ? '/home/<USER>' : '/home/<USER>';
  };

  const chargingGunData = useMemo(() => {
    if (data.Devices?.length > 0) {
      const dt = data.Devices.find((d: Record<string, any>) => d.device?.type === 'ChargeGun');
      return dt;
    }
  }, [data?.Devices]);

  const pcsData = useMemo(() => {
    if (data.Devices?.length > 0) {
      const dt = data.Devices.find((d: Record<string, any>) => d.device?.type === 'PCS');
      return dt;
    }
  }, [data?.Devices]);

  // 判断是否可以补能
  const canCharge = useMemo(() => {
    // 补能可以供电 供电不能补能
    if (systemStatus == 2) {
      return true;
    } else {
      return false;
    }
  }, [systemStatus]);

  // 判断pcs可以补能
  const chargeDisabled = useMemo(() => {
    if (data.ACInputStatus != 0 || !canCharge) {
      return true; // 不可以补能
    } else {
      return false;
    }
  }, [canCharge]);

  const isCharging = useMemo(() => {
    return data.ACInputStatus == 2;
  }, [data.ACInputStatus]);

  const acOperate = useCallback(async () => {
    if (!canCharge && data.ACInputStatus !== 2) {
      message.warning('当前状态无法补能');
      return;
    }
    if (data.ACInputStatus == 2) {
      // 停止补能确认
      Modal.confirm({
        title: '停止补能确认',
        content: '是否停止补能？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const result = await HomeScreenService.stopCurrentOperation('ac', true);
          if (result.success) {
            message.success(result.message || '停止补能成功');
          } else {
            message.error(result.message || '停止补能失败');
          }
        },
      });
      return;
    }

    // 开始补能
    if (!chargingPower) {
      message.warning('请设置补能功率');
      return;
    }

    try {
      setIsChecking(true);
      const hideLoading = message.loading('补能前检测...');

      // 开始补能
      const result = await HomeScreenService.startACCharging(chargingPower);
      hideLoading();
      if (result.success) {
        message.success(result.message || '开始补能');
      } else {
        message.error(result.message || '启动补能失败');
      }
    } catch (error) {
      message.error('补能异常，请检查设备状态');
    } finally {
      setIsChecking(false);
    }
  }, [data.ACInputStatus, chargingPower, canCharge]);

 
  return (
    <div className="mt-5 flex gap-4">
      {/* Left Column */}
      <div
        className="flex w-[300px] flex-col gap-4 rounded-lg p-4"
        style={{
          marginTop: '60px',
          marginLeft: '12px',
          background: `
    linear-gradient(7deg, rgba(21, 94, 216, 0.40) 0.57%, rgba(23, 63, 129, 0.00) 90.25%),
    linear-gradient(7deg, rgba(14, 80, 188, 0.20) 0.57%, rgba(23, 63, 129, 0.00) 90.25%)
    `,
        }}
      >
        {/* DC Charging Status */}
        <Card className="rounded-lg border-none bg-transparent">
          <CardContent className="p-0">
            <div className="relative w-full">
              <div className="mt-4 flex h-5 w-full items-center">
                <img className="relative h-5 w-5" alt="Frame" src="/home/<USER>" />

                <div className="relative flex flex-1 grow items-center gap-2.5">
                  <div className="relative mt-[-1.00px] w-fit whitespace-nowrap text-xl font-normal leading-[18px] tracking-[0] text-white [font-family:'PingFang_SC-Semibold',Helvetica]">
                    直流补能状态
                  </div>
                </div>
              </div>

              <div className="mt-4 w-full">
                <div className="flex h-[74px] w-full items-center gap-1.5 px-0.5 py-3">
                  <div className="relative flex h-[62px] flex-1 grow flex-col items-start gap-1.5 border border-solid border-transparent px-3 py-[7px] shadow-[inset_0px_0px_8.34px_#00a6ffcc] [background:linear-gradient(0deg,rgba(17,72,173,0.6)_0%,rgba(5,48,126,0)_100%)] [border-image:linear-gradient(360deg,rgba(32,143,255,1)_0%,rgba(36,145,255,0)_100%)_1]">
                    <div className="relative flex h-px w-full items-start gap-0.5">
                      <div className="relative mb-[-13.00px] h-3.5 w-6" />
                    </div>

                    <div className="relative flex h-3.5 w-full items-start gap-[5px] self-stretch px-[13px] py-[11px]">
                      <div className="relative mb-[-22.00px] flex flex-1 grow items-center gap-2.5 px-5 py-0">
                        <div className="relative mt-[-1.00px] flex-1 text-lg font-normal leading-[14px] tracking-[0] text-[#00c7ff] [font-family:'PingFang_SC-Semibold',Helvetica]">
                          {getStatusText(data.DCInputStatus)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  className="absolute right-[30px] top-[50px] h-[37px] w-[37px] bg-[url(/home/<USER>"
                  style={{
                    backgroundImage: `url(${getStatusIcon(data.DCInputStatus)})`,
                  }}
                />
              </div>
            </div>

            {/* AC Charging Status */}
            <div className="relative mt-2 w-full">
              <div className="flex h-5 w-full items-center">
                <img className="relative h-5 w-5" alt="Frame" src="/home/<USER>" />

                <div className="relative flex flex-1 grow items-center gap-2.5">
                  <div className="relative mt-[-1.00px] w-fit whitespace-nowrap text-xl font-normal leading-[18px] tracking-[0] text-white [font-family:'PingFang_SC-Semibold',Helvetica]">
                    交流补能状态
                  </div>
                </div>
              </div>

              <div className="mt-4 w-full">
                <div className="flex h-[74px] w-full items-center gap-1.5 px-0.5 py-[7px]">
                  <div className="relative flex h-[62px] flex-1 grow flex-col items-start gap-1.5 border border-solid border-transparent px-3 py-[7px] shadow-[inset_0px_0px_8.34px_#00a6ffcc] [background:linear-gradient(0deg,rgba(17,72,173,0.6)_0%,rgba(5,48,126,0)_100%)] [border-image:linear-gradient(360deg,rgba(32,143,255,1)_0%,rgba(36,145,255,0)_100%)_1]">
                    <div className="relative flex h-px w-full items-start gap-0.5">
                      <div className="relative mb-[-13.00px] h-3.5 w-6" />
                    </div>

                    <div className="relative flex h-3.5 w-full items-start gap-[5px] self-stretch px-[13px] py-[11px]">
                      <div className="relative mb-[-22.00px] flex flex-1 grow items-center gap-2.5 px-5 py-0">
                        <div className="relative mt-[-1.00px] flex-1 text-lg font-normal leading-[14px] tracking-[0] text-[#00c7ff] [font-family:'PingFang_SC-Semibold',Helvetica]">
                          {getStatusText(data.ACInputStatus)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="absolute right-[30px] top-[50px] h-[37px] w-[37px] bg-[url(/home/<USER>" style={{ backgroundImage: `url(${getStatusIcon(data.ACInputStatus)})` }} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Middle Column */}
      <div className="flex-1">
        <div className="relative mb-4 flex flex-col items-center justify-center">
          <VinSection />
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center gap-[5px] rounded-[7px] p-1">
              <img className="relative h-9 w-10" alt="Frame" src="/home/<USER>" />

              <div className="inline-flex items-center gap-2.5">
                <div className="mt-[-1.00px] w-36 text-[32px] font-medium leading-5 tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]">直流补能</div>
              </div>
            </div>
          </div>
        </div>

        <ActionButtonsSection data={chargingGunData} />
      </div>

      {/* Right Column */}
      <div className="mr-3 flex w-[430px] flex-col gap-4">
        <div className="flex items-center justify-center gap-[5px]">
          <div className="inline-flex items-start rounded-[7px] px-2.5 py-1">
            <img className="relative h-7 w-5" alt="Union" src="/home/<USER>" />
          </div>

          <div className="inline-flex items-center justify-center gap-2.5">
            <div className="mt-[-1.00px] w-fit whitespace-nowrap text-[32px] font-medium leading-5 tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]">交流补能</div>
          </div>
        </div>

        <PowerDetailsSection data={pcsData} />
        <ChargingStatusSection maxPower={acInputMaxPower} value={chargingPower} onPowerChange={handlePowerChange} disabled={isCharging || isChecking} />

        <Button
          className="mt-1 h-16 w-full rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
          onClick={acOperate}
          disabled={!chargingPower || isChecking}
        >
          <span
            className="whitespace-nowrap text-2xl font-normal leading-[18px] tracking-[0] [font-family:'PingFang_SC-Semibold',Helvetica]"
            style={{
              color: isCharging ? '#fe4545' : '#81dc4a',
            }}
          >
            {isChecking ? '补能检测中...' : isCharging ? '停止补能' : '开始补能'}
          </span>
        </Button>
        <div className="overflow-hidden text-xs text-[#fe4545]">{wsData.msg || wsData.acChargeErrMsg}</div>
      </div>
    </div>
  );
}

export default BuNengSection;
