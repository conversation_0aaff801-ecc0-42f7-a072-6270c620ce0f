import { Card, CardContent } from "@/components/ui/card";
import { Button, message, Modal } from 'antd';
import { ActionButtonsSection } from "../WrapperScreen/ActionButtonsSection";
import { isNil } from 'lodash';
import { useCallback, useMemo } from 'react';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';

/*   const powerReadings = [
    { id: 1, label: "A相电压", value: "400", unit: "V" },
    { id: 2, label: "A相电压", value: "400", unit: "V" },
    { id: 3, label: "A相电压", value: "400", unit: "V" },
    { id: 4, label: "A相电压", value: "400", unit: "V" },
    { id: 5, label: "A相电压", value: "400", unit: "V" },
    { id: 6, label: "A相电压", value: "400", unit: "V" },
    { id: 7, label: "A相电压", value: "400", unit: "V" },
    { id: 8, label: "A相电压", value: "400", unit: "V" },
  ]; */

function GongDianSection({ activeTab = 'buneng', data = {} }: any) {

  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  const { systemStatus } = useDeviceStore();

  // 判断是否可以供电
  const canGongdian = useMemo(() => {
    if (systemStatus == 2 || systemStatus != 1) {
      return true
    } else {
      return false
    }
  }, [systemStatus]);

  // 判断直流是否可以供电
  const dcBtnDisabled = useMemo(() => {
    if (data.DCOutStatus != 0 || !canGongdian) {
      return true // 不可以供电
    } else {
      return false
    }
  }, []);

  // 判断交流是否可以供电
  const acBtnDisabled = useMemo(() => {
    if (data.ACOutStatus != 0 || !canGongdian) {
      return true // 不可以供电
    } else {
      return false
    }
  }, [])

  // 获取状态文本 // status 1:开始、2:补能中 或 0:停止 | 启动1，0为关闭，2为进行中
  const getStatusText = (status: number) => {
    switch (status) {
      case 1: return '开始供电';
      case 2: return '供电中';
      case 0: return '停止供电';
      default: return '停止供电';
    }
  };

  // 获取图标
  const getStatusIcon = (status: number) => {
    return (status == 0 || !status) ? '/home/<USER>' : '/home/<USER>';
  };



  const chargingGunData = useMemo(() => {
    if (data.Devices?.length > 0) {
      const dt = data.Devices.find((d: Record<string, any>) => d.device?.type === 'ChargeGun')
      return dt
    }
  }, [data?.Devices]);

  const pcsData = useMemo(() => {
    if (data.Devices?.length > 0) {
      const dt = data.Devices.find((d: Record<string, any>) => d.device?.type === 'PCS')
      return dt
    }
  }, [data?.Devices]);


  const powerReadings = useMemo(() => {
    const list: any = [];
    if (pcsData?.itemList?.length > 0) {
      for (let item of pcsData?.itemList) {
        let unit = item.unit || "";
        if (item.description?.indexOf('电压') !== -1) {
          unit = 'V'
        } else if (item.description?.indexOf('电流') !== -1) {
          unit = 'A'
        }
        list.push({
          id: item.name,
          label: item.description || item.name,
          value: isNil(item.value) ? '--' : item.value,
          unit
        })
      }
    }
    return list;
  }, [data?.itemList]);

  const dcOperate = useCallback(async () => {

    if (!canGongdian && data.DCOutStatus !== 2) {
      message.warning('当前状态无法供电');
      return;
    }

    try {
      if (data.DCOutStatus == 2) {
        // 停止供电确认
        Modal.confirm({
          title: '停止供电确认',
          content: '确认要停止直流供电吗？',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            const result = await HomeScreenService.stopCurrentOperation('dc', false);
            if (result.success) {
              message.success(result.message || '停止供电成功');
            } else {
              message.error(result.message || '停止供电失败');
            }
          }
        });
      } else {
        // 开始供电
        const result = await HomeScreenService.startDCPowerSupply();
        if (result.success) {
          message.success(result.message || '开始供电');
        } else {
          message.error(result.message || '启动供电失败');
        }
      }
    } catch (error) {
      message.error('操作异常，请检查设备状态');
    }
  }, [dcBtnDisabled, data.DCOutStatus, canGongdian]);

  const acOperate = useCallback(async () => {
    if (!canGongdian) {
      message.warning('当前状态无法供电');
      return;
    }

    try {
      if (data.ACOutStatus == 2) {
        // 停止供电确认
        Modal.confirm({
          title: '停止供电确认',
          content: '确认要停止交流供电吗？',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            const result = await HomeScreenService.stopCurrentOperation('ac', false);
            if (result.success) {
              message.success('停止供电成功');
            } else {
              message.error(result.message || '停止供电失败');
            }
          }
        });
      } else {
        // 开始供电
        const result = await HomeScreenService.startACPowerSupply();
        if (result.success) {
          message.success('开始供电');
        } else {
          message.error(result.message || '启动供电失败');
        }
      }
    } catch (error) {
      message.error('操作异常，请检查设备状态');
    }
  }, [acBtnDisabled, data.ACOutStatus, canGongdian]);

  return (
    <div className="flex mt-5 gap-4">
      {/* Left Column */}
      <div className="flex flex-col gap-4 w-[300px] rounded-lg p-4"
        style={{
          marginTop: '60px',
          marginLeft: '12px',
          background: `
    linear-gradient(7deg, rgba(21, 94, 216, 0.40) 0.57%, rgba(23, 63, 129, 0.00) 90.25%),
    linear-gradient(7deg, rgba(14, 80, 188, 0.20) 0.57%, rgba(23, 63, 129, 0.00) 90.25%)
    `
        }}
      >
        {/* DC Charging Status */}
        <Card className="rounded-lg border-none bg-transparent">
          <CardContent className="p-0">
            <div className="relative w-full">
              <div className="flex w-full h-5 items-center mt-4">
                <img
                  className="relative w-5 h-5"
                  alt="Frame"
                  src="/home/<USER>"
                />

                <div className="flex items-center gap-2.5 relative flex-1 grow">
                  <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-white text-xl leading-[18px] whitespace-nowrap relative tracking-[0]">
                    直流供电状态
                  </div>
                </div>
              </div>

              <div className="mt-4 w-full">
                <div className="flex w-full h-[74px] items-center gap-1.5 px-0.5 py-3">
                  <div className="flex flex-col h-[62px] items-start gap-1.5 px-3 py-[7px] relative flex-1 grow border border-solid border-transparent shadow-[inset_0px_0px_8.34px_#00a6ffcc] [border-image:linear-gradient(360deg,rgba(32,143,255,1)_0%,rgba(36,145,255,0)_100%)_1] [background:linear-gradient(0deg,rgba(17,72,173,0.6)_0%,rgba(5,48,126,0)_100%)]">
                    <div className="flex w-full h-px items-start gap-0.5 relative">
                      <div className="relative w-6 h-3.5 mb-[-13.00px]" />
                    </div>

                    <div className="flex h-3.5 items-start gap-[5px] px-[13px] py-[11px] relative self-stretch w-full">
                      <div className="flex items-center gap-2.5 px-5 py-0 relative flex-1 grow mb-[-22.00px]">
                        <div className="flex-1 mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#00c7ff] text-lg leading-[14px] relative tracking-[0]">
                          {getStatusText(data.DCOutStatus)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="absolute w-[37px] h-[37px] top-[50px] right-[30px] bg-[url(/home/<USER>"
                  style={{
                    backgroundImage: `url(${getStatusIcon(data.DCOutStatus)})`,
                    backgroundSize: '100% 100%'
                  }}
                />
              </div>
            </div>

            {/* AC Charging Status */}
            <div className="relative w-full mt-2">
              <div className="flex w-full h-5 items-center">
                <img
                  className="relative w-5 h-5"
                  alt="Frame"
                  src="/home/<USER>"
                />

                <div className="flex items-center gap-2.5 relative flex-1 grow">
                  <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-white text-xl leading-[18px] whitespace-nowrap relative tracking-[0]">
                    交流供电状态
                  </div>
                </div>
              </div>

              <div className="mt-4 w-full">
                <div className="flex w-full h-[74px] items-center gap-1.5 px-0.5 py-[7px]">
                  <div className="flex flex-col h-[62px] items-start gap-1.5 px-3 py-[7px] relative flex-1 grow border border-solid border-transparent shadow-[inset_0px_0px_8.34px_#00a6ffcc] [border-image:linear-gradient(360deg,rgba(32,143,255,1)_0%,rgba(36,145,255,0)_100%)_1] [background:linear-gradient(0deg,rgba(17,72,173,0.6)_0%,rgba(5,48,126,0)_100%)]">
                    <div className="flex w-full h-px items-start gap-0.5 relative">
                      <div className="relative w-6 h-3.5 mb-[-13.00px]" />
                    </div>

                    <div className="flex h-3.5 items-start gap-[5px] px-[13px] py-[11px] relative self-stretch w-full">
                      <div className="flex items-center gap-2.5 px-5 py-0 relative flex-1 grow mb-[-22.00px]">
                        {/* // 修改 AC 状态显示部分 */}
                        <div className="flex-1 mt-[-1.00px] [font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#00c7ff] text-lg leading-[14px] relative tracking-[0]">
                          {getStatusText(data.ACOutStatus)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="absolute w-[37px] h-[37px] top-[50px] right-[30px] bg-[url(/home/<USER>"
                  style={{
                    backgroundImage: `url(${getStatusIcon(data.ACOutStatus)})`,
                    backgroundSize: '100% 100%'
                  }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Middle Column */}
      <div className="flex-1">
        <div className="flex flex-col items-center justify-center mb-4">
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center gap-[5px] p-1 rounded-[7px]">
              <img
                className="relative w-10 h-9"
                alt="Frame"
                src="/home/<USER>"
              />

              <div className="inline-flex items-center gap-2.5">
                <div className="w-36 mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#3ae353] text-[32px] leading-5 tracking-[0]">
                  直流供电
                </div>
              </div>
            </div>
          </div>
        </div>

        <ActionButtonsSection
          data={chargingGunData}
          activeTab={activeTab}
          dcOperate={dcOperate}
        />
      </div>

      {/* Right Column */}
      <div className="flex flex-col gap-4 w-[430px] mr-3 pt-3">
        <div className="flex justify-center items-center gap-[5px]">
          <div className="inline-flex items-start px-2.5 py-1 rounded-[7px]">
            <img
              className="relative w-5 h-7"
              alt="Union"
              src="/home/<USER>"
            />
          </div>

          <div className="inline-flex justify-center items-center gap-2.5">
            <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#3ae353] text-[32px] leading-5 whitespace-nowrap tracking-[0]">
              交流供电
            </div>
          </div>
        </div>

        <Card className="w-full max-w-[430px] max-h-[302px] rounded-lg bg-gradient-to-t from-[rgba(21,94,216,0.4)] to-transparent border-0">
          <CardContent className="p-4 pt-10">
            <div className="flex flex-wrap">
              {powerReadings.map((reading: any, index: number) => (
                <div
                  key={reading.id}
                  className="flex items-center w-1/2 mb-8"
                >
                  <span className="text-[#326fa5] text-xl min-w-[100px]">
                    {reading.label}
                  </span>
                  <div className="flex items-baseline">
                    <span className="text-[#c7f2ff] text-xl">
                      &nbsp;{reading.value}
                    </span>
                    <span className="text-[#c7f2ff] text-xl ml-1">
                      {reading.unit}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Button
          className="w-full h-16 mt-3 rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
          onClick={acOperate}
        >
          <span className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#81dc4a] text-2xl leading-[18px] whitespace-nowrap tracking-[0]"
            style={{ color: data.ACOutStatus == 2 ? '#fe4545' : '' }}>
            {getStatusText(data.ACOutStatus == 2 ? 0 : 1)}
          </span>
        </Button>
      </div>
    </div>
  );
}

export default GongDianSection;
