export const config = {
  ViewType: 'Switch', //Tile/Switch
  NavItems: [
    { label: '首页', ViewType: 'Show' },
    { label: '监控', ViewType: 'Show' },
    { label: '统计', ViewType: 'Hide' },
    { label: '告警', ViewType: 'Hide' },
    { label: '运维', ViewType: 'Show' },
  ],
  Header: {
    SystemStatus: {
      Name: 'CurrentSystemStatus',
      Label: '系统状态',
      ViewType: 'Content',
    },
    RemainPower: {
      Name: 'CurrentSystemStatus',
      Label: '剩余电量',
      Unit: 'kwh',
    },
    SOC: {
      Name: 'CurrentSystemStatus',
      Label: 'SoC',
      Unit: '%',
      ViewType: 'progressAndLabel',
    },
    Custom: [
      {
        Name: 'CurrentSystemStatus',
        Label: '装机容量',
        Unit: 'kwh',
      },
      {
        Name: 'CurrentSystemStatus',
        Label: '今日充电量',
        Unit: 'kwh',
      },
      {
        Name: 'CurrentSystemStatus',
        Label: '今日放电量',
        Unit: 'kwh',
      },
      {
        Name: 'CurrentSystemStatus',
        Label: '累计充电量',
        Unit: 'kwh',
      },
      {
        Name: 'CurrentSystemStatus',
        Label: '累计放电量',
        Unit: 'kwh',
      },
      {
        Name: 'BMSStatus',
        Label: 'BMS',
        Unit: '',
        device: 'BMS',
      },
      {
        Name: 'PCSStatus',
        Label: 'PCS',
        Unit: '',
        device: 'PCS',
      },
      {
        Name: 'lenquestatus',
        Label: '冷却设备',
        Unit: '',
        ViewType: 'Content',
        device: 'TemperContr',
      },
      {
        Name: 'ChargingStatus',
        Label: '充电桩',
        Unit: '',
        device: 'ChargeGun',
      },
      {
        Name: 'AlarmCount',
        Label: '告警',
        fontColor: 'red',
        Unit: '',
      },
    ],
  },
  // Content:{
  //   ViewType: 'Switch', //Tile/Switch
  // },
  Input: {
    Label: '补能',
    Content: {
      DCInput: {
        Label: '直流补能',
        ViewType: 'Show',
        VIN: {
          Label: 'vin码',
          ViewType: 'SingleSelect',
          Name: 'VINCode',
        },
        DCInputGroup: [
          {
            Label: '1#电池组',
            DeviceID: 'ZS2',
            Name: 'BMSCurrentState',
            LabelGoup: [
              {
                Unit: 'V',
                Label: '电压',
                ViewType: 'Label',
                DeviceID: 'ZS1',
                Name: 'VoltageOutputValue',
              },
              {
                Unit: 'A',
                Label: '电流',
                ViewType: 'Label',
                Name: 'CurrentOutputValue',
              },
              {
                Unit: 'Kw',
                Label: '功率',
                ViewType: 'Label',
                Name: 'DCInputBatteryPower',
              },
              {
                Unit: '秒',
                Label: '本次补能时长',
                ViewType: 'Label',
                type: 'duration',
                Name: 'CumulativeChargingTime',
              },
            ],
            InputPowerGoups: [
              {
                Title: '1#',
                Unit: '',
                Label: '',
                Image: 'Smile.png',
                ViewType: 'Content',
                Name: 'Charge_gun_position_detection1',
                Button: {
                  ButtonLabel: '补能复位',
                  API: 'url',
                  Type: 'Post',
                  RequestPara: '',
                },
              },
              {
                Title: '2#',
                Unit: '',
                Label: 'Status',
                Image: 'Smile.png',
                ViewType: 'Content',
                Name: 'Charge_gun_position_detection2',
                Button: {
                  ButtonLabel: '补能复位',
                  API: 'url',
                  Type: 'Post',
                  RequestPara: '',
                },
              },
            ],
            StatusFlag: {
              Name: 'progress',
              StatusType: 'progressStep',
              ErrorMsg: '',
              WarnMsg: '',
              CurrentMsg: '',
              Status: 0,
              Step: 'PCS待机',
              DCDetailMsg: {
                AddressName: '点位名称',
                Unit: '',
                Descript: '',
              },
            },
          },
        ],
      },
      ACInput: [
        {
          Label: '交流补能',
          ViewType: 'Show',
          DeviceID: 'PCSOverview',
          LabelGoup: [
            {
              Label: '网侧电压',
              Children: [
                {
                  Label: 'Uab',
                  Unit: 'V',
                  Name: 'Grid_AB_V',
                },
                {
                  Label: 'Ubc',
                  Unit: 'V',
                  Name: 'Grid_BC_V',
                },
                {
                  Label: 'Uca',
                  Unit: 'V',
                  Name: 'Grid_CA_V',
                },
              ],
            },
            {
              Label: '网侧电流',
              Children: [
                {
                  Label: 'Ia',
                  Unit: 'A',
                  Name: 'Grid_PhaseA_I',
                },
                {
                  Label: 'Ib',
                  Unit: 'A',
                  Name: 'Grid_PhaseB_I',
                },
                {
                  Label: 'Ic',
                  Unit: 'A',
                  Name: 'Grid_PhaseC_I',
                },
              ],
            },
            {
              Name: 'Grid_Freq',
              Label: '网侧频率',
              Unit: 'Hz',
            },
            {
              Name: 'Load_Rate',
              Label: '负载率',
              Unit: '%',
            },
            {
              Name: 'Apparent_Power',
              Label: '视在功率',
              Unit: 'KVA',
            },
            {
              Name: 'Active_Power',
              Label: '有功功率',
              Unit: 'Kw',
            },
            {
              Name: 'CurrentAllPCSMaxOutPower',
              Label: '输出功率',
              Unit: 'Kw',
              DeviceID: 'ComputerDevice1',
            },
            {
              Name: 'Battery_Current_BatI',
              Label: '电池电流',
              Unit: 'A',
            },
          ],
          MultiWriteItems: {
            Label: '',
            DeviceID: 'PCS1',
            Group: [
              // {
              //   Name: 'Less0',
              //   Type: 'INT',
              //   Title: '并网放电',
              //   Descript: '放电功率',
              //   Unit: '',
              //   Min: 1,
              //   Max: 99999999999,
              //   ApiType: 'Post',
              //   Api: '/EMS/ACGrid/OutPower/Start',
              //   Value: {
              //     type: 'acgridout',
              //     deviceID: 'Dock1',
              //     gunID: '',
              //     userInputPara: 200,
              //   },
              // },
              {
                Name: 'Greater0',
                Type: 'INT',
                Descript: '补能功率(Kw)',
                Title: '交流补能',
                Unit: '',
                Min: -999999999,
                Max: 0,
                DefaultValue: -220,
                // 开始供电时才需要
                // ApiType: 'Post',
                // Api: '/EMS/AC/InputPower/Start',
                // Value: {
                //   type: 'acinput',
                //   deviceID: 'Dock1',
                //   gunID: '',
                //   userInputPara: -220,
                // },
              },
            ],
          },
          ButtonItems: {
            Label: '',
            Items: [
              {
                Label: '',
                // 开始补能需要的传参
                Value: {
                  // type: 'acinput',
                  // deviceID: 'Dock1',
                  // gunID: '',
                  userInputPara: '{Greater0}',
                },
                Group: [
                  {
                    Name: 'start',
                    Value: 1,
                    Descript: "开始+前面的'Title'内容",
                  },
                  {
                    Name: 'stop',
                    Value: 2,
                    Descript: "停止+前面的'Title'内容",
                  },
                ],
              },
            ],
          },
          StatusFlag: {
            Name: 'progress',
            StatusType: 'progressStep',
            ErrorMsg: '',
            WarnMsg: '',
            CurrentMsg: '',
            Status: 0,
            Step: 'PCS待机',
          },
        },
      ],
    },
  },
  Output: {
    Label: '供电',
    Content: {
      ACOut: [
        {
          Label: '交流供电',
          ViewType: 'Show',
          DeviceID: 'PCSOverview',
          LabelGoup: [
            {
              Label: '网侧电压',
              Children: [
                {
                  Label: 'Uab',
                  Unit: 'V',
                  Name: 'Grid_AB_V',
                },
                {
                  Label: 'Ubc',
                  Unit: 'V',
                  Name: 'Grid_BC_V',
                },
                {
                  Label: 'Uca',
                  Unit: 'V',
                  Name: 'Grid_CA_V',
                },
              ],
            },
            {
              Label: '网侧电流',
              Children: [
                {
                  Label: 'Ia',
                  Unit: 'A',
                  Name: 'Grid_PhaseA_I',
                },
                {
                  Label: 'Ib',
                  Unit: 'A',
                  Name: 'Grid_PhaseB_I',
                },
                {
                  Label: 'Ic',
                  Unit: 'A',
                  Name: 'Grid_PhaseC_I',
                },
              ],
            },
            {
              Name: 'Grid_Freq',
              Label: '网侧频率',
              Unit: 'Hz',
            },
            {
              Name: 'Load_Rate',
              Label: '负载率',
              Unit: '%',
            },
            {
              Name: 'Apparent_Power',
              Label: '视在功率',
              Unit: 'KVA',
            },
            {
              Name: 'Active_Power',
              Label: '有功功率',
              Unit: 'Kw',
            },
            {
              Name: 'CurrentAllPCSMaxOutPower',
              Label: '输出功率',
              Unit: 'Kw',
              DeviceID: 'ComputerDevice1',
            },
            // {
            // 	"Name": "CurrentAllPCSOutPower",
            // 	"Label": "实时输出功率",
            // 	"Unit": "Kw",
            // 	"DeviceID": "ComputerDevice1"
            // },
            {
              Name: 'Battery_Current_BatI',
              Label: '电池电流',
              Unit: 'A',
            },
          ],
          ButtonItems: {
            Label: '',
            Items: [
              {
                Label: '',
                Group: [
                  {
                    Name: 'start',
                    Value: 1,
                    Descript: "开始+前面的'Title'内容",
                  },
                  {
                    Name: 'stop',
                    Value: 2,
                    Descript: "停止+前面的'Title'内容",
                  },
                ],
              },
            ],
          },
          StatusFlag: {
            Name: 'progress',
            StatusType: 'progressStep',
            ErrorMsg: '',
            WarnMsg: '',
            CurrentMsg: '',
            Status: 0,
            Step: 'PCS待机',
          },
        },
      ],
      ACDCOut: {
        Label: '直流放电',
        ViewType: 'Hide',
        ACDCOutGroup: [
          {
            Label: '1#充电桩',
            ViewType: 'Show',
            DeviceID: 'ZLAN1',
            LabelGoup: [
              {
                Label: 'A枪',
                Children: [
                  {
                    Name: 'Electronic_Lock_Signal',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV1',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC1',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP1',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC1',
                    Label: '时长',
                    Unit: 'Min',
                  },
                ],
                ButtonItems: {
                  Label: '',
                  Items: [
                    {
                      Label: '',
                      Group: [
                        {
                          Name: 'SSC1',
                          Value: 1,
                          Descript: '开始充电',
                        },
                        {
                          Name: 'SSC1',
                          Value: 2,
                          Descript: '结束充电',
                        },
                      ],
                    },
                  ],
                },
              },
              {
                Label: 'B枪',
                Children: [
                  {
                    Name: 'CC1CS2',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV2',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC2',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP2',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC2',
                    Label: '时长',
                    Unit: 'Min',
                  },
                ],
                ButtonItems: {
                  Label: '',
                  Items: [
                    {
                      Label: '',
                      Group: [
                        {
                          Name: 'SSC2',
                          Value: 1,
                          Descript: '开始充电',
                        },
                        {
                          Name: 'SSC2',
                          Value: 2,
                          Descript: '结束充电',
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
          /*  {
            Label: '2#充电桩',
            ViewType: 'Show',
            DeviceID: 'ZLAN1',
            LabelGoup: [
              {
                Label: 'A枪',
                Children: [
                  {
                    Name: 'Electronic_Lock_Signal',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV1',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC1',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP1',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC1',
                    Label: '时长',
                    Unit: 'Min',
                  },
                ],
                ButtonItems: {
                  Label: '',
                  Items: [
                    {
                      Label: '',
                      Group: [
                        {
                          Name: 'SSC1',
                          Value: 1,
                          Descript: '开始充电',
                        },
                        {
                          Name: 'SSC1',
                          Value: 2,
                          Descript: '结束充电',
                        },
                      ],
                    },
                  ],
                },
              },
              {
                Label: 'B枪',
                Children: [
                  {
                    Name: 'CC1CS2',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV2',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC2',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP2',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC2',
                    Label: '时长',
                    Unit: 'Min',
                  },
                ],
                ButtonItems: {
                  Label: '',
                  Items: [
                    {
                      Label: '',
                      Group: [
                        {
                          Name: 'SSC2',
                          Value: 1,
                          Descript: '开始充电',
                        },
                        {
                          Name: 'SSC2',
                          Value: 2,
                          Descript: '结束充电',
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
          {
            Label: '3#充电桩',
            ViewType: 'Show',
            DeviceID: 'ZLAN1',
            LabelGoup: [
              {
                Label: 'A枪',
                Children: [
                  {
                    Name: 'Electronic_Lock_Signal',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV1',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC1',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP1',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC1',
                    Label: '时长',
                    Unit: 'Min',
                  },
                ],
                ButtonItems: {
                  Label: '',
                  Items: [
                    {
                      Label: '',
                      Group: [
                        {
                          Name: 'SSC1',
                          Value: 1,
                          Descript: '开始充电',
                        },
                        {
                          Name: 'SSC1',
                          Value: 2,
                          Descript: '结束充电',
                        },
                      ],
                    },
                  ],
                },
              },
              {
                Label: 'B枪',
                Children: [
                  {
                    Name: 'CC1CS2',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV2',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC2',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP2',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC2',
                    Label: '时长',
                    Unit: 'Min',
                  },
                ],
                ButtonItems: {
                  Label: '',
                  Items: [
                    {
                      Label: '',
                      Group: [
                        {
                          Name: 'SSC2',
                          Value: 1,
                          Descript: '开始充电',
                        },
                        {
                          Name: 'SSC2',
                          Value: 2,
                          Descript: '结束充电',
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
          {
            Label: '4#充电桩',
            ViewType: 'Show',
            DeviceID: 'ZLAN1',
            LabelGoup: [
              {
                Label: 'A枪',
                Children: [
                  {
                    Name: 'Electronic_Lock_Signal',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV1',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC1',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP1',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC1',
                    Label: '时长',
                    Unit: 'Min',
                  },
                ],
                ButtonItems: {
                  Label: '',
                  Items: [
                    {
                      Label: '',
                      Group: [
                        {
                          Name: 'SSC1',
                          Value: 1,
                          Descript: '开始充电',
                        },
                        {
                          Name: 'SSC1',
                          Value: 2,
                          Descript: '结束充电',
                        },
                      ],
                    },
                  ],
                },
              },
              {
                Label: 'B枪',
                Children: [
                  {
                    Name: 'CC1CS2',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV2',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC2',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP2',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC2',
                    Label: '时长',
                    Unit: 'Min',
                  },
                ],
                ButtonItems: {
                  Label: '',
                  Items: [
                    {
                      Label: '',
                      Group: [
                        {
                          Name: 'SSC2',
                          Value: 1,
                          Descript: '开始充电',
                        },
                        {
                          Name: 'SSC2',
                          Value: 2,
                          Descript: '结束充电',
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          }, */
        ],
      },
      DCDCOut: {
        Label: '直流供电',
        ViewType: 'Hide',
        DCDCOutGroup: [
          {
            Label: '充电枪',
            ViewType: 'Show',
            DeviceID: 'ZLAN1',
            LabelGoup: [
              {
                Label: 'A枪',
                Children: [
                  {
                    Name: 'Electronic_Lock_Signal',
                    Label: '连接状态',
                    Unit: '',
                  },
                  {
                    Name: 'CCV1',
                    Label: '电压',
                    Unit: 'V',
                  },
                  {
                    Name: 'CCC1',
                    Label: '电流',
                    Unit: 'A',
                  },
                  {
                    Name: 'CCP1',
                    Label: '功率',
                    Unit: 'kw',
                  },
                  {
                    Name: 'CDTC1',
                    Label: '供电量',
                    Unit: 'KW',
                  },
                  {
                    Name: 'CDTC1',
                    Label: '供电时长',
                    Unit: 'Min',
                  },
                ],
              },
            ],
            MultiWriteItems: {
              Label: '',
              DeviceID: 'PCS1',
              Group: [
                {
                  Name: 'DCDC_Out_MaxCurrent',
                  Type: 'INT',
                  Descript: '最大输出电流(Kw)',
                  Title: '最大输出电流',
                  Unit: '',
                  Min: 0,
                  Max: 999999,
                  ApiType: 'Post',
                  // 配置当前API会在写值后执行写命令(目前仅支持当前api)
                  Api: '/api/RootInterface/WriteCommand',
                  Value: {
                    msgId: '', // 消息ID
                    deviceCode: 'PCS1', // 设备ID
                    userInputPara: 200,
                    addresses: [
                      {
                        name: 'DCDC_Out_MaxCurrent', // 点位名称
                        value: '', //点位值，输入为准，可以是字符串，数字，浮点型
                      },
                    ],
                  },
                },
              ],
            },
            ButtonItems: {
              Label: '',
              Items: [
                {
                  Label: '',
                  Group: [
                    {
                      Name: 'SSC1',
                      Value: 1,
                      Descript: '开始充电',
                    },
                    {
                      Name: 'SSC1',
                      Value: 2,
                      Descript: '结束充电',
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
    // 江西环境使用交流供电启停函数
    // ButtonItems: {
    //   Label: '',
    //   optionType: 'acOut', //针对江西环境
    //   Items: [
    //     {
    //       Label: '',
    //       Group: [
    //         {
    //           Name: 'SSC1',
    //           Value: 1,
    //           Descript: '开始充电',
    //         },
    //         {
    //           Name: 'SSC1',
    //           Value: 2,
    //           Descript: '结束充电',
    //         },
    //       ],
    //     },
    //   ],
    // },
  },
  GridOut: {
    Label: '放电',
    ViewType: 'Show',
    Content: {
      ACGridOut: [
        {
          Label: '并网放电',
          ViewType: 'Show',
          DeviceID: 'PCSOverview',
          LabelGoup: [
            {
              Label: '网侧电压',
              Children: [
                {
                  Label: 'Uab',
                  Unit: 'V',
                  Name: 'Grid_AB_V',
                },
                {
                  Label: 'Ubc',
                  Unit: 'V',
                  Name: 'Grid_BC_V',
                },
                {
                  Label: 'Uca',
                  Unit: 'V',
                  Name: 'Grid_CA_V',
                },
              ],
            },
            {
              Label: '网侧电流',
              Children: [
                {
                  Label: 'Ia',
                  Unit: 'A',
                  Name: 'Grid_PhaseA_I',
                },
                {
                  Label: 'Ib',
                  Unit: 'A',
                  Name: 'Grid_PhaseB_I',
                },
                {
                  Label: 'Ic',
                  Unit: 'A',
                  Name: 'Grid_PhaseC_I',
                },
              ],
            },
            {
              Name: 'Grid_Freq',
              Label: '网侧频率',
              Unit: 'Hz',
            },
            {
              Name: 'Load_Rate',
              Label: '负载率',
              Unit: '%',
            },
            {
              Name: 'Apparent_Power',
              Label: '视在功率',
              Unit: 'KVA',
            },
            {
              Name: 'Active_Power',
              Label: '有功功率',
              Unit: 'Kw',
            },
            {
              Name: 'CurrentAllPCSMaxOutPower',
              Label: '输出功率',
              Unit: 'Kw',
              DeviceID: 'ComputerDevice1',
            },
            {
              Name: 'Battery_Current_BatI',
              Label: '电池电流',
              Unit: 'A',
            },
          ],
          MultiWriteItems: {
            Label: '',
            Group: [
              {
                Name: 'Less0',
                Type: 'INT',
                Title: '并网放电',
                Descript: '放电功率',
                Unit: '',
                Min: 1,
                Max: 99999999999,
                // ApiType: 'Post',
                // Api: '/EMS/ACGrid/OutPower/Start',
                // Value: {
                //   type: 'acgridout',
                //   deviceID: 'Dock1',
                //   gunID: '',
                //   userInputPara: 200,
                // },
              },
            ],
          },
          ButtonItems: {
            Label: '',
            Items: [
              {
                Label: '',
                Group: [
                  {
                    Name: 'start',
                    Value: 1,
                    Descript: "开始+前面的'Title'内容",
                  },
                  {
                    Name: 'stop',
                    Value: 2,
                    Descript: "停止+前面的'Title'内容",
                  },
                ],
              },
            ],
          },
          StatusFlag: {
            Name: 'progress',
            StatusType: 'progressStep',
            ErrorMsg: '',
            WarnMsg: '',
            CurrentMsg: '',
            Status: 0,
            Step: 'PCS待机',
          },
        },
      ],
    },
  },
};
