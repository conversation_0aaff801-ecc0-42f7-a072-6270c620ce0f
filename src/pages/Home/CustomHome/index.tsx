import { useEffect, useState } from 'react';
import useRealTime from '../useRealTime';
import ContentLayout from '../components/ContentLayout';
import { config } from './config';
import { HeaderSection } from '../components/HeaderSection';
import { useDeviceStore } from '@/store/deviceStore';
import { useConfigStore } from '@/store/configStore';
interface DetailData {
  [key: string]: any;
}
export const HomeScreenPage = () => {
  const { data } = useRealTime();
  const [detailData, setDetailData] = useState<DetailData>({});
  const { setConfig } = useConfigStore();

  const { setGpsDevice, setCellularDevice, setSystemStatus } = useDeviceStore();

  useEffect(() => {
    // 更新设备状态
    if (data.Devices?.length > 0) {
      const gpsDevice = data.Devices.find((d: Record<string, any>) => d.device.type === 'GPS');
      const cellularDevice = data.Devices.find((d: Record<string, any>) => d.device.type === '4G');
      setGpsDevice(gpsDevice);
      setCellularDevice(cellularDevice);
    }
    if (data) {
      setSystemStatus(data.CurrentSystemStatus);
    }
  }, [data]);

  useEffect(() => {
    setConfig(config);
  }, [config]);

  return (
    <div className="flex w-full flex-row justify-center bg-[#001857] p-4 pb-1">
      <div className="relative w-[1280px] overflow-hidden bg-[#001857]">
        <div className="relative w-full">
          <HeaderSection data={data} config={config} />
        </div>
        <ContentLayout ViewType={config.ViewType} config={config} data={data}></ContentLayout>
      </div>
    </div>
  );
};
