import { Card, CardContent } from '@/components/ui/card';
import { httpPost } from '@/shared/http';
import { formatterValue } from '@/utils';
import { formatMilliseconds } from '@/utils/formatDate';
import { toFixed } from '@/utils/number';
import { message, Modal } from 'antd';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';

const BatteryCard = ({
  data = {},
  BatteryGroupSOC,
  index,
  acInputStatus,
  type = 'A',
}: any): JSX.Element => {
  const [soc, setSoc] = useState<any>(0);
  const [detail, setDetail] = useState<any>({});
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const info = data[type] || {};
  const topPosition = type === 'A' ? 'top-11' : 'top-[192px]'; // B枪位置 = A枪top位置(44px) + A枪高度(143px) - 22px
  const borderColor = info?.status == 1 ? 'outline-sky-400' : 'outline-[#4365A9]';
  const onlineBgColor = info?.status == 1 ? 'bg-green-500' : 'bg-[#4365A9]';
  const titleColor = info?.status == 1 ? 'text-sky-400' : 'text-[#4365A9]';
  const onlineTextColor = info?.status == 1 ? 'text-green-500' : 'text-[#4365A9]';
  const [dcChargingDockData, setDCChargingDockData] = useState<any>({});
  const [dcInputDetail, setDcInputDetail] = useState<any>({});
  const [chargingData, setChargingData] = useState<any>({});

  useEffect(() => {
    if (dcChargingDockData?.itemList?.length > 0) {
      const obj: any = {};
      for (const item of dcChargingDockData.itemList) {
        /*    if (item.name?.indexOf('BatteryGroupSOC') === 0) {
             setSoc(toFixed(item.value));
           } */
        obj[item.name] = item.value;
      }
      setDcInputDetail(obj);
    }
  }, [JSON.stringify(dcChargingDockData)]);

  useEffect(() => {
    setSoc(toFixed(BatteryGroupSOC || 0));
  }, [BatteryGroupSOC]);

  useEffect(() => {
    if (data?.itemList?.length > 0) {
      const obj: any = {};
      for (const item of data.itemList) {
        if (item.name?.indexOf('BatteryGroupSOC') === 0) {
          setSoc(toFixed(item.value));
        }
        obj[item.name] = item.value;
      }
      setDetail(obj);
    }
  }, [JSON.stringify(data)]);

  useEffect(() => {
    if (data.Devices?.length > 0) {
      // 电池组
      for (const d of data.Devices) {
        if (d.device.deviceID === 'DcChargingDock1') {
          setDCChargingDockData(d);
        } /*  else if (d.device.deviceID === 'DcChargingDock2') {
          setDCChargingDockData2(d);
        } else if (d.device.deviceID === 'PCS1') {
          const dcObj: any = { device: d.device, itemList: [] };
          for (const item of d.itemList) {
            if (['Grid_PhaseA_V', 'Grid_PhaseB_V', 'Grid_PhaseC_V', 'Grid_PhaseA_I', 'Grid_PhaseB_I', 'Grid_PhaseC_I'].includes(item.name)) {
              dcObj.itemList.push(item);
            }
          }
          // list  根据label 排序
          dcObj.itemList?.sort((a: any, b: any) => a.description?.localeCompare?.(b.description));
          // setDcPcsData(dcObj);
        } */
        if (d.device.deviceID === 'ZS2') {
          const deviceData: any = {};
          for (const item of d.itemList) {
            if (
              ['Charge_gun_position_detection1', 'Charge_gun_position_detection2'].includes(
                item.name,
              )
            )
              deviceData[item.name] = item;
          }
          setChargingData(deviceData);
        }
      }
    }
  }, [data?.Devices]);

  const status = useMemo(() => {
    return (
      !!chargingData?.['Charge_gun_position_detection1']?.value &&
      chargingData?.['Charge_gun_position_detection1']?.value < 18
    );
    // return data?.device?.status === 'Connect';
  }, [data?.device]);

  const dcCharging = useMemo(() => {
    return acInputStatus == 2 || acInputStatus == 1;
  }, [acInputStatus]);

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  // 充电座补能复位
  const operateDcStop = useCallback(
    (deviceId: string) => {
      // if (!dcCharging) {
      //   message.warning('当前状态不是补能中，无法补能复位');
      //   return;
      // }

      if (isOperating) {
        message.warning('操作冷却中，请稍后再试');
        return;
      }

      Modal.confirm({
        title: '操作确认',
        content: `是否要操作补能复位？`,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          try {
            // 清除之前的定时器（如果有的话）
            clearOperatingTimeout();
            setIsOperating(true);
            const res = await httpPost('EMS/DCInputPower/Stop', {
              chargingDockId: deviceId || 'DcChargingDock1', //充电座的充电口id
            });
            if (res.result == 'successful') {
              message.info('补能复位操作成功！');
              // 成功后3秒冷却时间
              timeoutRef.current = setTimeout(() => {
                setIsOperating(false);
                timeoutRef.current = null;
              }, 3000);
            } else {
              message.error(res?.resultInfo || '补能复位操作失败！');
              // 失败后立即可以再次点击
              setIsOperating(false);
            }
          } catch (error) {
            message.error('操作失败，请重试');
            // 异常后立即可以再次点击
            setIsOperating(false);
          }
        },
      });
    },
    [dcCharging, isOperating, clearOperatingTimeout],
  );
  const dcInputDetailMetrics = [
    { label: '电压', unit: 'V', field: 'VoltageOutputValue' },
    { label: '电流', unit: 'A', field: 'CurrentOutputValue' },
    { label: '功率', unit: 'kw', field: 'DCInputBatteryPower' },
    // { label: '本次供电量', unit: 'kwh', field: 'DCDC_ThisOut_Energy' }, //点位 DCDC_ThisOut_Energy
    {
      label: '本次供电时长',
      unit: '',
      field: 'CumulativeChargingTime',
      formatter: (v = 0) => formatMilliseconds(Number(v) * 60 * 1000),
    },
  ];

  return (
    <div className="relative w-full max-w-[430px]">
      <div className="relative h-full w-full">
        {/* Background and corner images */}
        {/* Battery group header */}
        <div className="absolute left-1/2 top-0 h-[40px] w-[260px] -translate-x-1/2">
          <div
            className="relative flex h-[40px] w-[260px] items-center"
            style={{ backgroundImage: "url('/home/<USER>')", backgroundRepeat: 'no-repeat' }}
          >
            {/* <img className="absolute left-[3px] top-0.5 h-[33px] w-[149px]" alt="Vector" src="/home/<USER>" /> */}

            <div className="w-full text-center text-xl font-medium leading-normal tracking-[0] text-[#39abff] [font-family:'PingFang_SC-Medium',Helvetica]">
              充电枪
            </div>
            <div
              className="absolute right-[40px] top-[15px] h-[9px] w-[9px] rounded-[4.5px] bg-[#858585]"
              style={{ backgroundColor: status ? '#00ff50' : '#858585' }}
            />
          </div>
        </div>
      </div>
      <Card
        className="absolute left-[2px] top-[2px] h-[397px] w-full border-0"
        style={{ backgroundImage: "url('/home/<USER>')", backgroundRepeat: 'no-repeat' }}
      >
        <CardContent className="p-0">
          <div
            className={`absolute left-8 inline-flex h-[336px] w-[371px] flex-col items-start justify-start gap-1.5 self-stretch px-3.5 pb-1 pt-2 outline outline-[3px] outline-offset-[-3px] ${borderColor} ${topPosition}`}
          >
            <div className="inline-flex items-center justify-start gap-1 self-stretch">
              <div className="flex items-center justify-start gap-2.5" />
              <div className="flex flex-1 items-center justify-start gap-2.5">
                <img
                  src={info?.status == 1 ? '/home/<USER>' : '/home/<USER>'}
                  alt="Icon"
                />
                <div
                  className={`ml-[-6px] justify-start font-['PingFang_SC'] text-xl font-semibold leading-snug ${titleColor}`}
                >
                  充电枪
                </div>
              </div>
              <div className="flex items-center justify-start gap-1">
                <div className={`rounded-full ${onlineBgColor}`} />
                <div className="flex items-center justify-start gap-2.5 rounded-[10px]">
                  <div
                    className="h-[6px] w-[6px] rounded-[4.5px] bg-[#858585]"
                    style={{ backgroundColor: status ? '#00ff50' : '#858585' }}
                  />
                  <div
                    className={`justify-start font-['PingFang_SC'] text-xl leading-none ${onlineTextColor}`}
                  >
                    {status ? '已连接' : '未连接'}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-start justify-start gap-6 self-stretch pt-1">
              {dcInputDetailMetrics.map((metric, index) => (
                <div
                  className="flex flex-1 items-end justify-start gap-[3px]"
                  key={`card-${index}`}
                >
                  <div className="flex items-center justify-start gap-2.5">
                    <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-[#3270A5]">
                      {metric.label}
                    </div>
                  </div>
                  <div className="flex items-end justify-start">
                    <div className="flex items-center justify-start gap-2.5">
                      <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
                        {/* {metric.formatter?.(info[metric.field]) || info[metric.field]} */}
                        {formatterValue(
                          { value: dcInputDetail[metric.field], formatter: metric.formatter },
                          metric.unit,
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BatteryCard;
