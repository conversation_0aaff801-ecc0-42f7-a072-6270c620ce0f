import { Separator } from '@/components/ui/separator';
import { useCallback, useEffect, useState } from 'react';
import { StatusSection } from '../WrapperScreen/StatusSection';
import BuNengSection from './BuNengSection';
import GongDianSection from './GongDianSection';
import useRealTime from '../useRealTime';
import { useDeviceStore } from '@/store/deviceStore';

export const HomeScreenPage = (): JSX.Element => {
  const { data, isConnected = false } = useRealTime();
  const { setGpsDevice, setCellularDevice, setSystemStatus } = useDeviceStore();
  const [activeTab, setActiveTab] = useState<'buneng' | 'gongdian'>('buneng');

  useEffect(() => {
    console.info?.('🚀 济南');
    // 更新设备状态
    if (data.Devices?.length > 0) {
      const gpsDevice = data.Devices.find((d: Record<string, any>) => d.device.type === 'GPS');
      const cellularDevice = data.Devices.find((d: Record<string, any>) => d.device.type === '4G');
      setGpsDevice(gpsDevice);
      setCellularDevice(cellularDevice);
    }
    if (data) {
      setSystemStatus(data.CurrentSystemStatus);
    }
  }, [data]);

  const tabChange = useCallback((tab: 'buneng' | 'gongdian') => {
    setActiveTab(tab);
  }, []);

  return (
    <div className="flex w-full flex-row justify-center bg-[#001857] p-4 pb-1">
      <div className="relative w-[1280px] overflow-hidden bg-[#001857]">
        {/* Header Section */}
        <div className="relative w-full">
          <StatusSection data={data} />
        </div>
        {/* Main Content */}
        <div className="relative mt-4 w-full">
          <div className="justify-centerpb-0 relative flex w-full items-center px-0 [background:linear-gradient(0deg,rgba(23,63,129,0)_0%,rgba(21,94,216,0.4)_100%)]">
            {/* 透明点击区域 */}
            <div className="absolute left-0 top-0 mr-6 flex h-[48px] w-full justify-center">
              <div
                onClick={() => tabChange('buneng')}
                className="z-10 h-full w-[155px] cursor-pointer"
              />
              <div
                onClick={() => tabChange('gongdian')}
                className="z-10 h-full w-[155px] cursor-pointer"
              />
            </div>

            {/* 背景图层 */}
            <div
              className={`h-[48px] w-[1247px] transition-opacity duration-300 ${activeTab === 'buneng' ? 'opacity-100' : 'hidden'}`}
              style={{ backgroundImage: `url(/home/<USER>'1226px 48px' }}
            />
            <div
              className={`h-[48px] w-[1226px] transition-opacity duration-300 ${activeTab === 'gongdian' ? 'opacity-100' : 'hidden'}`}
              style={{ backgroundImage: `url(/home/<USER>'1226px 48px' }}
            />
          </div>
          {activeTab === 'buneng' ? (
            <BuNengSection activeTab={activeTab} data={data} isConnected={isConnected} />
          ) : (
            <GongDianSection activeTab={activeTab} data={data} isConnected={isConnected} />
          )}
        </div>

        <Separator className="mt-1 h-[3px] w-full [background:linear-gradient(90deg,rgba(44,223,232,1)_0%,rgba(26,123,219,1)_49%,rgba(219,255,0,1)_100%),linear-gradient(0deg,rgba(217,217,217,1)_0%,rgba(217,217,217,1)_100%)]" />
      </div>
    </div>
  );
};
