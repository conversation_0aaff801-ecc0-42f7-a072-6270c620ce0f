import React, { useRef } from 'react';
import { getStatusIcon } from '../utils';
import { debounce } from 'lodash';
interface PowerSupplyStateProps {
  title?: string;
  status?: number;
  type?:'buneng'|'gongdian';
  onClick?:()=>void;
}
const PowerSupplyState = (props: PowerSupplyStateProps) => {
  const { title = '直流供电状态', status = 0, type='buneng',onClick=()=>{} } = props;
  const name={
    buneng:'补能',
    gongdian:'供电',
  }
  const debouncedOnClick = useRef(debounce(onClick, 5000, { leading: true, trailing: false })).current;

  return (
    <div onClick={debouncedOnClick}>
      <div className="flex items-center">
        <img className="relative h-5 w-5" alt="Frame" src="/home/<USER>" />
        {title}
      </div>
      <div className="order-0 flex-grow-1 flex h-[60px] w-[248px] items-center justify-between gap-[6px] bg-gradient-to-b from-[rgba(17,72,173,0.6)] to-[rgba(5,48,126,0)] px-[40px] shadow-[inset_0_0_8.34441px_rgba(0,166,255,0.8)]">
        <div>{status === 0 || !status ? `停止${name[type]}` : `${name[type]}中`}</div>
        <div
          className="h-[30px] w-[30px] bg-[100%_100%]"
          style={{
            backgroundImage: `url(${getStatusIcon(status)})`,
            backgroundSize: '100% 100%',
          }}
        />
      </div>
    </div>
  );
};

export default PowerSupplyState;
