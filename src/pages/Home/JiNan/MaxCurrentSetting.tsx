import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import storage from '@/shared/storage';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Input, Modal } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import Keyboard from 'react-simple-keyboard';
interface Props {
  value?: number;
  defaultValue?: number;
  min?: number;
  max?: number;
  disabled?: boolean;
  onPowerChange?: (power: number) => void;
  style?: React.CSSProperties;
  onSubmit?: (power: number) => void;
}
const MaxCurrentSetting = ({
  value,
  min = 1,
  max = 200,
  onPowerChange,
  disabled,
  style = {},
  onSubmit,
  defaultValue = 100,
}: Props): JSX.Element => {
  const [power, setPower] = useState<any>(value);
  const [error, setError] = useState<string>('');
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    if (value !== power) {
      setPower(value);
    }
  }, [value]);

  const handlePowerChange = useCallback(
    (value: string) => {
      const numValue = Number(value);
      setPower(value);
      if (isNaN(numValue)) {
        setError('请输入有效的数字');
        return;
      }
      if (numValue < min) {
        setError(`最小值不能小于 ${min}KW`);
        setPower(value);
        onPowerChange?.(min);
        return;
      }
      if (numValue > max) {
        setError(`最大功率不能超过 ${max}KW`);
        setPower(value);
        onPowerChange?.(max);
        return;
      }
      if (numValue <= 0) {
        setError('功率必须大于0');
        return;
      }
      setError('');

      onPowerChange?.(numValue);
    },
    [max],
  );
  const handleInputFocus = (e: any) => {
    e.preventDefault();
    if (!disabled) {
      setShowKeyboard(true);
      keyboardRef.current?.setInput('');
    }
  };
  const handleKeyboardInput = useCallback(
    (input: string) => {
      handlePowerChange(input);
    },
    [handlePowerChange],
  );
  const handleCancel = () => {
    // 取消时关闭键盘并恢复输入框值
    const _power = storage.get('dcdcPower') || defaultValue;
    handleKeyboardInput(_power);
    setShowKeyboard(false);
    setConfirmLoading(false);
    setConfirmVisible(false);
  };
  const handleConfirm = async () => {
    if (error) {
      return;
    }
    setConfirmLoading(true);
    try {
      await onSubmit?.(power);
    } catch (error) {
      console.log(error);
    } finally {
      setShowKeyboard(false);
      setConfirmLoading(false);
      setConfirmVisible(false);
    }
  };
  return (
    <div className="relative flex items-center gap-2">
      <div className="text-xl text-[#3270A5]">最大输出电流设置</div>
      <div className="flex-1">
        <Input
          className="cst border-input file:text-foreground placeholder:text-muted-foreground focus-visible:ring-ring flex h-[38px] w-full rounded-md border border-solid border-[#9ebdfa] px-3 py-1 text-base text-[#e3f8ff] shadow-sm transition-colors [background:transparent!important] file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:text-[#e3f8ff] disabled:opacity-50 md:text-sm"
          value={power}
          onFocus={handleInputFocus}
          disabled={disabled}
          allowClear
          placeholder="请输入电流设置"
          onChange={(e) => {
            const v = e.target.value;
            handlePowerChange(String(v));
          }}
          style={{
            color: '#e3f8ff',
          }}
        />
      </div>
      {error && <div className="z-1 absolute right-0 top-[35px] text-sm text-red-500">{error}</div>}

      {showKeyboard && (
        <div
          className="absolute bottom-14 left-0 z-50 w-full bg-[#1B53B7]"
          onTouchStart={(e) => e.stopPropagation()}
          onTouchMove={(e) => e.preventDefault()}
        >
          <div className="flex justify-end p-2">
            <Button
              type="text"
              size="large"
              // icon={<CloseOutlined />}
              onClick={handleCancel}
              style={{ color: '#fff',fontSize:'22px' }}
            >
              取消
            </Button>
          </div>
          <Keyboard
            keyboardRef={(r) => (keyboardRef.current = r)}
            layout={{
              default: ['1 2 3', '4 5 6', '7 8 9', '. 0 {bksp}', '{enter}'],
            }}
            display={{
              '{bksp}': '删除',
              '{enter}': '确认',
            }}
            input={power}
            onChange={handleKeyboardInput}
            onKeyPress={(button) => {
              // 处理删除按钮
              if (button === '{bksp}') {
                let newInput = power?.toString()?.slice(0, -1) || '';
                handlePowerChange(newInput);
              }
              if (button === '{enter}') {
                //  回车保存设置  有报错不允许保存
                if (error) {
                  return;
                }
                setShowKeyboard(false);
                setConfirmVisible(true);
              }
            }}
            theme="hg-theme-default custom-keyboard"
            // useTouchEvents={true} // 启用触摸事件支持
            useMouseEvents={true} // 启用鼠标事件
            disableCaretPositioning={true} // 禁用光标定位，避免触摸冲突
          />
        </div>
      )}

      {/* 操作确认 */}
      <ConfirmModal
        open={confirmVisible}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        confirmLoading={confirmLoading}
        content={`是否确认设置最大输出电流为 ${power}？`}
      />
    </div>
  );
};

export default MaxCurrentSetting;
