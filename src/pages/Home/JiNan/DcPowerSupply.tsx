import { CardContent } from '@/components/ui/card';
import { formatterValue } from '@/utils';
import { formatMilliseconds } from '@/utils/formatDate';
import { But<PERSON>, Card, Divider } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { getStatusText } from './GongDianSection';
import MaxCurrentSetting from './MaxCurrentSetting';
import storage from '@/shared/storage';

// 数组两两分组工具函数
const groupArrayIntoPairs = <T,>(arr: T[]): T[][] => {
  const result: T[][] = [];
  for (let i = 0; i < arr.length; i += 2) {
    result.push(arr.slice(i, i + 2));
  }
  return result;
};
// // 生成随机ID：时间戳 + 随机数
// const generateRandomId = () => {
//   const timestamp = new Date().getTime().toString();
//   const random = Math.random().toString(36).substr(2, 8);
//   return `${timestamp}-${random}`;
// }
const defaultMaxValue = 100;

const DcPowerSupply = ({
  allData = {},
  data = {},
  type = 'A',
  dcdcStatus,
  onButtonClick = () => {},
  handlePowerSubmit = () => {},
}: any) => {
  const [dcOutputDetail, setDcOutputDetail] = useState<any>({});
  const [maxPower, setMaxPower] = useState<number>();

  const dcOutputDetailMetrics = [
    { label: '电压', unit: 'V', field: 'DCDC_Out_V' },
    { label: '电流', unit: 'A', field: 'DCDC_Out_I' },
    { label: '功率', unit: 'kw', field: 'DCDC_Out_Power' },
    { label: '本次供电量', unit: 'kw', field: 'DCDC_ThisOut_Energy' },
    {
      label: '本次供电时长',
      unit: '',
      field: 'DCDC_ThisOut_Time',
      formatter: (v = 0) => formatMilliseconds(Number(v) * 60 * 1000),
    },
  ];

  useEffect(() => {
    const info = data[type] || {};
    setDcOutputDetail(info);
  }, [data, type]);
  // 默认值
  useEffect(() => {
    const _power = storage.get('dcdcPower') || defaultMaxValue;
    setMaxPower(_power);
  }, []);

  const handlePowerChange = (power: number) => {
    setMaxPower(power);
  };
  // const debouncedDCDCOperate = useRef(
  //   debounce(onButtonClick, 5000, { leading: true, trailing: false }),
  // ).current;

  const borderColor = dcdcStatus == 1 ? 'outline-sky-400' : 'outline-[#4365A9]';
  const onlineBgColor = dcdcStatus == 1 ? 'bg-green-500' : 'bg-[#4365A9]';
  const titleColor = dcdcStatus == 1 ? 'text-sky-400' : 'text-[#4365A9]';
  const onlineTextColor = dcdcStatus == 1 ? 'text-green-500' : 'text-[#4365A9]';
  const isPrepareOutput = useMemo(() => {
    return data.ACOutStatus == 1;
  }, [data.ACOutStatus]);
  return (
    <div className="relative w-full max-w-[430px]">
      <div className="relative h-full w-full">
        {/* Background and corner images */}
        {/* Battery group header */}
        <div className="absolute left-1/2 top-0 h-[40px] w-[260px] -translate-x-1/2">
          <div
            className="relative flex h-[40px] w-[260px] items-center"
            style={{ backgroundImage: "url('/home/<USER>')", backgroundRepeat: 'no-repeat' }}
          >
            {/* <img className="absolute left-[3px] top-0.5 h-[33px] w-[149px]" alt="Vector" src="/home/<USER>" /> */}

            <div className="w-full text-center text-xl font-medium leading-normal tracking-[0] text-[#39abff] [font-family:'PingFang_SC-Medium',Helvetica]">
              充电枪
            </div>
            <div
              className="absolute right-[40px] top-[15px] h-[9px] w-[9px] rounded-[4.5px] bg-[#858585]"
              style={{ backgroundColor: dcdcStatus === 1 ? '#00ff50' : '#858585' }}
            />
          </div>
        </div>
      </div>
      <Card
        className="absolute left-[2px] top-[2px] h-[397px] w-full border-0"
        style={{ backgroundImage: "url('/home/<USER>')", backgroundRepeat: 'no-repeat' }}
      >
        <CardContent className="p-0">
          <div
            className={`absolute left-8 inline-flex h-[336px] w-[371px] flex-col items-start justify-start gap-1.5 self-stretch px-3.5 pb-1 pt-2 outline outline-[3px] outline-offset-[-3px] ${borderColor} top-11`}
          >
            <div className="inline-flex items-center justify-start gap-1 self-stretch">
              <div className="flex items-center justify-start gap-2.5" />
              <div className="flex flex-1 items-center justify-start gap-2.5">
                <img
                  src={dcOutputDetail?.status == 1 ? '/home/<USER>' : '/home/<USER>'}
                  alt="Icon"
                />
                <div
                  className={`ml-[-6px] justify-start font-['PingFang_SC'] text-xl font-semibold leading-snug ${titleColor}`}
                >
                  充电枪
                </div>
              </div>
              <div className="flex items-center justify-start gap-1">
                <div className={`rounded-full ${onlineBgColor}`} />
                <div className="flex items-center justify-start gap-2.5 rounded-[10px]">
                  <div
                    className="h-[6px] w-[6px] rounded-[4.5px] bg-[#858585]"
                    style={{
                      backgroundColor: dcdcStatus === 1 ? '#00ff50' : '#858585',
                    }}
                  />
                  <div
                    className={`justify-start font-['PingFang_SC'] text-xl leading-none ${onlineTextColor}`}
                  >
                    {dcdcStatus === 1 ? '已连接' : '未连接'}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-full flex-wrap items-start justify-start gap-6 self-stretch pt-1">
              {groupArrayIntoPairs(dcOutputDetailMetrics).map((item, i) => (
                <div className="flex w-full justify-between gap-6" key={`DcPowerSupply${i}`}>
                  {item?.map((metric, index) => (
                    <div
                      key={`DcPowerSupply${i}-${index}`}
                      className="flex w-1/2 flex-1 items-end justify-start gap-[3px]"
                    >
                      <div className="flex items-center justify-start gap-2.5">
                        <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-[#3270A5]">
                          {metric.label}
                        </div>
                      </div>
                      <div className="flex items-end justify-start">
                        <div className="flex items-center justify-start gap-2.5">
                          <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
                            {/* {metric.formatter?.(info[metric.field]) || info[metric.field]} */}
                            {formatterValue(
                              { value: dcOutputDetail[metric.field] && metric.field ==='DCDC_Out_Power'? (Number(dcOutputDetail[metric.field]) / 1000).toFixed(2):dcOutputDetail[metric.field], formatter: metric.formatter },
                              metric.unit,
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
            <Divider style={{ backgroundColor: '#1C4DA0' }} />
            <MaxCurrentSetting
              value={maxPower}
              onPowerChange={handlePowerChange}
              onSubmit={handlePowerSubmit}
              defaultValue={defaultMaxValue}
            />
            <Button
              className="mt-3 h-[50px] w-full rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
              onClick={onButtonClick}
              disabled={isPrepareOutput}
            >
              <span
                className="whitespace-nowrap text-2xl font-normal leading-[18px] tracking-[0] text-[#81dc4a] [font-family:'PingFang_SC-Semibold',Helvetica]"
                style={{ color: allData.CurrentSystemStatus === 6 ? '#fe4545' : '' }}
              >
                {isPrepareOutput
                  ? '供电启动…'
                  : getStatusText(allData.CurrentSystemStatus === 6 ? 0 : 1)}
              </span>
            </Button>
          </div>
          <div></div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DcPowerSupply;
