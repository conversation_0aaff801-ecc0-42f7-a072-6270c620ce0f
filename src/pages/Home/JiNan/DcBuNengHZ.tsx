import { CardContent } from '@/components/ui/card';
import { Card, message, Modal } from 'antd';
import { formatMilliseconds } from '@/utils/formatDate';
import { formatterValue } from '@/utils';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { BatterySlots } from '@/components/BatterySlots';
import { httpPost } from '@/shared/http';

const dcInputDetailMetrics = [
  { label: '电压', unit: 'V', field: 'VoltageOutputValue' },
  { label: '电流', unit: 'A', field: 'CurrentOutputValue' },
  { label: '功率', unit: 'kw', field: 'DCInputBatteryPower' },
  // { label: "本次补能量", unit: "kw", field: "DCInputPowerCapacity" },
  {
    label: '本次补能时长',
    unit: '',
    field: 'CumulativeChargingTime',
    formatter: (v = 0) => formatMilliseconds(Number(v) * 60 * 1000),
  },
];

const DcBuNengHZ = ({ data }: { data: any }) => {
  const [dcInputDetail, setDcInputDetail] = useState<any>({});
  const [chargingData, setChargingData] = useState<any>({});
  const [dcChargingDockData, setDCChargingDockData] = useState<any>({});
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const [dcPcsData, setDcPcsData] = useState<any>({});

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const dcCharging = useMemo(() => {
    return data.DCInputStatus == 2 || data.DCInputStatus == 1;
  }, [data.DCInputStatus]);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);
  const operateDcStop = useCallback(
    (deviceId: string) => {
      if (!dcCharging) {
        message.warning('当前状态不是补能中，无法补能复位');
        return;
      }

      if (isOperating) {
        message.warning('操作冷却中，请稍后再试');
        return;
      }

      Modal.confirm({
        title: '操作确认',
        content: `是否要操作补能复位？`,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          try {
            // 清除之前的定时器（如果有的话）
            clearOperatingTimeout();
            setIsOperating(true);
            const res = await httpPost('EMS/DCInputPower/Stop', {
              chargingDockId: deviceId || 'DcChargingDock1', //充电座的充电口id
            });
            if (res.result == 'successful') {
              message.info('补能复位操作成功！');
              // 成功后3秒冷却时间
              timeoutRef.current = setTimeout(() => {
                setIsOperating(false);
                timeoutRef.current = null;
              }, 3000);
            } else {
              message.error(res?.resultInfo || '补能复位操作失败！');
              // 失败后立即可以再次点击
              setIsOperating(false);
            }
          } catch (error) {
            message.error('操作失败，请重试');
            // 异常后立即可以再次点击
            setIsOperating(false);
          }
        },
      });
    },
    [dcCharging, isOperating, clearOperatingTimeout],
  );

  useEffect(() => {
    if (dcChargingDockData?.itemList?.length > 0) {
      const obj: any = {};
      for (const item of dcChargingDockData.itemList) {
        /*    if (item.name?.indexOf('BatteryGroupSOC') === 0) {
             setSoc(toFixed(item.value));
           } */
        obj[item.name] = item.value;
      }
      setDcInputDetail(obj);
    }
  }, [JSON.stringify(dcChargingDockData)]);

  useEffect(() => {
    if (data.Devices?.length > 0) {
      // 电池组
      for (const d of data.Devices) {
        if (d.device.deviceID === 'DcChargingDock1') {
          setDCChargingDockData(d);
        } /* else if (d.device.deviceID === 'DcChargingDock2') {
          setDCChargingDockData2(d);
        }  */ else if (d.device.deviceID === 'PCS1') {
          const dcObj: any = { device: d.device, itemList: [] };
          for (const item of d.itemList) {
            if (
              [
                'Grid_PhaseA_V',
                'Grid_PhaseB_V',
                'Grid_PhaseC_V',
                'Grid_PhaseA_I',
                'Grid_PhaseB_I',
                'Grid_PhaseC_I',
              ].includes(item.name)
            ) {
              dcObj.itemList.push(item);
            }
          }
          // list  根据label 排序
          dcObj.itemList?.sort((a: any, b: any) => a.description?.localeCompare?.(b.description));
          setDcPcsData(dcObj);
        }
        if (d.device.deviceID === 'ZS2') {
          const deviceData: any = {};
          for (const item of d.itemList) {
            if (
              ['Charge_gun_position_detection1', 'Charge_gun_position_detection2'].includes(
                item.name,
              )
            )
              deviceData[item.name] = item;
          }
          setChargingData(deviceData);
        }
      }
    }
  }, [data?.Devices]);

  return (
    <Card className="relative h-[467px] w-full border-none">
      <CardContent className="p-0">
        <div className="relative h-[467px]">
          <img
            className="absolute left-[-3px] top-[3px] h-[434px] w-[383px]"
            alt="Rectangle"
            src="/home/<USER>"
          />

          {/* Corner decorations */}
          <img
            className="absolute left-[337px] top-[394px] h-[46px] w-[43px]"
            alt="Group"
            src="/home/<USER>"
          />
          <img
            className="absolute left-[337px] top-0 h-[46px] w-[42px]"
            alt="Group"
            src="/home/<USER>"
          />
          <img
            className="absolute left-0 top-0 h-[46px] w-[43px]"
            alt="Group"
            src="/home/<USER>"
          />
          <img
            className="absolute left-0 top-[394px] h-[46px] w-[43px]"
            alt="Group"
            src="/home/<USER>"
          />

          {/* Header title */}
          <div className="absolute left-[101px] top-[3px] h-[37px] w-[185px]">
            <div className="h-[37px] w-[187px]">
              <div className="relative -top-0.5 left-[-3px] h-[39px] w-[191px]">
                <div className="absolute left-[42px] top-[7px] w-[92px] text-center text-lg font-medium leading-[normal] tracking-[0] text-[#39abff] [font-family:'PingFang_SC-Medium',Helvetica]">
                  直流补能
                </div>

                <img
                  className="absolute left-[3px] top-0.5 h-9 w-[185px]"
                  alt="Vector"
                  src="/home/<USER>"
                />
                <div className="absolute left-[10.5px] top-[30px] h-[9px] w-[19px] rotate-180">
                  <img
                    className="absolute -top-1 left-[2px] h-[15px] w-[25px] -rotate-180"
                    alt="Group"
                    src="/home/<USER>"
                  />
                </div>
                <div className="absolute right-[11px] top-[30px] h-[9px] w-[19px] rotate-180">
                  <img
                    className="absolute -top-1 left-[-3px] h-[15px] w-[25px] -rotate-180"
                    alt="Group"
                    src="/home/<USER>"
                  />
                </div>
                <img
                  className="absolute left-0 top-0 h-[17px] w-[191px]"
                  alt="Group"
                  src="/home/<USER>"
                />
                <div
                  className="absolute left-[147px] top-4 h-[11px] w-[11px] rounded-[5.28px] bg-[#00ff50]"
                  // style={{ backgroundColor: dcChargingDockData?.device?.status === 'Connect' ? '#00ff50' : '#858585' }}
                  style={{
                    backgroundColor:
                      `${data?.DCInputStatus}` === '0' || !data?.DCInputStatus
                        ? '#858585'
                        : '#00ff50',
                  }}
                />
              </div>
            </div>
          </div>

          {/* Main content */}
          <div className="flex h-full w-[380px] flex-col px-7 pt-10">
            {/* Electrical readings card */}
            <div className='mt-2 bg-[linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)]'>
            <Card className="mt-2 h-[270px] w-full rounded-lg border-none bg-[linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)]">
              <CardContent className="flex flex-col gap-2 px-4 pb-[21px] pt-4">
                {dcInputDetailMetrics.map((metric, index) => (
                  <div key={index} className="flex items-center justify-start gap-3">
                    <div className="text-xl font-medium leading-9 tracking-[0] text-[#00c7ff] [font-family:'PingFang_SC-Medium',Helvetica]">
                      {metric.label}
                    </div>
                    <div className="text-xl font-medium leading-9 tracking-[0] text-[#c7f2ff] [font-family:'PingFang_SC-Medium',Helvetica]">
                      {formatterValue(
                        { value: dcInputDetail[metric.field], formatter: metric.formatter },
                        metric.unit,
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
            </div>
            {/* Start charging button */}
            <div className="mt-3">
              <BatterySlots
                status={[
                  !!chargingData?.['Charge_gun_position_detection1']?.value &&
                    chargingData?.['Charge_gun_position_detection1']?.value < 18,
                //   !!chargingData?.['Charge_gun_position_detection2']?.value &&
                //     chargingData?.['Charge_gun_position_detection2']?.value < 18,
                ]}
              >
                <div className="flex min-h-[50px] flex-1 items-center">
                  <div
                    style={{
                      width: '70px',
                      height: '26px',
                      padding: 3,
                      background:
                        'linear-gradient(175deg, rgba(0, 135.47, 255, 0.65) 0%, rgba(0, 135.47, 255, 0.08) 100%)',
                      borderRadius: 7.76,
                      outline: '0.78px #39ABFF solid',
                      outlineOffset: '-0.78px',
                      justifyContent: 'center',
                      alignItems: 'center',
                      gap: 3.1,
                      display: 'inline-flex',
                    }}
                  >
                    <div
                      onClick={() => operateDcStop(dcChargingDockData?.device?.deviceID)}
                      style={{
                        color: '#FE4545',
                        cursor: 'pointor',
                        fontSize: '12px',
                        fontWeight: '400',
                        wordWrap: 'break-word',
                      }}
                    >
                      补能复位
                    </div>
                  </div>
                </div>
              </BatterySlots>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DcBuNengHZ;
