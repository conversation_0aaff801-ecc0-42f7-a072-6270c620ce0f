.custom-modal {
    background: #143F8C;
    color: #fff;

    :global {
        .ant-modal-body {
            font-size: 16px;
        }

        .ant-modal-header {
            margin-bottom: 32px;
        }

        .ant-modal-content,
        .ant-modal-header,
        .ant-select-outlined.ant-select-status-error:not(.ant-select-customize-input) .ant-select-selector {
            background: #143F8C;
            background-color: #143F8C;
            box-shadow: none;
            color: #fff;
        }

        .ant-modal-title,
        .ant-modal-close {
            color: #fff;
            font-size: 20px;

            svg {
                height: 20px;
                width: 20px;
            }
        }

        .ant-modal-body .ant-select-selector {
            gap: 8px;
            opacity: 1;
            padding-right: 8px;
            padding-left: 8px;
            border-radius: 6px;
            border-width: 1px;
            background: linear-gradient(173.82deg, rgba(0, 135, 255, 0.125) 25.25%, rgba(0, 135, 255, 0.26) 93.59%);
            border: 1px solid #39ABFF;
            color: #fff;

            .ant-select-selection-placeholder {
                color: rgb(255 255 255 / 25%);
            }
        }

        .ant-modal-footer {
            display: flex;
            justify-content: space-around;
            margin-top: 48px;

            .ant-btn-color-primary {
                background: #0478EC;

            }

            button {
                flex: 1;
            }
        }
    }
}


.custom-check {
    :global {

        .ant-checkbox-inner {
            border-radius: 50%;
            background-color: transparent;
            width: 20px;
            height: 20px;
        }

        .ant-checkbox-checked .ant-checkbox-inner {
            background-color: #6DE875 !important
        }
    }
}