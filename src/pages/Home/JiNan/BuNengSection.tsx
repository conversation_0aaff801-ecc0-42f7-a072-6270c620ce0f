import { ChargingStatusSection } from '../WrapperScreen/ChargingStatusSection';
import { PowerDetailsSection } from '../WrapperScreen/PowerDetailsSection';
import { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { Button, Form, message, Modal } from 'antd';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import useRecharge from '../useRecharge';
import BatteryCard from './BatteryCard';
import { getStatusIcon } from '../utils';
import storage from '@/shared/storage';
import VinSection from '../WrapperScreen/VinSection';
import PowerSupplyState from './PowerSupplyState';
import { debounce } from 'lodash';
import DcBuNengHZ from './DcBuNengHZ';

// 获取状态文本 // status 1:开始、2:补能中，0:停止 | 启动1，0为关闭，2为进行中
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '开始补能';
    case 2:
      return '补能中';
    case 0:
      return '停止补能';
    default:
      return '停止补能';
  }
};
const Item = (props: { label: string; value: any; width: string; unit?: string }) => {
  const { label, value, width, unit = '' } = props;
  return (
    <div className="flex flex-1 items-end justify-start gap-[3px]" style={{ width: width }}>
      <div className="flex items-center justify-start gap-2.5">
        <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-[#9CCEFF]">
          {label}：
        </div>
      </div>
      <div className="flex items-end justify-start">
        <div className="flex items-center justify-start gap-2.5">
          <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
            {value}
            {unit}
          </div>
        </div>
      </div>
    </div>
  );
};
function BuNengSection({ data = {} }: any) {
  const [chargingPower, setChargingPower] = useState<number>(220);
  const [dcChargingDockData, setDCChargingDockData] = useState<any>({});
  const [pcsData, setPcsData] = useState<any>({});
  const [isChecking, setIsChecking] = useState(false);
  const { data: wsData } = useRecharge();

  // 当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电 6 为供电（直流）
  const { systemStatus, acInputMaxPower = 122,/*  acInputDefaultPower = 80 */ } = useDeviceStore();
  const acInputDefaultPower = 80;

  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 默认值
  useEffect(() => {
    const power = storage.get('chargingPower') || acInputDefaultPower;
    setChargingPower(power);
  }, [acInputDefaultPower]);

  const handlePowerChange = (power: number) => {
    setChargingPower(power);
  };
  // DCDC_ThisOut_Energy

  useEffect(() => {
    if (data.Devices?.length > 0) {
      // 电池组
      for (const d of data.Devices) {
        if (d.device.deviceID === 'DcChargingDock1') {
          setDCChargingDockData(d);
        } /* else if (d.device.deviceID === 'DcChargingDock2') {
          setDCChargingDockData2(d);
        }  */ else if (d.device.deviceID === 'PCS1') {
          const obj: any = { device: d.device, itemList: [] };
          for (const item of d.itemList) {
            if (
              [
                'Grid_PhaseA_V',
                'Grid_PhaseB_V',
                'Grid_PhaseC_V',
                'Grid_PhaseA_I',
                'Grid_PhaseB_I',
                'Grid_PhaseC_I',
              ].includes(item.name)
            ) {
              obj.itemList.push(item);
            }
          }
          // list  根据label 排序
          obj.itemList?.sort((a: any, b: any) => a.description?.localeCompare?.(b.description));
          setPcsData(obj);
        }
      }
    }
  }, [data?.Devices]);

  // 判断是否可以交流补能 正在供电、补能都不允许再次补能
  const canCharge = useMemo(() => {
    // 当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电 6 为供电（直流）
    // 直流供电 交流供电 交流补能三个只允许一个操作中
    if (systemStatus == 2  || data.ACInputStatus === 2) {
      return true;
    } else {
      return false;
    }
  }, [systemStatus,data.ACInputStatus]);

  const isCharging = useMemo(() => {
    return data.ACInputStatus == 2;
  }, [data.ACInputStatus]);

  const isPrepareCharging = useMemo(() => {
    return data.ACInputStatus == 1;
  }, [data.ACInputStatus]);

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  // 交流补能
  const acOperate = useCallback(
    async (params: any) => {
      if (!params?.canCharge ) {
        message.warning('当前状态无法补能');
        return;
      }
      if (params?.ACInputStatus == 2) {
        // 停止补能确认
        Modal.confirm({
          title: '停止补能确认',
          content: '是否停止补能？',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            const result = await HomeScreenService.stopCurrentOperation('ac', true);
            if (result.success) {
              message.success(result.message || '停止补能成功');
            } else {
              message.error(result.message || '停止补能失败');
            }
          },
        });
        return;
      }

      // 开始补能
      if (!params?.chargingPower) {

        message.warning('请设置补能功率');
        return;
      }

      try {
        setIsChecking(true);
        const hideLoading = message.loading('正在补能启动...');

        // 开始补能
        const result = await HomeScreenService.startACCharging(params?.chargingPower);
        hideLoading();
        if (result.success) {
          message.success(result.message || '开始补能');
          storage.set('chargingPower', params?.chargingPower || params?.acInputMaxPower);
        } else {
          message.error(result.message || '启动补能失败');
        }
      } catch (error) {
        message.error('补能异常，请检查设备状态');
      } finally {
        setIsChecking(false);
      }
    },
    [data.ACInputStatus, canCharge, acInputMaxPower],
  );
  const debouncedAcOperate = useRef(
    debounce((params: any) => acOperate(params), 5000, { leading: true, trailing: false }),
  ).current;

  const handleAcOperate = useCallback(() => {
    debouncedAcOperate({ chargingPower,ACInputStatus:data.ACInputStatus, canCharge, acInputMaxPower});
  }, [debouncedAcOperate, chargingPower,data.ACInputStatus, canCharge, acInputMaxPower]);
  return (
    <div className="mt-2 flex gap-4">
      {/* Left Column */}

      <div className="mb-2 mt-[44px] flex w-[300px] flex-row items-center justify-start rounded-[8px] bg-[linear-gradient(6.58deg,rgba(14,80,188,0.2)_0.57%,rgba(23,63,129,0)_90.25%)] from-[rgba(21,94,216,0.4)] via-[rgba(23,63,129,0)] to-[rgba(23,63,129,0)]">
        <div className="flex h-full flex-1 flex-col items-center gap-16">
          <PowerSupplyState title={'直流补能状态'} status={data.DCInputStatus} type="buneng" />
          <PowerSupplyState title={'交流补能状态'} status={data.ACInputStatus} type="buneng" />
        </div>
      </div>

      <div className="flex-1">
        <DcBuNengHZ data={data} />
      </div>
      {/* TODO 隐藏 恢复杭州版本补能页面 */}
      {false && (
        <div className="h-[472px] flex-1">
          <div className="relative flex flex-col items-center justify-center">
            {/* <VinSection /> */}
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center gap-[5px] rounded-[7px] p-1">
                <img className="relative h-9 w-10" alt="Frame" src="/home/<USER>" />

                <div className="inline-flex items-center gap-2.5">
                  <div className="mt-[-1.00px] text-[24px] font-medium leading-5 tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]">
                    直流补能
                  </div>
                  {/* <div
                  className="h-[30px] w-[30px] bg-[100%_100%]"
                  style={{
                    backgroundImage: `url(${getStatusIcon(data.DCInputStatus)})`,
                    backgroundSize: '100% 100%',
                  }}
                /> */}
                </div>
              </div>
            </div>
          </div>
          <div className="mt-1 flex flex-row items-center justify-center gap-8 pb-2">
            <BatteryCard
              data={dcChargingDockData}
              key={'dcChargingDockData1'}
              index={1}
              BatteryGroupSOC={data.BatteryGroupSOC1}
              acInputStatus={data.ACInputStatus}
            />
          </div>
        </div>
      )}

      {/* Right Column */}
      <div className="mr-3 flex w-[430px] flex-col gap-4">
        <div className="flex items-center justify-center gap-[5px]">
          <div className="inline-flex items-start rounded-[7px] px-2.5 py-1">
            <img className="relative h-7 w-5" alt="Union" src="/home/<USER>" />
          </div>

          <div className="inline-flex items-center justify-center gap-2.5">
            <div className="mt-[-1.00px] w-fit whitespace-nowrap text-[24px] font-medium leading-5 tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]">
              交流补能
            </div>
            <div
              className="h-[30px] w-[30px] bg-[100%_100%]"
              style={{
                backgroundImage: `url(${getStatusIcon(data.ACInputStatus)})`,
                backgroundSize: '100% 100%',
              }}
            />
          </div>
        </div>
        <PowerDetailsSection data={pcsData} />
        <ChargingStatusSection
          maxPower={acInputMaxPower}
          value={chargingPower}
          onPowerChange={handlePowerChange}
          disabled={isCharging || isChecking}
        />

        <Button
          className="mt-1 h-16 w-full rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
          onClick={handleAcOperate}
          disabled={!chargingPower || isChecking || isPrepareCharging}
        >
          <span
            className="whitespace-nowrap text-2xl font-normal leading-[18px] tracking-[0] [font-family:'PingFang_SC-Semibold',Helvetica]"
            style={{
              color: isCharging ? '#fe4545' : '#81dc4a',
            }}
          >
            {isChecking || isPrepareCharging ? '补能启动...' : isCharging ? '停止补能' : '开始补能'}
          </span>
        </Button>
        {false && (
          <div className="w-full max-w-[430px] rounded-lg border-0 bg-gradient-to-t from-[rgba(21,94,216,0.4)] to-transparent pl-4 pr-4">
            <div className="flex flex-col gap-5 text-xl">
              <div>
                <div className="text-[#9CCEFF]">网侧电压：</div>
                <div className="flex justify-between">
                  <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
                    Uab: {'0'}V
                  </div>
                  <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
                    Ubc: {'0'}V
                  </div>
                  <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
                    Uca: {'0'}V
                  </div>
                </div>
              </div>
              <div>
                <div className="text-[#9CCEFF]">网侧电流：</div>
                <div className="flex justify-between">
                  <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
                    la: {'0'}A
                  </div>
                  <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
                    lb: {'0'}A
                  </div>
                  <div className="justify-start font-['PingFang_SC'] text-xl font-medium leading-none text-cyan-100">
                    lc: {'0'}A
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-5">
                <div className="flex">
                  <Item label="网侧频率" value={0} width="50%" />
                  <Item label="负载率" value={0} width="50%" />
                </div>
                <div className="flex">
                  <Item label="视在功率" value={0} width="50%" />
                  <Item label="有功功率" value={0} width="50%" />
                </div>
                <div className="flex">
                  <Item label="电池电流" value={0} width="50%" />
                </div>
              </div>
            </div>
          </div>
        )}
        <div className="overflow-hidden text-xs text-[#fe4545]">
          {wsData.msg || wsData.acChargeErrMsg}
        </div>
      </div>
    </div>
  );
}

export default BuNengSection;
