import { Card, CardContent } from '@/components/ui/card';
import { Button, message, Modal } from 'antd';
import { debounce, isNil } from 'lodash';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import PowerSupplyState from './PowerSupplyState';
import BatteryCard from './BatteryCard';
import DcPowerSupply from './DcPowerSupply';
import { httpPost } from '@/shared/http';
import storage from '@/shared/storage';
import { generateUUID } from '@/utils/uuid';

// Mock data for development and testing
// const mockData = {
//   Devices: [
//     {
//       device: {
//         deviceID: 'ComputerDevice1',
//         status: 'Connect'
//       },
//       itemList: [
//         {
//           name: 'CurrentAllPCSMaxOutPower',
//           value: 50.5
//         }
//       ]
//     },
//     {
//       device: {
//         deviceID: 'PCS1',
//         status: 'Connect'
//       },
//       itemList: [
//         {
//           name: 'Load_Rate',
//           value: 75.3
//         }
//       ]
//     },
//     {
//       device: {
//         deviceID: 'PCS2',
//         status: 'Connect'
//       },
//       itemList: [
//         {
//           name: 'Load_Rate',
//           value: 82.1
//         }
//       ]
//     }
//   ],
//   ACOutStatus: 0,
//   DCOutStatus: 0
// };

// 定义电压电流映射关系
// const acParameterMap: any = {
//   'Load_Rate': { label: 'A相电压', unit: 'V' },
//   'Invert_PhaseB_V': { label: 'B相电压', unit: 'V' },
//   'Invert_PhaseC_V': { label: 'C相电压', unit: 'V' },
//   'Invert_PhaseA_I_Group1': { label: 'Ⅰ组A相电流', unit: 'A' },
//   'Invert_PhaseB_I_Group1': { label: 'Ⅰ组B相电流', unit: 'A' },
//   'Invert_PhaseC_I_Group1': { label: 'Ⅰ组C相电流', unit: 'A' },
// };

// 获取状态文本 // status 1:开始、2:补能中 或 0:停止 | 启动1，0为关闭，2为进行中
export const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '开始供电';
    case 2:
      return '供电中';
    case 0:
      return '停止供电';
    default:
      return '停止供电';
  }
};

const getDcdcDetail = (d: any) => {
  const obj: any = { A: {}, B: {} };
  for (const item of d.itemList) {
    if (
      [
        'DCDC_Out_V',
        'DCDC_Out_I',
        'DCDC_Out_Power',
        'DCDC_ThisOut_Energy',
        'DCDC_ThisOut_Time',
      ].includes(item.name)
    ) {
      obj.A[item.name.replace(/1/g, '')] = item.value;
    }
  }
  return obj;
};

function GongDianSection({ data = {} }: any) {
  const [acPcsData, setAcPcsData] = useState<any>({});
  // const [dcPcsData, setDcPcsData] = useState<any>({});
  const [dcdc, setDcdc] = useState<any>({});
  const [dcdcStatus, setDcdcStatus] = useState<any>(0);

  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  const { systemStatus } = useDeviceStore();
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  // data = mockData
  useEffect(() => {
    if (data.Devices?.length > 0) {
      // 电池组
      const list = [];
      const obj: any = { device: {}, itemList: [] };
      for (const d of data.Devices) {
        // if (d.device.deviceID === 'ChargingSPCS1tation1') {
        //   setStation1(getDetail(d));
        // }
        if (d.device.deviceID === 'PCS1') {
          setDcdc((prev: any) => ({ ...prev, ...getDcdcDetail(d) }));
        }
        if (d.device.deviceID === 'ZLAN1') {
          // dcdc充电枪状态  Electronic_lock_Signal
          for (const item of d.itemList) {
            if (item.name === 'Electronic_Lock_Signal') {
              setDcdcStatus(item.value);
            }
          }
        }
        if (d.device.deviceID === 'ComputerDevice1') {
          obj.device = d.device;
          for (const item of d.itemList) {
            if (item.name == 'CurrentAllPCSMaxOutPower') {
              list.push({
                id: item.name,
                label: '输出功率',
                value: isNil(item.value) ? '--' : item.value,
                unit: 'kw',
              });
            }
          }
        } else if (d.device.deviceID === 'PCS1') {
          obj.device = d.device;
          for (const item of d.itemList) {
            if (item.name == 'Load_Rate') {
              list.push({
                id: item.name,
                label: 'PCS1负载率',
                value: isNil(item.value) ? '--' : item.value,
                unit: '%',
              });
            }
          }
        } else if (d.device.deviceID === 'PCS2') {
          for (const item of d.itemList) {
            if (item.name == 'Load_Rate') {
              list.push({
                id: item.name,
                label: 'PCS2负载率',
                value: isNil(item.value) ? '--' : item.value,
                unit: '%',
              });
            }
          }
        }
      }
      // list.sort((a, b) => a.label.localeCompare(b.label));
      obj.itemList = [...list];
      setAcPcsData(obj);
    }
  }, [data?.Devices]);

  // 判断是否可以供电
  const canGongdian = useMemo(() => {
    if (systemStatus === 2 || data.ACOutStatus == 2) {
      return true;
    } else {
      return false;
    }
  }, [systemStatus, data.ACOutStatus]);

  // 判断交流是否可以供电
  const acBtnDisabled = useMemo(() => {
    if (data.ACOutStatus != 0 || !canGongdian) {
      return true; // 不可以供电
    } else {
      return false;
    }
  }, []);

  const isPrepareOutput = useMemo(() => {
    return data.ACOutStatus == 1;
  }, [data.ACOutStatus]);

  /*   // 获取图标
    const getStatusIcon = (status: number) => {
      return (status == 0 || !status) ? '/home/<USER>' : '/home/<USER>';
    };
   */

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  const acOperate = useCallback(
    async (params: any) => {
      // data.ACOutStatus ==2 表示正在供电
      if (!params?.canGongdian) {
        message.warning('当前状态无法供电');
        return;
      }

      if (params?.isOperating) {
        message.warning('操作冷却中，请稍后再试');
        return;
      }

      try {
        if (params?.ACOutStatus == 2) {
          // 停止供电确认
          Modal.confirm({
            title: '停止供电确认',
            content: '确认要停止交流供电吗？',
            okText: '确认',
            cancelText: '取消',
            onOk: async () => {
              // 清除之前的定时器（如果有的话）
              clearOperatingTimeout();
              setIsOperating(true);
              const result = await HomeScreenService.stopCurrentOperation('ac', false);
              if (result.success) {
                message.success('停止供电成功');
                // 成功后3秒冷却时间
                timeoutRef.current = setTimeout(() => {
                  setIsOperating(false);
                  timeoutRef.current = null;
                }, 3000);
              } else {
                message.error(result.message || '停止供电失败');
                // 失败后立即可以再次点击
                setIsOperating(false);
              }
            },
          });
        } else {
          // 清除之前的定时器（如果有的话）
          clearOperatingTimeout();
          setIsOperating(true);
          // 开始供电
          const result = await HomeScreenService.startACPowerSupply();
          if (result.success) {
            message.success('开始供电');
            // 成功后3秒冷却时间
            timeoutRef.current = setTimeout(() => {
              setIsOperating(false);
              timeoutRef.current = null;
            }, 3000);
          } else {
            message.error(result.message || '启动供电失败');
            // 失败后立即可以再次点击
            setIsOperating(false);
          }
        }
      } catch (error) {
        message.error('操作异常，请检查设备状态');
        // 异常后立即可以再次点击
        setIsOperating(false);
      }
    },
    [clearOperatingTimeout],
  );

  const debouncedAcOperate = useRef(
    debounce((params: any) => acOperate(params), 5000, { leading: true, trailing: false }),
  ).current;

  const handleAcOperate = useCallback(() => {
    debouncedAcOperate({ acBtnDisabled, ACOutStatus: data.ACOutStatus, canGongdian, isOperating });
  }, [debouncedAcOperate, acBtnDisabled, data.ACOutStatus, canGongdian, isOperating]);

  const handlePowerSubmit = async (power?: any) => {
    try {
      const _power = power || storage.get('dcdcPower');
      storage.set('dcdcPower', _power);
      const params = {
        devices: [
          {
            msgId: generateUUID(), // 消息ID
            deviceCode: 'PCS1', // 设备ID
            addresses: [
              {
                name: 'DCDC_Out_MaxCurrent', // 点位名称
                value: _power, //点位值，输入为准，可以是字符串，数字，浮点型
              },
            ],
          },
        ],
      };
      await httpPost('/api/RootInterface/WriteCommand', params);
    } catch (error) {}
  };

  /**
   *
   * @param opt 直流供电：
   */
  const DCOutputOption = async (params: any) => {
    try {
      const opt = params?.systemStatus !== 6 ? 'start' : 'stop';
      if (opt == 'start') {
        if (params?.systemStatus !== 2 && params?.systemStatus !== undefined) {
          message.warning('当前状态无法供电');
          return;
        }
        const body = {
          type: 'dcout',
          deviceId: 'PCS1', //充电桩ID  station1?.device?.name
          gunId: 'A', //A枪或B枪
        };
        Modal.confirm({
          title: '开始供电确认',
          content: '确认要开始直流供电吗？',
          okText: '确认',
          cancelText: '取消',
          // cancelButtonProps: {
          //   disabled:true
          // },
          onOk: async () => {
            await handlePowerSubmit();
            const result = await HomeScreenService.startDCDCPowerSupply(body);
            if (result.success) {
              message.success('开始供电成功');
            } else {
              message.error(result.message || '开始供电失败');
            }
          },
        });
      } else {
        Modal.confirm({
          title: '停止供电确认',
          content: '确认要停止直流供电吗？',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            const body = {
              deviceId: 'PCS1', //充电桩ID
              gunId: 'A',
            };
            const result = await HomeScreenService.stopCurrentOperation('dcdc', false, body);
            if (result.success) {
              message.success('停止供电成功');
            } else {
              message.error(result.message || '停止供电失败');
            }
          },
        });
      }
    } catch (error) {
      message.error('操作异常，请检查设备状态');
    }
  };

  const debouncedDCDCOperate = useRef(
    debounce((params: any) => DCOutputOption(params), 5000, { leading: true, trailing: false }),
  ).current;

  const handleDCDCOperate = useCallback(() => {
    debouncedDCDCOperate({ systemStatus });
  }, [debouncedDCDCOperate, systemStatus]);

  return (
    <div className="mt-2 flex gap-4">
      <div className="mb-2 mt-[44px] flex w-[300px] flex-row items-center justify-start rounded-[8px] bg-[linear-gradient(6.58deg,rgba(14,80,188,0.2)_0.57%,rgba(23,63,129,0)_90.25%)] from-[rgba(21,94,216,0.4)] via-[rgba(23,63,129,0)] to-[rgba(23,63,129,0)]">
        <div className="flex h-full flex-1 flex-col items-center gap-16">
          <PowerSupplyState
            title={'直流供电状态'}
            status={data.CurrentSystemStatus === 6 ? 1 : 0}
            type="gongdian"
            // onClick={DCOutputOption}
          />
          <PowerSupplyState
            title={'交流供电状态'}
            status={`${data?.ACOutStatus}` === '0' || !data?.ACOutStatus ? 0 : 1}
            type="gongdian"
            // onClick={acOperate}
          />
        </div>
      </div>
      {/* 直流供电 */}
      <div className="h-[472px] flex-1">
        <div className="flex flex-col items-center justify-center">
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center gap-[5px] rounded-[7px] p-1">
              <img className="relative h-9 w-10" alt="Frame" src="/home/<USER>" />

              <div className="inline-flex items-center gap-2.5">
                <div className="mt-[-1.00px] text-[24px] font-medium leading-5 tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]">
                  直流供电
                </div>
                {/* <div
                  className="h-[30px] w-[30px] bg-[100%_100%]"
                  style={{
                    backgroundImage: `url(${getStatusIcon(data.DCInputStatus)})`,
                    backgroundSize: '100% 100%',
                  }}
                /> */}
              </div>
            </div>
          </div>
        </div>
        <div className="mt-1 flex flex-row items-center justify-center gap-8 pb-2">
          <DcPowerSupply
            allData={data}
            data={dcdc}
            key={'dcChargingDockData1'}
            onButtonClick={handleDCDCOperate}
            handlePowerSubmit={handlePowerSubmit}
            dcdcStatus={dcdcStatus}
          />
        </div>
      </div>
      {/* 交流供电 */}
      <div className="relative ml-4 w-full max-w-[390px]">
        <Card className="relative h-[467px] w-full border-none">
          <CardContent className="p-0">
            <div className="relative h-[467px]">
              <img
                className="absolute left-1 top-[3px] h-[460px] w-[383px]"
                alt="Rectangle"
                src="/home/<USER>"
              />

              {/* Corner decorations */}
              <img
                className="absolute left-[347px] top-[421px] h-[46px] w-[43px]"
                alt="Group"
                src="/home/<USER>"
              />
              <img
                className="absolute left-[348px] top-0 h-[46px] w-[42px]"
                alt="Group"
                src="/home/<USER>"
              />
              <img
                className="absolute left-0 top-0 h-[46px] w-[43px]"
                alt="Group"
                src="/home/<USER>"
              />
              <img
                className="absolute left-0 top-[421px] h-[46px] w-[43px]"
                alt="Group"
                src="/home/<USER>"
              />

              {/* Header title */}
              <div className="absolute left-[101px] top-[3px] h-[37px] w-[185px]">
                <div className="h-[37px] w-[187px]">
                  <div className="relative -top-0.5 left-[-3px] h-[39px] w-[191px]">
                    <div className="absolute left-[42px] top-[7px] w-[92px] text-center text-lg font-medium leading-[normal] tracking-[0] text-[#39abff] [font-family:'PingFang_SC-Medium',Helvetica]">
                      交流供电
                    </div>

                    <img
                      className="absolute left-[3px] top-0.5 h-9 w-[185px]"
                      alt="Vector"
                      src="/home/<USER>"
                    />
                    <div className="absolute left-[10.5px] top-[30px] h-[9px] w-[19px] rotate-180">
                      <img
                        className="absolute -top-1 left-[2px] h-[15px] w-[25px] -rotate-180"
                        alt="Group"
                        src="/home/<USER>"
                      />
                    </div>
                    <div className="absolute right-[11px] top-[30px] h-[9px] w-[19px] rotate-180">
                      <img
                        className="absolute -top-1 left-[-3px] h-[15px] w-[25px] -rotate-180"
                        alt="Group"
                        src="/home/<USER>"
                      />
                    </div>
                    <img
                      className="absolute left-0 top-0 h-[17px] w-[191px]"
                      alt="Group"
                      src="/home/<USER>"
                    />
                    <div
                      className="absolute left-[147px] top-4 h-[11px] w-[11px] rounded-[5.28px] bg-[#00ff50]"
                      // style={{ backgroundColor: acPcsData?.device?.status === 'Connect' ? '#00ff50' : '#858585' }}
                      style={{
                        backgroundColor:
                          `${data?.ACOutStatus}` === '0' || !data?.ACOutStatus
                            ? '#858585'
                            : '#00ff50',
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Main content */}
              <div className="flex h-full w-[390px] flex-col px-7 pt-10">
                {/* Electrical readings card */}
                <Card className="mt-2 h-[300px] w-full rounded-lg border-none [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)]">
                  <CardContent className="flex flex-col items-center justify-center gap-[13px] p-4">
                    <div className="flex h-[300px] w-[324px] flex-col gap-2 p-2 pl-3">
                      {acPcsData?.itemList?.map?.((reading: any, index: number) => (
                        <div
                          key={`${reading.id}-${index}`}
                          className="flex max-h-[40px] w-[300px] items-center"
                        >
                          <span className="text-xl font-medium leading-9 tracking-[0] text-[#00c7ff] [font-family:'PingFang_SC-Medium',Helvetica]">
                            {reading.label}
                          </span>
                          <span className="ml-2 text-xl font-medium leading-9 tracking-[0] text-[#c7f2ff]">
                            {reading.value}&nbsp;{reading.unit}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                {/* Start charging button */}
                <Button
                  className="mt-3 h-16 w-full rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
                  onClick={handleAcOperate}
                  // disabled={`${data?.ACOutStatus}` === '0' || !data?.ACOutStatus || isPrepareOutput}
                  // 0 1 开始 2 结束
                  disabled={isPrepareOutput}
                >
                  <span
                    className="whitespace-nowrap text-2xl font-normal leading-[18px] tracking-[0] text-[#81dc4a] [font-family:'PingFang_SC-Semibold',Helvetica]"
                    style={{ color: data.ACOutStatus == 2 ? '#fe4545' : '' }}
                  >
                    {isPrepareOutput ? '供电启动…' : getStatusText(data.ACOutStatus == 2 ? 0 : 1)}
                  </span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default GongDianSection;
