import { Card, CardContent } from "@/components/ui/card";
import { ChargingStatusSection } from "../WrapperScreen/ChargingStatusSection";
import { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { Button, message, Modal } from 'antd';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import { httpPost } from '@/shared/http';
import { BatterySlots } from '@/components/BatterySlots';
import { formatMilliseconds } from '@/utils/formatDate';
import { formatterValue } from '@/utils';
import storage from '@/shared/storage';


const dcInputDetailMetrics = [
  { label: "电压", unit: "V", field: "VoltageOutputValue" },
  { label: "电流", unit: "A", field: "CurrentOutputValue" },
  { label: "功率", unit: "kw", field: "DCInputBatteryPower" },
  // { label: "本次补能量", unit: "kw", field: "DCInputPowerCapacity" },
  { label: "本次补能时长", unit: "", field: "CumulativeChargingTime", formatter: (v = 0) => formatMilliseconds(Number(v) * 60 * 1000) },
];

// 定义电压电流映射关系
const unitMap: Record<string, { label: string; unit: string }> = {
  'Grid_PhaseA_V': { label: 'A相电压', unit: 'V' },
  'Grid_PhaseB_V': { label: 'B相电压', unit: 'V' },
  'Grid_PhaseC_V': { label: 'C相电压', unit: 'V' },
  'Grid_PhaseA_I': { label: 'A相电流', unit: 'A' },
  'Grid_PhaseB_I': { label: 'B相电流', unit: 'A' },
  'Grid_PhaseC_I': { label: 'C相电流', unit: 'A' },
};


function BuNengSection({ data = {} }: any) {
  const [chargingPower, setChargingPower] = useState<number>(220);
  const [isChecking, setIsChecking] = useState(false);
  const [dcPcsData, setDcPcsData] = useState<any>({});
  const [dcChargingDockData, setDCChargingDockData] = useState<any>({});
  const [dcChargingDockData2, setDCChargingDockData2] = useState<any>({});
  const [dcInputDetail, setDcInputDetail] = useState<any>({});
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  // const { data: wsData } = useRecharge();

  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  // 补能可以供电 供电不能补能
  const { systemStatus, acInputMaxPower = 380, acInputDefaultPower = 220 } = useDeviceStore();

  // 默认值
  useEffect(() => {
    const power = storage.get('chargingPower') || acInputDefaultPower;
    setChargingPower(power);
  }, [acInputDefaultPower]);

  const handlePowerChange = (power: number) => {
    setChargingPower(power);
  };

  useEffect(() => {
    if (data.Devices?.length > 0) {
      // 电池组
      for (const d of data.Devices) {
        if (d.device.deviceID === 'DcChargingDock1') {
          setDCChargingDockData(d)
        } else if (d.device.deviceID === 'DcChargingDock2') {
          setDCChargingDockData2(d)
        } else if (d.device.deviceID === 'PCS1') {
          const dcObj: any = { device: d.device, itemList: [] };
          for (const item of d.itemList) {
            if (["Grid_PhaseA_V", "Grid_PhaseB_V", "Grid_PhaseC_V", "Grid_PhaseA_I", "Grid_PhaseB_I", "Grid_PhaseC_I"].includes(item.name)) {
              dcObj.itemList.push(item)
            }
          }
          // list  根据label 排序
          dcObj.itemList?.sort((a: any, b: any) => a.description?.localeCompare?.(b.description));
          setDcPcsData(dcObj);
        }
      }
    }
  }, [data?.Devices]);

  useEffect(() => {
    if (dcChargingDockData?.itemList?.length > 0) {
      const obj: any = {};
      for (const item of dcChargingDockData.itemList) {
        /*    if (item.name?.indexOf('BatteryGroupSOC') === 0) {
             setSoc(toFixed(item.value));
           } */
        obj[item.name] = item.value;
      }
      setDcInputDetail(obj);
    }
  }, [JSON.stringify(dcChargingDockData)]);


  // 判断是否可以补能
  const canCharge = useMemo(() => {
    // 补能可以供电 供电不能补能
    if (systemStatus == 2 || (systemStatus != 4 && systemStatus != 1)) {
      return !!systemStatus;
    } else {
      return false
    }
  }, [systemStatus]);


  // 判断pcs可以补能
  const chargeDisabled = useMemo(() => {
    if (data.ACInputStatus != 0 || !canCharge) {
      return true // 不可以补能
    } else {
      return false
    }
  }, [canCharge]);

  const isCharging = useMemo(() => {
    return data.ACInputStatus == 2
  }, [data.ACInputStatus])


  const isPrepareCharging = useMemo(() => {
    return data.ACInputStatus == 1
  }, [data.ACInputStatus]);

  const acOperate = useCallback(async () => {
    if (!canCharge) {
      message.warning('当前状态无法补能');
      return;
    }
    if (data.ACInputStatus == 2) {
      // 停止补能确认
      Modal.confirm({
        title: '停止补能确认',
        content: '是否停止补能？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const result = await HomeScreenService.stopCurrentOperation('ac', true);
          if (result.success) {
            message.success(result.message || '停止补能成功');
          } else {
            message.error(result.message || '停止补能失败');
          }
        }
      });
      return;
    }

    // 开始补能
    if (!chargingPower) {
      message.warning('请设置补能功率');
      return;
    }

    try {
      setIsChecking(true);
      const hideLoading = message.loading('正在补能启动...');

      // 开始补能
      const result = await HomeScreenService.startACCharging(chargingPower);
      hideLoading();
      if (result.success) {
        message.success(result.message || '开始补能');
        storage.set('chargingPower', chargingPower || acInputMaxPower);
      } else {
        message.error(result.message || '启动补能失败');
      }
    } catch (error) {
      message.error('补能异常，请检查设备状态');
    } finally {
      setIsChecking(false);
    }
  }, [data.ACInputStatus, chargingPower, canCharge]);

  const dcCharging = useMemo(() => {
    return data.DCInputStatus == 2 || data.DCInputStatus == 1
  }, [data.DCInputStatus])

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  const operateDcStop = useCallback((deviceId: string) => {
    if (!dcCharging) {
      message.warning('当前状态不是补能中，无法补能复位');
      return;
    }

    if (isOperating) {
      message.warning('操作冷却中，请稍后再试');
      return;
    }

    Modal.confirm({
      title: '操作确认',
      content: `是否要操作补能复位？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 清除之前的定时器（如果有的话）
          clearOperatingTimeout();
          setIsOperating(true);
          const res = await httpPost('EMS/DCInputPower/Stop', {
            "chargingDockId": deviceId || "DcChargingDock1", //充电座的充电口id
          });
          if (res.result == "successful") {
            message.info('补能复位操作成功！');
            // 成功后3秒冷却时间
            timeoutRef.current = setTimeout(() => {
              setIsOperating(false);
              timeoutRef.current = null;
            }, 3000);
          } else {
            message.error(res?.resultInfo || '补能复位操作失败！');
            // 失败后立即可以再次点击
            setIsOperating(false);
          }
        } catch (error) {
          message.error('操作失败，请重试');
          // 异常后立即可以再次点击
          setIsOperating(false);
        }
      }
    });

  }, [dcCharging, isOperating, clearOperatingTimeout]);

  return (
    <div className="relative w-full  flex flex-row gap-1">
      <Card className="relative w-full h-[467px] border-none">
        <CardContent className="p-0">
          <div className="relative h-[467px]">
            <img
              className="absolute w-[383px] h-[460px] top-[3px] left-1"
              alt="Rectangle"
              src="/home/<USER>"
            />

            {/* Corner decorations */}
            <img
              className="absolute w-[43px] h-[46px] top-[421px] left-[347px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[42px] h-[46px] top-0 left-[348px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[43px] h-[46px] top-0 left-0"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[43px] h-[46px] top-[421px] left-0"
              alt="Group"
              src="/home/<USER>"
            />

            {/* Header title */}
            <div className="absolute w-[185px] h-[37px] top-[3px] left-[101px]">
              <div className="w-[187px] h-[37px]">
                <div className="relative w-[191px] h-[39px] -top-0.5 left-[-3px]">
                  <div className="absolute w-[92px] top-[7px] left-[42px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#39abff] text-lg text-center tracking-[0] leading-[normal]">
                    直流补能
                  </div>

                  <img
                    className="absolute w-[185px] h-9 top-0.5 left-[3px]"
                    alt="Vector"
                    src="/home/<USER>"
                  />
                  <div className="absolute w-[19px] h-[9px] top-[30px] left-[10.5px] rotate-180">
                    <img
                      className="absolute w-[25px] h-[15px] -top-1 left-[2px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <div className="absolute w-[19px] h-[9px] top-[30px] right-[11px] rotate-180">
                    <img
                      className="absolute w-[25px] h-[15px] -top-1 left-[-3px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <img
                    className="absolute w-[191px] h-[17px] top-0 left-0"
                    alt="Group"
                    src="/home/<USER>"
                  />
                  <div className="absolute w-[11px] h-[11px] top-4 left-[147px] bg-[#00ff50] rounded-[5.28px]"
                    style={{ backgroundColor: dcChargingDockData?.device?.status === 'Connect' ? '#00ff50' : '#858585' }}
                  />
                </div>
              </div>
            </div>


            {/* Main content */}
            <div className="flex flex-col w-[390px] h-full pt-10 px-7">
              {/* Electrical readings card */}
              <Card className="w-full h-[281px] mt-2 rounded-lg [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)] border-none">
                <CardContent className="flex flex-col gap-2 pt-4 pb-[21px] px-6">
                  {dcInputDetailMetrics.map((metric, index) => (
                    <div key={index} className="flex items-center justify-start gap-3">
                      <div className="[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#00c7ff] text-xl leading-9 tracking-[0]">
                        {metric.label}
                      </div>
                      <div className="[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#c7f2ff] text-xl tracking-[0] leading-9">
                        {formatterValue({ value: dcInputDetail[metric.field], formatter: metric.formatter }, metric.unit)}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
              {/* Start charging button */}
              <div className="mt-3">
                <BatterySlots status={[dcChargingDockData?.device?.status === 'Connect', dcChargingDockData2?.device?.status === 'Connect',]} >
                  <div style={{ width: '70px', height: '26px', padding: 3, background: 'linear-gradient(175deg, rgba(0, 135.47, 255, 0.65) 0%, rgba(0, 135.47, 255, 0.08) 100%)', borderRadius: 7.76, outline: '0.78px #39ABFF solid', outlineOffset: '-0.78px', justifyContent: 'center', alignItems: 'center', gap: 3.10, display: 'inline-flex' }}>
                    <div onClick={() => operateDcStop(dcChargingDockData?.device?.deviceID)} style={{ color: '#FE4545', cursor: 'pointor', fontSize: '12px', fontWeight: '400', wordWrap: 'break-word' }}>补能复位</div>
                  </div>
                </BatterySlots>
              </div>
            </div>
          </div>

        </CardContent>
      </Card>
      {/* 第二个 */}
      <Card className="relative w-[390px] h-[467px] border-none">
        <CardContent className="p-0">
          <div className="relative h-[467px]">
            <img
              className="absolute w-[383px] h-[460px] top-[3px] left-1"
              alt="Rectangle"
              src="/home/<USER>"
            />

            {/* Corner decorations */}
            <img
              className="absolute w-[43px] h-[46px] top-[421px] left-[347px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[42px] h-[46px] top-0 left-[348px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[43px] h-[46px] top-0 left-0"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[43px] h-[46px] top-[421px] left-0"
              alt="Group"
              src="/home/<USER>"
            />

            {/* Header title */}
            <div className="absolute w-[185px] h-[37px] top-[3px] left-[101px]">
              <div className="w-[187px] h-[37px]">
                <div className="relative w-[191px] h-[39px] -top-0.5 left-[-3px]">
                  <div className="absolute w-[92px] top-[7px] left-[42px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#39abff] text-lg text-center tracking-[0] leading-[normal]">
                    交流补能
                  </div>

                  <img
                    className="absolute w-[185px] h-9 top-0.5 left-[3px]"
                    alt="Vector"
                    src="/home/<USER>"
                  />
                  <div className="absolute w-[19px] h-[9px] top-[30px] left-[10.5px] rotate-180">
                    <img
                      className="absolute w-[25px] h-[15px] -top-1 left-[2px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <div className="absolute w-[19px] h-[9px] top-[30px] right-[11px] rotate-180">
                    <img
                      className="absolute w-[25px] h-[15px] -top-1 left-[-3px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <img
                    className="absolute w-[191px] h-[17px] top-0 left-0"
                    alt="Group"
                    src="/home/<USER>"
                  />
                  <div className="absolute w-[11px] h-[11px] top-4 left-[147px] bg-[#00ff50] rounded-[5.28px]" style={{ backgroundColor: dcPcsData?.device?.status === 'Connect' ? '#00ff50' : '#858585' }} />
                </div>
              </div>
            </div>


            {/* Main content */}
            <div className="flex flex-col w-full h-full pt-10 px-7">
              {/* Electrical readings card */}
              <Card className="w-full h-[201px] mt-2 rounded-lg [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)] border-none">
                <CardContent className="flex flex-col w-[350px]  items-center justify-center gap-[13px] p-4">
                  <div className="flex flex-wrap h-[206px] overflow-y-auto pb-3 gap-2">
                    {dcPcsData?.itemList?.map?.((reading: any, index: number) => (
                      <div
                        key={index}
                        className="flex items-center max-h-[40px] w-[150px]"
                      >
                        <span className="[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#00c7ff] text-l leading-9 tracking-[0]">
                          {reading.description?.replace?.('电网 ', '')}
                        </span>
                        <span className="ml-2 font-medium text-[#c7f2ff] text-l tracking-[0] leading-9">
                          {reading.value}&nbsp;{unitMap[reading.name].unit}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              {/* Start charging button */}
              <ChargingStatusSection
                maxPower={acInputMaxPower}
                value={chargingPower}
                onPowerChange={handlePowerChange}
                disabled={isCharging || isChecking}
                style={{ padding: 12, marginTop: 16, height: 110 }}
              />

              <Button
                className="w-full h-16 mt-2 rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
                onClick={acOperate}
                disabled={!chargingPower || isChecking || isPrepareCharging}
              >
                <span
                  className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-2xl leading-[18px] whitespace-nowrap tracking-[0]"
                  style={{
                    color: isCharging ? '#fe4545' : '#81dc4a'
                  }}
                >
                  {(isChecking || isPrepareCharging) ? '补能启动...' : (isCharging ? '停止补能' : '开始补能')}
                </span>
              </Button>
            </div>
          </div>

        </CardContent>
      </Card>
    </div>
  );

}

export default BuNengSection;
