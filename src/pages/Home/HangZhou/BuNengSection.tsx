import { Card, CardContent } from '@/components/ui/card';
import { ChargingStatusSection } from '../WrapperScreen/ChargingStatusSection';
import { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { Button, Divider, message, Modal, Radio } from 'antd';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import { httpPost } from '@/shared/http';
import { BatterySlots } from '@/components/BatterySlots';
import { formatMilliseconds } from '@/utils/formatDate';
import { formatterValue } from '@/utils';
import storage from '@/shared/storage';
import { debounce, isNil } from 'lodash';
import ProgressStatus from '../WrapperScreen/ProgessStatus';
import { useGlobalError } from '@/contexts/GlobalErrorContext';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import {
  formateProgressData,
  formateProgressDataByDevice,
  getCurrent,
  mergeProgressData,
} from '../useProgress';
import {
  getAllDeviceIDs,
  getPorcessTitle,
  MainViewRequestAddress,
  notProgressStatusMap,
} from './config';
import './BuNengSection.less';

const dcInputDetailMetrics = [
  { label: '电压', unit: 'V', field: 'VoltageOutputValue' },
  { label: '电流', unit: 'A', field: 'CurrentOutputValue' },
  { label: '功率', unit: 'kw', field: 'DCInputBatteryPower' },
  // { label: "本次补能量", unit: "kw", field: "DCInputPowerCapacity" },
  {
    label: '本次补能时长',
    unit: '',
    field: 'CumulativeChargingTime',
    formatter: (v = 0) => formatMilliseconds(Number(v) * 60 * 1000),
  },
];

// 定义电压电流映射关系
const unitMap: Record<string, { label: string; unit: string }> = {
  Grid_PhaseA_V: { label: 'A相电压', unit: 'V' },
  Grid_PhaseB_V: { label: 'B相电压', unit: 'V' },
  Grid_PhaseC_V: { label: 'C相电压', unit: 'V' },
  Grid_PhaseA_I: { label: 'A相电流', unit: 'A' },
  Grid_PhaseB_I: { label: 'B相电流', unit: 'A' },
  Grid_PhaseC_I: { label: 'C相电流', unit: 'A' },
};
// 交流补能和并网放电不需要流程的状态
//  0-初始状态 1-启动中 2-供电启动成功（不允许停止，继续显示启动流程）3-停止中  4-启动成功（允许停止，不再显示启动流程）
const notProgressStatusACMap = [0, 4, undefined];
const startedStatusMap = [1, 2, 3, 4];
function BuNengSection({ data = {} }: any) {
  const bunengConfig = MainViewRequestAddress.Input.ACInput?.[0];
  const { err, setError, progressAll } = useGlobalError();
  const [chargingPower, setChargingPower] = useState<number>(-220);
  const [isChecking, setIsChecking] = useState(false);
  const [dcPcsData, setDcPcsData] = useState<any>({});
  const [dcChargingDockData, setDCChargingDockData] = useState<any>({});
  const [dcChargingDockData2, setDCChargingDockData2] = useState<any>({});
  const [chargingData, setChargingData] = useState<any>({});
  const [dcInputDetail, setDcInputDetail] = useState<any>({});
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  // const { data: wsData } = useRecharge();
  const progressACInput = progressAll.acInput;
  const progressDCInput = progressAll.dcInput;
  const progressGridOut = progressAll.acGridOut;
  // data = mockData
  // const progressGridOut = mockacGridOut;
  // 供电流程弹窗状态
  // 直流补能
  // const [openProgressDCModal, setOpenProgressDCModal] = useState(false);
  const [progressDCData, setProgressDCData] = useState<any>([]);
  const [currentProgressDCNode, setCurrentProgressDCNode] = useState<any>({});

  // 交流补能/ 并网放电
  const [progressData, setProgressData] = useState<any>([]);
  const [currentProgressACNode, setCurrentProgressACNode] = useState<any>({});
  const [openProgressModal, setOpenProgressModal] = useState(false);

  // 操作确认
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmContent, setConfirmContent] = useState('');
  const [confirmFuncParams, setConfirmFuncParams] = useState<any>(null);
  const [detailData, setDetailData] = useState<any>({});
  const [isPrepareDCInput, setIsPrepareDCInput] = useState<boolean>(false);
  const [type, setType] = useState<string>('buneng');

  const processData = (type: string, pdata: any) => {
    const _progressData = formateProgressData(pdata);
    const currentNode = getCurrent(pdata);
    setCurrentProgressACNode(currentNode);
    setProgressData((prev: any[]) => {
      const merged = mergeProgressData(prev, _progressData);
      storage.set(type, merged);
      return merged;
    });
  };
  // 处理交流补能
  useEffect(() => {
    if (Object.keys(progressACInput).length === 0) return;
    processData('progressACInput', progressACInput);
  }, [progressACInput]);

  // 并网放电进度
  useEffect(() => {
    if (Object.keys(progressGridOut).length === 0) return;
    processData('progressACGridOut', progressGridOut);
  }, [progressGridOut]);

  // 处理直流补能进度
  useEffect(() => {
    if (!progressDCInput || Object.keys(progressDCInput).length === 0) return;
    const _progressData = formateProgressData(progressDCInput);
    const currentNode = getCurrent(progressDCInput);
    setCurrentProgressDCNode(currentNode);
    setProgressDCData((prev: any[]) => {
      const merged = mergeProgressData(prev, _progressData);
      storage.set('progressDCInput', merged);
      return merged;
    });
  }, [progressDCInput]);

  const isPrepareACInputACGrid = useMemo(() => {
    if (
      data.ACGridOutPowerStatus !== undefined &&
      notProgressStatusACMap.includes(data.ACGridOutPowerStatus)
    ) {
      setProgressData([]);
      storage.set('progressACGridOut', []);
    }
    if (data.ACInputStatus !== undefined && notProgressStatusACMap.includes(data.ACInputStatus)) {
      setProgressData([]);
      storage.set('progressACInput', []);
    }
    return (
      !notProgressStatusACMap.includes(data.ACGridOutPowerStatus) ||
      !notProgressStatusACMap.includes(data.ACInputStatus)
    );
  }, [data.ACGridOutPowerStatus, data.ACInputStatus]);

  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  const { systemStatus, acInputMaxPower = 380, acInputDefaultPower = -220 } = useDeviceStore();

  // 恢复交流补能和并网放电本地存储流程数据
  useEffect(() => {
    if (isPrepareACInputACGrid) {
      if (!notProgressStatusACMap.includes(data.ACGridOutPowerStatus)) {
        const progressACGridOut = storage.get('progressACGridOut') || [];
        setProgressData(progressACGridOut);
      }
      if (!notProgressStatusACMap.includes(data.ACInputStatus)) {
        const progressACInput = storage.get('progressACInput') || [];
        setProgressData(progressACInput);
      }
    }
  }, [isPrepareACInputACGrid, data.ACGridOutPowerStatus, data.ACInputStatus]);

  useEffect(() => {
    const DCInputStatus = data.DCInputStatus;
    const isPrepare = !notProgressStatusMap.includes(DCInputStatus);
    if (isPrepare) {
      setConfirmVisible(false);
      setIsPrepareDCInput(isPrepare);
      // 状态更新为直流补能开始时清空补能错误信息
      // @ts-ignore
      setError((prev: any) => ({ ...prev, ddc: '', content: '' }));
    }

    if (DCInputStatus !== undefined && notProgressStatusMap.includes(DCInputStatus)) {
      // 延迟5秒执行
      const timer = setTimeout(() => {
        setProgressDCData([]);
        storage.set('progressDCInput', []);
        setIsPrepareDCInput(isPrepare);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [data.DCInputStatus, notProgressStatusMap, setProgressDCData]);

  // 默认值
  useEffect(() => {
    const power = storage.get('chargingPower') || acInputDefaultPower;
    if (power > 0) {
      setType('fangdian');
    } else {
      setType('buneng');
    }
    setChargingPower(Number(power));
  }, [acInputDefaultPower]);

  const handlePowerChange = (power: number) => {
    if (type === 'buneng' && power > 0) {
      message.warning('交流补能功率设置不能大于0KW（如-100）');
      // 恢复之前设置的功率值
      const prevPower = storage.get('chargingPower') || acInputDefaultPower;
      // 为了确保触发子组件的更新
      setChargingPower(Number(prevPower) + 0.001);
      setTimeout(() => {
        setChargingPower(Number(prevPower));
      }, 0);
      return;
    }
    if (type === 'fangdian' && power < 0) {
      message.warning('并网放电功率设置不能小于0KW（如100）');
      // 恢复之前设置的功率值
      const prevPower = storage.get('chargingPower') || acInputDefaultPower;
      // 为了确保触发子组件的更新
      setChargingPower(Number(prevPower) + 0.001);
      setTimeout(() => {
        setChargingPower(Number(prevPower));
      }, 0);
      return;
    }
    setChargingPower(power);
    // 将新的功率值保存到本地存储
    storage.set('chargingPower', power);
  };

  useEffect(() => {
    const deviceIDs = getAllDeviceIDs(bunengConfig);
    if (data.Devices?.length > 0) {
      // 电池组
      for (const d of data.Devices) {
        if (d.device.deviceID === 'DcChargingDock1') {
          setDCChargingDockData(d);
        } else if (d.device.deviceID === 'DcChargingDock2') {
          setDCChargingDockData2(d);
        }
        if (deviceIDs.includes(d.device.deviceID)) {
          // 交流补能详情显示
          const formattedData: any = {};
          if (d.itemList && Array.isArray(d.itemList)) {
            d.itemList.forEach((item: any) => {
              if (item.name) {
                formattedData[item.name] = item.value;
              }
            });
          }
          setDetailData((prev: any) => ({ ...prev, [d.device.deviceID]: formattedData }));
        }

        if (d.device.deviceID === 'ZS2') {
          const deviceData: any = {};
          for (const item of d.itemList) {
            if (
              ['Charge_gun_position_detection1', 'Charge_gun_position_detection2'].includes(
                item.name,
              )
            )
              deviceData[item.name] = item;
          }
          setChargingData(deviceData);
        }
      }
    }
  }, [data?.Devices]);

  useEffect(() => {
    if (dcChargingDockData?.itemList?.length > 0) {
      const obj: any = {};
      for (const item of dcChargingDockData.itemList) {
        /*    if (item.name?.indexOf('BatteryGroupSOC') === 0) {
             setSoc(toFixed(item.value));
           } */
        obj[item.name] = item.value;
      }
      setDcInputDetail(obj);
    }
  }, [JSON.stringify(dcChargingDockData)]);

  // 判断是否可以开始
  const canCharge = useMemo(() => {
    // 仅待机可以补能、供电
    if (systemStatus == 2) {
      return !!systemStatus;
    } else {
      return false;
    }
  }, [systemStatus]);
  // 并网放电、交流补能
  const currentOperation = useMemo(() => {
    // const p={ACGridOutPowerStatus:data.ACGridOutPowerStatus,ACInputStatus: data.ACInputStatus, chargingPower, isChecking, canCharge}

    // 启动中
    if (
      data.ACGridOutPowerStatus == 1 ||
      (isChecking && chargingPower >= 0) ||
      data.ACGridOutPowerStatus == 2
    ) {
      return '放电启动...';
    }
    if (data.ACInputStatus == 1 || (isChecking && chargingPower < 0) || data.ACInputStatus === 2) {
      return '补能启动...';
    }
    // 交流补能中
    if (data.ACInputStatus === 4) {
      return '停止补能';
    }
    if (data.ACGridOutPowerStatus === 4) {
      return '停止放电';
    }
    // 待开始 // chargingPower<0 走交流补能 >0 并网放电
    if (type === 'buneng') {
      return '开始补能';
    }
    if (type === 'fangdian') {
      return '开始放电';
    }
  }, [
    data.ACGridOutPowerStatus,
    data.ACInputStatus,
    chargingPower,
    isChecking,
    canCharge,
    systemStatus,
    type,
  ]);

  // 判断pcs可以补能
  const chargeDisabled = useMemo(() => {
    if (data.ACInputStatus != 0 || !canCharge) {
      return true; // 不可以补能
    } else {
      return false;
    }
  }, [canCharge]);
  // 交流补能中
  const isCharging = useMemo(() => {
    return data.ACInputStatus == 2 || data.ACInputStatus == 4;
  }, [data.ACInputStatus]);

  // 并网放电中
  const isGridOut = useMemo(() => {
    return data.ACGridOutPowerStatus == 2 || data.ACGridOutPowerStatus == 4;
  }, [data.ACGridOutPowerStatus]);

  // 正在并网放电或者交流补能中
  const isPrepareCharging = useMemo(() => {
    return data.ACInputStatus == 1 || data.ACGridOutPowerStatus == 1;
  }, [data.ACInputStatus, data.ACGridOutPowerStatus]);

  const acOperate = useCallback(async (params: any) => {
    const { currentOperation = '' } = params;
    // chargingPower<0 走交流补能 >0 并网放电
    // 无法开始 &&  无法结束
    if (!params?.canCharge && params?.ACInputStatus !== 4 && params?.ACGridOutPowerStatus !== 4) {
      message.warning(`当前状态无法${currentOperation}`);
      return;
    }
    // @ts-ignore
    setError((prev: any) => ({ ...prev, ac: '', acGridOut: '', content: '' }));
    if (params?.ACInputStatus == 4 || params?.ACGridOutPowerStatus === 4) {
      // 停止补能、并网放电确认
      setConfirmContent(`确认是否${currentOperation}？`);
      // 存储当前操作的参数
      const currentParams = {
        type: params?.ACInputStatus == 4 ? 'ac' : 'acGridOut',
        ...params,
        currentOperation,
      };
      setConfirmFuncParams(currentParams);
      setConfirmVisible(true);
      return;
    }

    // 开始补能
    if (!params?.chargingPower) {
      message.warning('请设置补能功率');
      return;
    }
    setProgressData([]);
    storage.set('progressACInput', []);
    storage.set('progressACGridOut', []);
    setConfirmContent(`确认是否${currentOperation}？`);
    // 存储当前操作的参数
    const currentParams = {
      type: 'start',
      ...params,
      currentOperation,
    };
    setConfirmFuncParams(currentParams);
    setConfirmVisible(true);
  }, []);

  // 添加防抖 - 使用最新值调用
  const debouncedAcOperate = useRef(
    debounce((params: any) => acOperate(params), 5000, { leading: true, trailing: false }),
  ).current;

  const handleAcOperate = useCallback(() => {
    const params = {
      chargingPower,
      ACInputStatus: data.ACInputStatus,
      ACGridOutPowerStatus: data.ACGridOutPowerStatus,
      canCharge,
      acInputMaxPower,
      currentOperation,
    };
    debouncedAcOperate(params);
  }, [
    debouncedAcOperate,
    chargingPower,
    data.ACInputStatus,
    data.ACGridOutPowerStatus,
    canCharge,
    acInputMaxPower,
    currentOperation,
  ]);

  const dcCharging = useMemo(() => {
    return data.DCInputStatus == 2 || data.DCInputStatus == 1;
  }, [data.DCInputStatus]);

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      debouncedAcOperate.cancel();
    };
  }, [debouncedAcOperate]);

  const handleDCRest = async (deviceId: string) => {
    try {
      // 清除之前的定时器（如果有的话）
      clearOperatingTimeout();
      setIsOperating(true);
      const res = await httpPost('EMS/DCInputPower/Stop', {
        chargingDockId: deviceId || 'DcChargingDock1', //充电座的充电口id
      });
      if (res.result == 'successful') {
        message.info('补能复位操作成功！');
        // 成功后3秒冷却时间
        timeoutRef.current = setTimeout(() => {
          setIsOperating(false);
          timeoutRef.current = null;
        }, 3000);
      } else {
        message.error(res?.resultInfo || '补能复位操作失败！');
        // 失败后立即可以再次点击
        setIsOperating(false);
      }
    } catch (error) {
      message.error('操作失败，请重试');
      // 异常后立即可以再次点击
      setIsOperating(false);
    }
  };
  const operateDcStop = useCallback(
    (deviceId: string) => {
      if (!dcCharging) {
        message.warning('当前状态不是补能中，无法补能复位');
        return;
      }

      if (isOperating) {
        message.warning('操作冷却中，请稍后再试');
        return;
      }
      setProgressDCData([]);
      storage.set('progressDCInput', []);
      setConfirmVisible(true);
      setConfirmContent(`确认是否要操作补能复位？`);
      // 存储当前操作的参数，而不是函数
      const currentParams = {
        type: 'reset',
        deviceId,
      };
      setConfirmFuncParams(currentParams);
    },
    [dcCharging, isOperating, clearOperatingTimeout],
  );
  // TODO 停止接口无法正常返回兼容
  useEffect(() => {
    if (confirmLoading && confirmFuncParams.type !== 'start') {
      // 停止
      if (confirmFuncParams.type === 'ac' && data?.ACInputStatus === 0) {
        setConfirmVisible(false);
      }
      if (confirmFuncParams.type === 'acGridOut' && data?.ACGridOutPowerStatus === 0) {
        setConfirmVisible(false);
      }
    }
  }, [confirmFuncParams, confirmLoading, data?.ACInputStatus, data?.ACGridOutPowerStatus]);
  // 清理操作状态
  const cleanupOperation = () => {
    setConfirmLoading(false);
    setConfirmVisible(false);
  };
  // 操作确认
  const handleConfirm = useCallback(async (operationData: any) => {
    const { type, chargingPower, currentOperation, acInputMaxPower } = operationData;
    setConfirmLoading(true);
    setProgressData([]);
    storage.set('progressACInput', []);
    storage.set('progressACGridOut', []);
    try {
      if (type === 'start') {
        setOpenProgressModal(true);
        setConfirmVisible(false);
        await handleStartOperation(chargingPower, currentOperation);
      } else if (type === 'reset') {
        // setConfirmVisible(false);
        await handleDCRest(operationData.deviceId);
      } else {
        setOpenProgressModal(true);
        setConfirmVisible(false);
        await handleStopOperation(type, currentOperation);
      }
    } finally {
      cleanupOperation();
    }
  }, []);

  const handleStartOperation = async (chargingPower: number, currentOperation: string) => {
    setIsChecking(true);

    try {
      const isCharging = chargingPower < 0;
      const result = isCharging
        ? await HomeScreenService.startACCharging(chargingPower)
        : await HomeScreenService.startACGridOut(chargingPower);

      if (result.success) {
        const successMessage = isCharging ? '开始补能' : '开始放电';
        message.success(result.message || successMessage);
        storage.set('chargingPower', chargingPower);
      } else {
        const errorMessage = isCharging ? '启动补能失败' : '启动放电失败';
        message.error(result.message || errorMessage);
      }
    } catch (error) {
      // @ts-ignore
      handleOperationError(error.message, chargingPower < 0 ? 'ac' : 'acGridOut');
    } finally {
      setIsChecking(false);
    }
  };

  const handleStopOperation = async (type: string, currentOperation: string) => {
    try {
      const result = await HomeScreenService.stopCurrentOperation(type, true);
      const successMessage = `${currentOperation}成功`;
      const errorMessage = `${currentOperation}失败`;

      if (result.success) {
        message.success(result.message || successMessage);
      } else {
        message.error(result.message || errorMessage);
      }
    } catch (error) {
      handleOperationError(error, type);
    }
  };

  const handleOperationError = (error: any, operationType: string) => {
    const errorMessage =
      error ??
      (operationType === 'ac'
        ? '补能异常，请检查设备状态'
        : operationType === 'acGridOut'
          ? '放电异常，请检查设备状态'
          : '请检查设备状态');
    // setError({
    //   content: error.message || errorMessage,
    //   type: operationType,
    // });

    message.error(errorMessage || '操作异常，请检查设备状态');
  };

  // 获取字段值并处理Content类型的翻译
  const getFieldValue = useCallback(
    (name: string, id: string = 'PCSOverview') => {
      const value = detailData?.[id]?.[name];
      const res = value !== undefined && value !== null ? value : '-';
      //  res是小数时保留两位小数，整数则原样返回
      if (typeof res === 'number' && res % 1 !== 0) {
        return res.toFixed(0);
      }
      return res;
    },
    [detailData],
  );
  const handleTypeChange = (value: string) => {
    // chargingPower
    setType(value);

    setChargingPower((prev) => {
      if (prev > 0 && value === 'buneng') {
        return prev * -1;
      }
      if (prev < 0 && value === 'fangdian') {
        return prev * -1;
      }
      return prev;
    });
  };

  return (
    <div className="relative flex w-full flex-row gap-1">
      <Card className="relative h-[467px] w-full border-none">
        <CardContent className="p-0">
          <div className="relative h-[467px]">
            <img
              className="absolute left-1 top-[3px] h-[460px] w-[383px]"
              alt="Rectangle"
              src="/home/<USER>"
            />

            {/* Corner decorations */}
            <img
              className="absolute left-[347px] top-[421px] h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-[348px] top-0 h-[46px] w-[42px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-0 top-0 h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-0 top-[421px] h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />

            {/* Header title */}
            <div className="absolute left-[101px] top-[3px] h-[37px] w-[185px]">
              <div className="h-[37px] w-[187px]">
                <div className="relative -top-0.5 left-[-3px] h-[39px] w-[191px]">
                  <div className="absolute left-[42px] top-[7px] w-[92px] text-center text-lg font-medium leading-[normal] tracking-[0] text-[#39abff] [font-family:'PingFang_SC-Medium',Helvetica]">
                    直流补能
                  </div>

                  <img
                    className="absolute left-[3px] top-0.5 h-9 w-[185px]"
                    alt="Vector"
                    src="/home/<USER>"
                  />
                  <div className="absolute left-[10.5px] top-[30px] h-[9px] w-[19px] rotate-180">
                    <img
                      className="absolute -top-1 left-[2px] h-[15px] w-[25px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <div className="absolute right-[11px] top-[30px] h-[9px] w-[19px] rotate-180">
                    <img
                      className="absolute -top-1 left-[-3px] h-[15px] w-[25px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <img
                    className="absolute left-0 top-0 h-[17px] w-[191px]"
                    alt="Group"
                    src="/home/<USER>"
                  />
                  <div
                    className="absolute left-[147px] top-4 h-[11px] w-[11px] rounded-[5.28px] bg-[#00ff50]"
                    // style={{ backgroundColor: dcChargingDockData?.device?.status === 'Connect' ? '#00ff50' : '#858585' }}
                    style={{
                      backgroundColor:
                        `${data?.DCInputStatus}` === '0' || !data?.DCInputStatus
                          ? '#858585'
                          : '#00ff50',
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Main content */}
            <div className="flex h-full w-[390px] flex-col px-7 pt-12">
              {/* Electrical readings card */}
              <Card className="mt-2 h-[230px] w-full rounded-lg border-none [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)]">
                <CardContent className="flex flex-col gap-2 px-6 pb-[21px] pt-4">
                  {dcInputDetailMetrics.map((metric, index) => (
                    <div key={index} className="flex items-center justify-start gap-3">
                      <div className="text-xl font-medium leading-9 tracking-[0] text-[#00c7ff] [font-family:'PingFang_SC-Medium',Helvetica]">
                        {metric.label}
                      </div>
                      <div className="text-xl font-medium leading-9 tracking-[0] text-[#c7f2ff] [font-family:'PingFang_SC-Medium',Helvetica]">
                        {formatterValue(
                          { value: dcInputDetail[metric.field], formatter: metric.formatter },
                          metric.unit,
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
              {/* Start charging button */}
              <div className="mt-3">
                <BatterySlots
                  status={[
                    !!chargingData?.['Charge_gun_position_detection1']?.value &&
                      chargingData?.['Charge_gun_position_detection1']?.value < 18,
                    !!chargingData?.['Charge_gun_position_detection2']?.value &&
                      chargingData?.['Charge_gun_position_detection2']?.value < 18,
                  ]}
                >
                  <div className="flex min-h-[50px] flex-1 items-center">
                    <div
                      style={{
                        width: '70px',
                        height: '26px',
                        padding: 3,
                        background:
                          'linear-gradient(175deg, rgba(0, 135.47, 255, 0.65) 0%, rgba(0, 135.47, 255, 0.08) 100%)',
                        borderRadius: 7.76,
                        outline: '0.78px #39ABFF solid',
                        outlineOffset: '-0.78px',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: 3.1,
                        display: 'inline-flex',
                      }}
                    >
                      <div
                        onClick={() => operateDcStop(dcChargingDockData?.device?.deviceID)}
                        style={{
                          color: '#FE4545',
                          cursor: 'pointer',
                          fontSize: '12px',
                          fontWeight: '400',
                          wordWrap: 'break-word',
                        }}
                      >
                        补能复位
                      </div>
                    </div>
                  </div>
                </BatterySlots>
              </div>
              {
                // 直流补能停止中/启动中
                isPrepareDCInput && progressDCData && (
                  <div className="mt-2 h-[48px] w-full">
                    <ProgressStatus
                      // @ts-ignore
                      stepData={progressDCData}
                      current={currentProgressDCNode}
                      title={getPorcessTitle('DCInputStatus', data.DCInputStatus)}
                      // openModal={data.DCInputStatus == 1}
                      // delay={3000}
                    />
                  </div>
                )
              }
              {/* 获取报错信息 */}
              {err?.['dc'] && (
                <div className="text-md mt-2 whitespace-pre-wrap break-all text-[#FF4545]">
                  {err?.['dc']}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      {/* 第二个 */}
      <Card className="relative h-[467px] w-[390px] border-none">
        <CardContent className="p-0">
          <div className="relative h-[467px]">
            <img
              className="absolute left-1 top-[3px] h-[460px] w-[383px]"
              alt="Rectangle"
              src="/home/<USER>"
            />

            {/* Corner decorations */}
            <img
              className="absolute left-[347px] top-[421px] h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-[348px] top-0 h-[46px] w-[42px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-0 top-0 h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-0 top-[421px] h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />

            {/* 更换为交流补能并网放电tab切换 */}
            <div className="w-full pt-3">
              <Radio.Group
                onChange={(e) => handleTypeChange(e.target.value)}
                defaultValue={'buneng'}
                value={type}
                className={`custom-radio-group incard-radio-group flex w-full justify-center`}
              >
                {/* 正在放电不允许切换到补能 */}
                <Radio.Button
                  key={'buneng'}
                  value={'buneng'}
                  className={'custom-radio'}
                  disabled={data.ACGridOutPowerStatus != 0}
                >
                  交流补能
                </Radio.Button>
                {/* 正在补能不允许切换到并网放电 */}
                <Radio.Button
                  key={'fangdian'}
                  value={'fangdian'}
                  className={'custom-radio'}
                  disabled={data.ACInputStatus != 0}
                >
                  并网放电
                </Radio.Button>
              </Radio.Group>
            </div>

            {/* Main content */}
            <div className="flex h-[calc(100%-50px)] w-full flex-col px-7 pt-2">
              <div
                className="rounded-lg border-none [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)]"
                style={{ height: 'calc(100% - 20px)' }}
              >
                {/* Electrical readings card */}
                <Card className="box-shadow-[none] mt-2 h-[250px] w-full border-none shadow-[none]">
                  <CardContent className="flex w-[350px] flex-col items-center justify-center gap-[13px] p-4">
                    <div className="overflow-y-auto/* */ flex h-full w-full flex-row flex-wrap gap-2 pb-3">
                      {bunengConfig?.LabelGoup?.map((item) => {
                        if (!item.Children || !item.Children?.length) {
                          const value = getFieldValue(
                            item.Name || '',
                            item.DeviceID || bunengConfig.DeviceID,
                          );
                          const unit = item.Unit || '';
                          const valueLength = `${value}${unit}`.length;

                          return (
                            <div
                              style={{ width: 'calc(50% - 8px)' }}
                              className="flex"
                              key={item.Label}
                            >
                              <span className="text-nowrap text-[18px] tracking-[0] text-[#9CCEFF] [font-family:'PingFang_SC-Medium',Helvetica]">
                                {item.Label}:
                              </span>
                              <div
                                className={`ml-[2px] tracking-[0] text-[#E3F8FF] ${valueLength > 6 ? 'text-[16px]' : 'text-[18px]'}`}
                              >
                                {value}
                                {unit ? <span className="ml-[2px]">{unit}</span> : ''}
                              </div>
                            </div>
                          );
                        }
                        return (
                          <div key={item.Label} style={{ width: 'calc(100% - 16px)' }}>
                            <span className="text-nowrap text-[18px] tracking-[0] text-[#9CCEFF] [font-family:'PingFang_SC-Medium',Helvetica]">
                              {item.Label}:
                            </span>
                            <div className="flex justify-between">
                              {item.Children?.map((child) => {
                                const value = getFieldValue(
                                  child.Name || '',
                                  item.DeviceID || bunengConfig.DeviceID,
                                );
                                const unit = child.Unit || '';
                                const valueLength = `${value}${unit}`.length;
                                return (
                                  <div key={child.Label} className="flex">
                                    <span className="text-[18px] tracking-[0] text-[#E3F8FF] [font-family:'PingFang_SC-Medium',Helvetica]">
                                      {child.Label}:
                                    </span>
                                    <div
                                      className={`ml-[2px] tracking-[0] text-[#E3F8FF] ${valueLength > 6 ? 'text-[16px]' : 'text-[18px]'}`}
                                    >
                                      {value}
                                      {unit ? <span className="ml-[2px]">{unit}</span> : ''}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        );
                      })}
                      {/* {dcPcsData?.itemList?.map?.((reading: any, index: number) => (
                      <div key={index} className="flex max-h-[40px] w-[150px] items-center">
                        <span className="text-l font-medium leading-9 tracking-[0] text-[#00c7ff] [font-family:'PingFang_SC-Medium',Helvetica]">
                          {reading.description?.replace?.('电网 ', '')}
                        </span>
                        <span className="text-l ml-2 font-medium leading-9 tracking-[0] text-[#c7f2ff]">
                          {reading.value}&nbsp;{unitMap[reading.name].unit}
                        </span>
                      </div>
                    ))} */}
                    </div>
                  </CardContent>
                </Card>
                <div className="px-3">
                  <Divider className="m-0 bg-[#1C4DA0]" />
                </div>
                {/* Start charging button */}
                <ChargingStatusSection
                  label={
                    type === 'buneng' ? '功率设置(KW)(需输入负数)' : '功率设置(KW)(需输入正数)'
                  }
                  negativeAllowed={true}
                  maxPower={acInputMaxPower}
                  value={chargingPower}
                  onPowerChange={handlePowerChange}
                  type={bunengConfig.MultiWriteItems.Group}
                  disabled={isCharging || isGridOut || isChecking}
                  style={{
                    padding: 12,
                    marginTop: 16,
                    height: 42,
                    border: 'none',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                />
                <div className="mt-2 h-[48px] w-full px-2">
                  {!isPrepareACInputACGrid && (
                    <Button
                      className="h-[48px] w-full rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
                      onClick={handleAcOperate}
                      disabled={!chargingPower || isChecking || isPrepareCharging}
                    >
                      <span
                        className="whitespace-nowrap text-2xl font-normal leading-[18px] tracking-[0] [font-family:'PingFang_SC-Semibold',Helvetica]"
                        style={{
                          color: isCharging || isGridOut ? '#fe4545' : '#81dc4a',
                        }}
                      >
                        {/* {isChecking || isPrepareCharging
                    ? '补能启动...'
                    : isCharging || isGridOut
                      ? '停止补能'
                      : '开始补能'} */}

                        {currentOperation}
                      </span>
                    </Button>
                  )}
                  {isPrepareACInputACGrid && progressData && (
                    <div className="h-full w-full">
                      <ProgressStatus
                        // @ts-ignore
                        stepData={progressData || []}
                        current={currentProgressACNode}
                        title={
                          getPorcessTitle('ACInputStatus', data.ACInputStatus) ||
                          getPorcessTitle('ACGridOutPowerStatus', data.ACGridOutPowerStatus)
                        }
                        openModal={isPrepareACInputACGrid && openProgressModal}
                      />
                    </div>
                  )}
                  {/* chargingPower < 0 ? '交流补能' : '并网放电' */}
                  {err?.['mergedACandACGrid'] && (
                    <div className="text-md mt-1 whitespace-pre-wrap break-all text-[#FF4545]">
                      {err?.['mergedACandACGrid']}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => {
          setConfirmVisible(false);
        }}
        onConfirm={() => handleConfirm(confirmFuncParams)}
        confirmLoading={confirmLoading}
        content={<div className="leading-7">{confirmContent}</div>}
      />
    </div>
  );
}

export default BuNengSection;
