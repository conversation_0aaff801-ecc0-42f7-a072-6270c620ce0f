// TODO  流程模拟数据 待替换为真实数据
export const testdata = [
  {
    id: 0,
    title: 'PCS、BMS 状态检测',
    status: 'finish',
  },
  {
    id: 1,
    title: '禁放检测',
    status: 'alarm', //有告警但不影响继续运行
    description: '告警信息', //告警信息
  },
  {
    id: 2,
    title: 'DC侧开机',
    status: 'error', //报错的节点，不会运行
    description: '错误信息',
  },
  {
    id: 3,
    title: '开水冷(3分钟左右)',
    status: 'wait',
  },
  {
    id: 4,
    title: '生成放电列表',
    status: 'wait',
  },
  {
    id: 5,
    title: '绝缘检测1',
    status: 'wait',
  },
  {
    id: 6,
    title: '待机判断',
    status: 'wait',
  },
  {
    id: 7,
    title: '绝缘检测2',
    status: 'wait',
  },
  {
    id: 8,
    title: '离网逆变',
    status: 'wait',
  },
  {
    id: 9,
    title: '启动供电',
    status: 'wait',
  },
];
export const notProgressStatusMap = [0, 2, undefined];

export const StatusMap: Record<number, string> = {
  1: '运行',
  3: '停止运行',
};

// 遍历data对象，找到对应状态的数据点
const keyMap: Record<string, string> = {
  ACGridOutPowerStatus: '并网放电',
  ACInputStatus: '交流补能',
  ACOutStatus: '交流供电',
  DCOutStatus: '直流供电',
  DCInputStatus: '直流补能',
  DCDCPowerstatus: 'DCDC',
};
// 根据状态获取对应的数据点标题
export const getPorcessTitle = (targetKey: string, status: number) => {
  // 状态映射
  const statusText = StatusMap[status] || '';

  if (targetKey && statusText) {
    const type = keyMap[targetKey] || targetKey;
    return `${type}${statusText}准备`;
  }

  // 默认返回空字符串
  return '';
};

export function getAllDeviceIDs(config: any): string[] {
  const ids = new Set<string>();

  const traverse = (obj: any) => {
    if (!obj || typeof obj !== 'object') return;

    if ('DeviceID' in obj) {
      ids.add(obj.DeviceID);
    }

    Object.values(obj).forEach((value) => {
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (typeof value === 'object') {
        traverse(value);
      }
    });
  };

  traverse(config);
  return Array.from(ids);
}

export const MainViewRequestAddress = {
  ViewType: 'Tile/Switch',
  Header: {
    SystemStatus: {
      Name: 'CurrentSystemStatus',
      Label: '系统状态',
      ViewType: 'Content',
    },
    RemainPower: {
      Name: 'CurrentSystemStatus',
      Label: '剩余电量',
      Unit: 'kwh',
    },
    SOC: {
      Name: 'CurrentSystemStatus',
      Label: 'SoC',
      Unit: '%',
      ViewType: 'progressAndLabel',
    },
    Custom: [
      {
        Name: 'CurrentSystemStatus',
        Label: '装机容量',
        Unit: 'kwh',
      },
      {
        Name: 'CurrentSystemStatus',
        Label: '今日充电量',
        Unit: 'kwh',
      },
      {
        Name: 'CurrentSystemStatus',
        Label: '今日放电量',
        Unit: 'kwh',
      },
      {
        Name: 'CurrentSystemStatus',
        Label: '累计充电量',
        Unit: 'kwh',
      },
      {
        Name: 'CurrentSystemStatus',
        Label: '累计放电量',
        Unit: 'kwh',
      },
      {
        Name: 'BMSStatus',
        Label: '',
        Unit: 'BMS',
      },
      {
        Name: 'PCSStatus',
        Label: '',
        Unit: 'PCS',
      },
      {
        Name: 'lenquestatus',
        Label: '',
        Unit: '冷却设备',
        ViewType: 'Content',
      },
      {
        Name: 'ChargingStatus',
        Label: '',
        Unit: '充电桩',
      },
      {
        Name: 'AlarmStatus',
        Label: '',
        fontColor: 'red',
        Unit: '告警',
      },
    ],
  },
  Input: {
    DCInput: {
      Label: '直流补能',
      ViewType: 'Hide/Show',
      VIN: {
        Label: 'vin码',
        ViewType: 'SingleSelect',
        Name: 'VINCode',
      },
      DCInputGroup: [
        {
          Label: '1#电池组',
          DeviceID: 'DcChargingDock1',
          LabelGoup: [
            {
              Unit: '%',
              Label: 'SoC',
              ViewType: 'progressAndLabel',
              Name: 'BatteryGroupSOC1',
            },
            {
              Unit: 'V',
              Label: '电压',
              ViewType: 'Label',
              Name: 'VoltageOutputValue',
            },
            {
              Unit: 'A',
              Label: '电流',
              ViewType: 'Label',
              Name: 'CurrentOutputValue',
            },
            {
              Unit: 'Kw',
              Label: '功率',
              ViewType: 'Label',
              Name: 'DCInputBatteryPower',
            },
            {
              Unit: '秒',
              Label: '本次补能时长',
              ViewType: 'Label',
              Name: 'CumulativeChargingTime',
            },
            {
              Unit: 'Kwh',
              Label: '本次补能电量',
              ViewType: 'Label',
              Name: 'DcChargingDock1',
            },
          ],
          InputPowerGoups: [
            {
              Title: '1#',
              Unit: '',
              Label: '',
              Image: 'Smile.png',
              ViewType: 'Content',
              Name: 'Input1',
              Button: {
                ButtonLabel: '补能复位',
                API: 'url',
                Type: 'Post',
                RequestPara: '',
              },
            },
            {
              Title: '2#',
              Unit: '',
              Label: 'Status',
              Image: 'Smile.png',
              ViewType: 'Content',
              Name: 'Input1',
              Button: {
                ButtonLabel: '补能复位',
                API: 'url',
                Type: 'Post',
                RequestPara: '',
              },
            },
          ],
          StatusFlag: {
            Name: 'progress',
            StatusType: 'progressStep',
            ErrorMsg: '',
            WarnMsg: '',
            CurrentMsg: '',
            Status: 0,
            Step: 'PCS待机',
            DCDetailMsg: {
              AddressName: '点位名称',
              Unit: '',
              Descript: '',
            },
          },
        },
      ],
    },
    ACInput: [
      {
        Label: '交流补能(并网放电)',
        ViewType: 'Show',
        DeviceID: 'PCSOverview',
        LabelGoup: [
          {
            Label: '网侧电压',
            Children: [
              {
                Label: 'Uab',
                Unit: 'V',
                Name: 'Grid_AB_V',
              },
              {
                Label: 'Ubc',
                Unit: 'V',
                Name: 'Grid_BC_V',
              },
              {
                Label: 'Uca',
                Unit: 'V',
                Name: 'Grid_CA_V',
              },
            ],
          },
          {
            Label: '网侧电流',
            Children: [
              {
                Label: 'Ia',
                Unit: 'A',
                Name: 'Grid_PhaseA_I',
              },
              {
                Label: 'Ib',
                Unit: 'A',
                Name: 'Grid_PhaseB_I',
              },
              {
                Label: 'Ic',
                Unit: 'A',
                Name: 'Grid_PhaseC_I',
              },
            ],
          },
          {
            Name: 'Grid_Freq',
            Label: '网侧频率',
            Unit: 'Hz',
          },
          {
            Name: 'Load_Rate',
            Label: '负载率',
            Unit: '%',
          },
          {
            Name: 'Apparent_Power',
            Label: '视在功率',
            Unit: 'KVA',
          },
          {
            Name: 'Active_Power',
            Label: '有功功率',
            Unit: 'Kw',
          },
          {
            Name: 'CurrentAllPCSMaxOutPower',
            Label: '输出功率',
            Unit: 'Kw',
            DeviceID: 'ComputerDevice1',
          },
          {
            Name: 'Battery_Current_BatI',
            Label: '电池电流',
            Unit: 'A',
          },
        ],
        MultiWriteItems: {
          Label: '',
          Group: [
            {
              Name: 'Less0',
              Type: 'INT',
              Title: '并网放电',
              Descript: '放电功率',
              Unit: '',
              Min: 1,
              Max: 99999999999,
              apiType: 'Post',
              Api: '/EMS/ACGrid/OutPower/Start',
              Value: {
                type: 'acgridout',
                deviceID: 'Dock1',
                gunID: '',
                userInputPara: 200,
              },
            },
            {
              Name: 'Greater0',
              Type: 'INT',
              Descript: '补能功率',
              Title: '交流补能',
              Unit: '',
              Min: -999999999,
              Max: 0,
              apiType: 'Post',
              Api: 'EMS/AC/InputPower/Start',
              Value: {
                type: 'acinput',
                deviceID: 'Dock1',
                gunID: '',
                userInputPara: 200,
              },
            },
          ],
        },
        ButtonItems: {
          Label: '',
          Items: [
            {
              Label: '',
              Group: [
                {
                  Name: 'start',
                  Value: 1,
                  Descript: "开始+前面的'Title'内容",
                },
                {
                  Name: 'stop',
                  Value: 2,
                  Descript: "停止+前面的'Title'内容",
                },
              ],
            },
          ],
        },
        StatusFlag: {
          Name: 'progress',
          StatusType: 'progressStep',
          ErrorMsg: '',
          WarnMsg: '',
          CurrentMsg: '',
          Status: 0,
          Step: 'PCS待机',
        },
      },
    ],
  },
  Output: {
    ACOut: [
      {
        Label: '交流放电',
        ViewType: 'Show',
        DeviceID: 'PCSOverview',
        LabelGoup: [
          {
            Label: '网侧电压',
            Children: [
              {
                Label: 'Uab',
                Unit: 'V',
                Name: 'Grid_AB_V',
              },
              {
                Label: 'Ubc',
                Unit: 'V',
                Name: 'Grid_BC_V',
              },
              {
                Label: 'Uca',
                Unit: 'V',
                Name: 'Grid_CA_V',
              },
            ],
          },
          {
            Label: '网侧电流',
            Children: [
              {
                Label: 'Ia',
                Unit: 'A',
                Name: 'Grid_PhaseA_I',
              },
              {
                Label: 'Ib',
                Unit: 'A',
                Name: 'Grid_PhaseB_I',
              },
              {
                Label: 'Ic',
                Unit: 'A',
                Name: 'Grid_PhaseC_I',
              },
            ],
          },
          {
            Name: 'Grid_Freq',
            Label: '网侧频率',
            Unit: 'Hz',
          },
          {
            Name: 'Load_Rate',
            Label: '负载率',
            Unit: '%',
          },
          {
            Name: 'Apparent_Power',
            Label: '视在功率',
            Unit: 'KVA',
          },
          {
            Name: 'Active_Power',
            Label: '有功功率',
            Unit: 'Kw',
          },
          {
          	"Name": "CurrentAllPCSMaxOutPower",
          	"Label": "输出功率",
          	"Unit": "Kw",
          	"DeviceID": "ComputerDevice1"
          },
          // {
          // 	"Name": "CurrentAllPCSOutPower",
          // 	"Label": "实时输出功率",
          // 	"Unit": "Kw",
          // 	"DeviceID": "ComputerDevice1"
          // },
          {
            Name: 'Battery_Current_BatI',
            Label: '电池电流',
            Unit: 'A',
          },
        ],
        StatusFlag: {
          Name: 'progress',
          StatusType: 'progressStep',
          ErrorMsg: '',
          WarnMsg: '',
          CurrentMsg: '',
          Status: 0,
          Step: 'PCS待机',
        },
      },
    ],
    ACDCOut: {
      Label: '直流放电',
      ViewType: 'Hide/Show',
      ACDCOutGroup: [
        {
          Label: '1#充电桩',
          ViewType: 'Show',
          DeviceID: 'ChargingStation1',
          LabelGoup: [
            {
              Label: 'A枪',
              Children: [
                {
                  Name: 'CC1CS1',
                  Label: '连接状态',
                  Unit: '',
                },
                {
                  Name: 'CCV1',
                  Label: '电压',
                  Unit: 'V',
                },
                {
                  Name: 'CCC1',
                  Label: '电流',
                  Unit: 'A',
                },
                {
                  Name: 'CCP1',
                  Label: '功率',
                  Unit: 'kw',
                },
                {
                  Name: 'CDTC1',
                  Label: '时长',
                  Unit: 'Min',
                },
              ],
              ButtonItems: {
                Label: '',
                Items: [
                  {
                    Label: '',
                    Group: [
                      {
                        Name: 'SSC1',
                        Value: 1,
                        Descript: '开始充电',
                      },
                      {
                        Name: 'SSC1',
                        Value: 2,
                        Descript: '结束充电',
                      },
                    ],
                  },
                ],
              },
            },
            {
              Label: 'B枪',
              Children: [
                {
                  Name: 'CC1CS2',
                  Label: '连接状态',
                  Unit: '',
                },
                {
                  Name: 'CCV2',
                  Label: '电压',
                  Unit: 'V',
                },
                {
                  Name: 'CCC2',
                  Label: '电流',
                  Unit: 'A',
                },
                {
                  Name: 'CCP2',
                  Label: '功率',
                  Unit: 'kw',
                },
                {
                  Name: 'CDTC2',
                  Label: '时长',
                  Unit: 'Min',
                },
              ],
              ButtonItems: {
                Label: '',
                Items: [
                  {
                    Label: '',
                    Group: [
                      {
                        Name: 'SSC2',
                        Value: 1,
                        Descript: '开始充电',
                      },
                      {
                        Name: 'SSC2',
                        Value: 2,
                        Descript: '结束充电',
                      },
                    ],
                  },
                ],
              },
            },
          ],
          StatusFlag: {
            Name: 'progress',
            StatusType: 'progressStep',
            ErrorMsg: '',
            WarnMsg: '',
            CurrentMsg: '',
            Status: 0,
            Step: 'PCS待机',
          },
        },
      ],
    },
  },
};
