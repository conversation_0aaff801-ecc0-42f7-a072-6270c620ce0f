import { Card, CardContent } from "@/components/ui/card";
import { Button, Input, message, Modal } from 'antd';
import { isNil } from 'lodash';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import { httpPost } from '@/shared/http';
import { CloseOutlined } from '@ant-design/icons';
import Keyboard from 'react-simple-keyboard';
import { generateUUID } from '@/utils/uuid';

// Mock data for development and testing
const mockData = {
  Devices: [
    {
      device: {
        deviceID: 'ComputerDevice1',
        status: 'Connect'
      },
      itemList: [
        {
          name: 'CurrentAllPCSMaxOutPower',
          value: 50.5
        }
      ]
    },
    {
      device: {
        deviceID: 'PCS1',
        status: 'Connect'
      },
      itemList: [
        {
          name: 'Load_Rate',
          value: 75.3
        },
        {
          name: 'ACOutPowerPercentage',
          value: 70.2
        }
      ]
    },
    {
      device: {
        deviceID: 'PCS2',
        status: 'Connect'
      },
      itemList: [
        {
          name: 'Load_Rate',
          value: 82.1
        },
        {
          name: 'ACOutPowerPercentage',
          value: 30.5
        }
      ]
    }
  ],
  ACOutStatus: 0,
  DCOutStatus: 0
};

// 定义电压电流映射关系
// const acParameterMap: any = {
//   'Load_Rate': { label: 'A相电压', unit: 'V' },
//   'Invert_PhaseB_V': { label: 'B相电压', unit: 'V' },
//   'Invert_PhaseC_V': { label: 'C相电压', unit: 'V' },
//   'Invert_PhaseA_I_Group1': { label: 'Ⅰ组A相电流', unit: 'A' },
//   'Invert_PhaseB_I_Group1': { label: 'Ⅰ组B相电流', unit: 'A' },
//   'Invert_PhaseC_I_Group1': { label: 'Ⅰ组C相电流', unit: 'A' },
// };

// 获取状态文本 // status 1:开始、2:补能中 或 0:停止 | 启动1，0为关闭，2为进行中
const getStatusText = (status: number) => {
  switch (status) {
    case 1: return '开始供电';
    case 2: return '供电中';
    case 0: return '停止供电';
    default: return '停止供电';
  }
};

function GongDianSection({ data = {} }: any) {
  const [acPcsData, setAcPcsData] = useState<any>({});
  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  const { systemStatus } = useDeviceStore();
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [currentDevice, setCurrentDevice] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);

  // data = mockData
  useEffect(() => {
    if (data.Devices?.length > 0) {
      // 电池组
      const list = [];
      const obj: any = { device: {}, itemList: [] };
      for (const d of data.Devices) {
        if (d.device.deviceID === 'ComputerDevice1') {
          obj.device = d.device;
          for (const item of d.itemList) {
            if (item.name == 'CurrentAllPCSMaxOutPower') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                label: '输出功率',
                value: isNil(item.value) ? '--' : item.value,
                unit: 'kw'
              });
            }
          }
        } else if (d.device.deviceID === 'PCS1') {
          obj.device = d.device;
          for (const item of d.itemList) {
            if (item.name == 'Load_Rate') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                label: 'PCS1负载率',
                value: isNil(item.value) ? '--' : item.value,
                unit: '%'
              });
            } else if (item.name == 'ACOutPowerPercentage') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                deviceCode: d.device.deviceID,
                label: 'PCS1并机功率比例',
                value: isNil(item.value) ? '--' : item.value,
                unit: '%'
              });
            }
          }
        } else if (d.device.deviceID === 'PCS2') {
          for (const item of d.itemList) {
            if (item.name == 'Load_Rate') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                label: 'PCS2负载率',
                value: isNil(item.value) ? '--' : item.value,
                unit: '%'
              });
            } else if (item.name == 'ACOutPowerPercentage') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                label: 'PCS2并机功率比例',
                deviceCode: d.device.deviceID,
                value: isNil(item.value) ? '--' : item.value,
                unit: '%'
              });
            }
          }
        }
        // list.sort((a, b) => a.label.localeCompare(b.label));
        obj.itemList = [...list];
        setAcPcsData(obj);
      }
    }
  }, [data?.Devices]);

  // 判断是否可以供电
  const canGongdian = useMemo(() => {
    if (systemStatus != 1) {
      return !!systemStatus;
    } else {
      return false
    }
  }, [systemStatus]);

  // 判断直流是否可以供电
  const dcBtnDisabled = useMemo(() => {
    if (data.DCOutStatus != 0 || !canGongdian) {
      return true // 不可以供电
    } else {
      return false
    }
  }, []);

  // 判断交流是否可以供电
  const acBtnDisabled = useMemo(() => {
    if (data.ACOutStatus != 0 || !canGongdian) {
      return true // 不可以供电
    } else {
      return false
    }
  }, [])


  const isPrepareOutput = useMemo(() => {
    return data.ACOutStatus == 1
  }, [data.ACOutStatus]);



  /*   // 获取图标
    const getStatusIcon = (status: number) => {
      return (status == 0 || !status) ? '/home/<USER>' : '/home/<USER>';
    };
   */

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  const acOperate = useCallback(async () => {
    if (!canGongdian) {
      message.warning('当前状态无法供电');
      return;
    }

    if (isOperating) {
      message.warning('操作冷却中，请稍后再试');
      return;
    }

    try {
      if (data.ACOutStatus == 2) {
        // 停止供电确认
        Modal.confirm({
          title: '停止供电确认',
          content: '确认要停止交流供电吗？',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            // 清除之前的定时器（如果有的话）
            clearOperatingTimeout();
            setIsOperating(true);
            const result = await HomeScreenService.stopCurrentOperation('ac', false);
            if (result.success) {
              message.success('停止供电成功');
              // 成功后3秒冷却时间
              timeoutRef.current = setTimeout(() => {
                setIsOperating(false);
                timeoutRef.current = null;
              }, 3000);
            } else {
              message.error(result.message || '停止供电失败');
              // 失败后立即可以再次点击
              setIsOperating(false);
            }
          }
        });
      } else {
        // 清除之前的定时器（如果有的话）
        clearOperatingTimeout();
        setIsOperating(true);
        // 开始供电
        const result = await HomeScreenService.startACPowerSupply();
        if (result.success) {
          message.success('开始供电');
          // 成功后3秒冷却时间
          timeoutRef.current = setTimeout(() => {
            setIsOperating(false);
            timeoutRef.current = null;
          }, 3000);
        } else {
          message.error(result.message || '启动供电失败');
          // 失败后立即可以再次点击
          setIsOperating(false);
        }
      }
    } catch (error) {
      message.error('操作异常，请检查设备状态');
      // 异常后立即可以再次点击
      setIsOperating(false);
    }
  }, [acBtnDisabled, data.ACOutStatus, canGongdian, isOperating, clearOperatingTimeout]);


  // 处理点击设置并机功率比例
  const handleCommIdClick = (pcs: any) => {
    if (pcs.id == 'ACOutPowerPercentage') {
      setCurrentDevice(pcs);
      setInputValue(pcs.value?.toString?.() || '');
      setIsModalVisible(true);
    }
  };

  // 处理确认设置
  const handleOk = async () => {
    if (!currentDevice) return;
    const value = parseInt(inputValue);
    if (isNaN(value)) {
      message.error('请输入有效的数字');
      return;
    } else if (value < 10 || value > 90) {
      message.error('请输入10-90的数值');
      return;
    }

    setLoading(true);
    try {
      const params = {
        devices: [
          {
            msgId: generateUUID(),
            deviceCode: 'PCS2',
            addresses: [
              {
                name: "ACOutPowerPercentage",
                value: value
              }
            ]
          },
          {
            msgId: generateUUID(),
            deviceCode: 'PCS1',
            addresses: [
              {
                name: "ACOutPowerPercentage",
                value: value
              }
            ]
          }
        ]
      };

      const res = await httpPost('/api/RootInterface/WriteCommand', params);

      if (res.code === 0) {
        message.success('设置成功');
        setIsModalVisible(false);
        setInputValue('');
        setCurrentDevice(null);
      } else {
        message.error(res.message || '设置失败');
      }
    } catch (error) {
      message.error('设置失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setInputValue('');
    setCurrentDevice(null);
    keyboardRef.current?.setInput("");
  };

  const handleInputFocus = () => {
    setShowKeyboard(true);
    keyboardRef.current?.setInput("");
  };

  const handleInput = useCallback((v: string) => {
    setInputValue(v);
  }, []);

  const handleKeyboardInput = useCallback((v: string) => {
    console.log(v);
    handleInput(v);
  }, [handleInput]);


  return (
    <div className="relative w-full max-w-[390px] ml-4">
      <Card className="relative w-full h-[467px] border-none">
        <CardContent className="p-0">
          <div className="relative h-[467px]">
            <img
              className="absolute w-[383px] h-[460px] top-[3px] left-1 z-0"
              alt="Rectangle"
              src="/home/<USER>"
            />

            {/* Corner decorations */}
            <img
              className="absolute w-[43px] h-[46px] top-[421px] left-[347px] z-10"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[42px] h-[46px] top-0 left-[348px] z-10"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[43px] h-[46px] top-0 left-0 z-10"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute w-[43px] h-[46px] top-[421px] left-0 z-10"
              alt="Group"
              src="/home/<USER>"
            />

            {/* Header title */}
            <div className="absolute w-[185px] h-[37px] top-[3px] left-[101px] z-20">
              <div className="w-[187px] h-[37px]">
                <div className="relative w-[191px] h-[39px] -top-0.5 left-[-3px]">
                  <div className="absolute w-[92px] top-[7px] left-[42px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#39abff] text-lg text-center tracking-[0] leading-[normal]">
                    交流供电
                  </div>

                  <img
                    className="absolute w-[185px] h-9 top-0.5 left-[3px]"
                    alt="Vector"
                    src="/home/<USER>"
                  />
                  <div className="absolute w-[19px] h-[9px] top-[30px] left-[10.5px] rotate-180">
                    <img
                      className="absolute w-[25px] h-[15px] -top-1 left-[2px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <div className="absolute w-[19px] h-[9px] top-[30px] right-[11px] rotate-180">
                    <img
                      className="absolute w-[25px] h-[15px] -top-1 left-[-3px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <img
                    className="absolute w-[191px] h-[17px] top-0 left-0"
                    alt="Group"
                    src="/home/<USER>"
                  />
                  <div className="absolute w-[11px] h-[11px] top-4 left-[147px] bg-[#00ff50] rounded-[5.28px]" style={{ backgroundColor: acPcsData?.device?.status === 'Connect' ? '#00ff50' : '#858585' }} />
                </div>
              </div>
            </div>

            {/* Main content */}
            <div className="flex flex-col w-[390px] h-full pt-10 px-7 relative z-30">


              {/* Electrical readings card */}
              <Card className="w-full h-[300px] mt-2 rounded-lg [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)] border-none">
                <CardContent className="flex flex-col items-center justify-center gap-[13px] p-4 relative z-40">
                  <div className="flex flex-col h-[300px] w-[324px] p-2 pl-3 gap-2">
                    {acPcsData?.itemList?.map?.((reading: any, index: number) => (
                      <div
                        key={reading.key}
                        className="flex items-center max-h-[40px] w-[300px]"
                        onClick={() => {
                          handleCommIdClick(reading)
                        }}
                      >
                        <span className="[font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#00c7ff] text-xl leading-9 tracking-[0]">
                          {reading.label}
                        </span>
                        <span className="ml-2 font-medium text-[#c7f2ff] tracking-[0] leading-9 text-xl">
                          {reading.value}&nbsp;{reading.unit}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              {/* Start charging button */}
              <Button
                className="w-full h-16 mt-3 rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
                onClick={acOperate}
                disabled={isPrepareOutput}
              >
                <span className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[#81dc4a] text-2xl leading-[18px] whitespace-nowrap tracking-[0]"
                  style={{ color: data.ACOutStatus == 2 ? '#fe4545' : '' }}>
                  {isPrepareOutput ? '供电启动…' : getStatusText(data.ACOutStatus == 2 ? 0 : 1)}
                </span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Modal
        title="设置并机功率比例"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
        okText="确定"
        cancelText="取消"
        className="custom-modal"
        width={900}
        styles={{ body: { height: '400px', overflow: 'hidden' } }}
      >
        <>
          <div className="space-y-4">
            <div className="text-gray-600">
              请输入{currentDevice?.label}：
            </div>
            <Input
              type="number"
              min={10}
              max={90}
              value={inputValue}
              onChange={(e) => handleInput(e.target.value)}
              placeholder="请输入10-90的数值"
              className="w-full"
              onFocus={handleInputFocus}
            />
            <div className="text-sm text-gray-500">
              请输入10-90的数值
            </div>
          </div>
          {showKeyboard && (
            <div className="absolute bottom-16 left-0 w-full z-50 bg-gray-100"
              onTouchStart={(e) => e.stopPropagation()}
              onTouchMove={(e) => e.preventDefault()}
            >
              <div className="flex justify-end p-2">
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  onClick={() => setShowKeyboard(false)}
                  style={{ color: '#000' }}
                />
              </div>
              <Keyboard
                keyboardRef={(r) => (keyboardRef.current = r)}
                layout={{
                  default: ["1 2 3", "4 5 6", "7 8 9", ". 0 {bksp}"]
                }}
                onChange={handleKeyboardInput}
                theme="hg-theme-default custom-keyboard"
                useTouchEvents={true}              // 启用触摸事件支持
                // useMouseEvents={true}              // 启用鼠标事件
                disableCaretPositioning={true}     // 禁用光标定位，避免触摸冲突
              />
            </div>
          )}
        </>
      </Modal>
    </div>

  );

}

export default GongDianSection;
