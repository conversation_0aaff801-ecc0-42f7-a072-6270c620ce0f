import { Card, CardContent } from '@/components/ui/card';
import { Button, message } from 'antd';
import { isNil } from 'lodash';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import ProgressStatus from '../WrapperScreen/ProgessStatus';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import { useGlobalError } from '@/contexts/GlobalErrorContext';
import { formateProgressData, getCurrent, mergeProgressData } from '../useProgress';
import storage from '@/shared/storage';
import {
  getAllDeviceIDs,
  getPorcessTitle,
  MainViewRequestAddress,
  notProgressStatusMap,
} from './config';

// Mock data for development and testing
const mockData = {
  Devices: [
    {
      device: {
        deviceID: 'ComputerDevice1',
        status: 'Connect',
      },
      itemList: [
        {
          name: 'CurrentAllPCSMaxOutPower',
          value: 50.5,
        },
      ],
    },
    {
      device: {
        deviceID: 'PCS1',
        status: 'Connect',
      },
      itemList: [
        {
          name: 'Load_Rate',
          value: 75.3,
        },
        {
          name: 'ACOutPowerPercentage',
          value: 70.2,
        },
      ],
    },
    {
      device: {
        deviceID: 'PCS2',
        status: 'Connect',
      },
      itemList: [
        {
          name: 'Load_Rate',
          value: 82.1,
        },
        {
          name: 'ACOutPowerPercentage',
          value: 30.5,
        },
      ],
    },
  ],
  ACOutStatus: 0,
  DCOutStatus: 0,
};

// 定义电压电流映射关系
// const acParameterMap: any = {
//   'Load_Rate': { label: 'A相电压', unit: 'V' },
//   'Invert_PhaseB_V': { label: 'B相电压', unit: 'V' },
//   'Invert_PhaseC_V': { label: 'C相电压', unit: 'V' },
//   'Invert_PhaseA_I_Group1': { label: 'Ⅰ组A相电流', unit: 'A' },
//   'Invert_PhaseB_I_Group1': { label: 'Ⅰ组B相电流', unit: 'A' },
//   'Invert_PhaseC_I_Group1': { label: 'Ⅰ组C相电流', unit: 'A' },
// };

// 获取状态文本 // status 1:开始、2:补能中 或 0:停止 | 启动1，0为关闭，2为进行中
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '开始供电';
    case 2:
      return '供电中';
    case 0:
      return '停止供电';
    default:
      return '停止供电';
  }
};

function GongDianSection({ data = {} }: any) {
  const [acPcsData, setAcPcsData] = useState<any>({});
  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  const { systemStatus } = useDeviceStore();
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [detailData, setDetailData] = useState<any>({});
  const { err, setError, progressAll } = useGlobalError();

  // 供电流程弹窗状态
  const [openProgressModal, setOpenProgressModal] = useState(false);
  const [progressData, setProgressData] = useState<any>([]);
  const [currentProgressNode, setCurrentProgressNode] = useState<any>({});
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmContent, setConfirmContent] = useState('');
  const [confirmFuncParams, setConfirmFuncParams] = useState<any>(null);
  const gongdianConfig = MainViewRequestAddress.Output.ACOut?.[0];
  const progress = progressAll.acOut;

  useEffect(() => {
    if (JSON.stringify(progress) === '{}') return;
    // 更新流程
    const _progressData = formateProgressData(progress);
    const currentNode = getCurrent(progress);
    setCurrentProgressNode(currentNode);
    setProgressData((prev: any[]) => {
      const merged = mergeProgressData(prev, _progressData);
      storage.set('progressACOut', merged);
      return merged;
    });
  }, [progress]);

  // 获取字段值并处理Content类型的翻译
  const getFieldValue = useCallback(
    (name: string, id: string = 'PCSOverview') => {
      const value = detailData?.[id]?.[name];
      const res = value !== undefined && value !== null ? value : '-';
      //  res是小数时保留两位小数，整数则原样返回
      if (typeof res === 'number' && res % 1 !== 0) {
        return res.toFixed(0);
      }
      return res;
    },
    [detailData],
  );

  // data = mockData
  useEffect(() => {
    const deviceIDs = getAllDeviceIDs(gongdianConfig);

    if (data.Devices?.length > 0) {
      // 电池组
      const list = [];
      const obj: any = { device: {}, itemList: [] };
      for (const d of data.Devices) {
        if (d.device.deviceID === 'ComputerDevice1') {
          obj.device = d.device;
          for (const item of d.itemList) {
            if (item.name == 'CurrentAllPCSMaxOutPower') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                label: '输出功率',
                value: isNil(item.value) ? '--' : item.value,
                unit: 'kw',
              });
            }
          }
          /* //  交流供电详情显示
          const formattedData: any = {};
          if (d.itemList && Array.isArray(d.itemList)) {
            d.itemList.forEach((item: any) => {
              if (item.name) {
                formattedData[item.name] = item.value;
              }
            });
          }
          setDetailData((prev: any) => ({ ...prev, [d.device.deviceID]: formattedData })); */
        } else if (d.device.deviceID === 'PCS1') {
          obj.device = d.device;
          for (const item of d.itemList) {
            if (item.name == 'Load_Rate') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                label: 'PCS1负载率',
                value: isNil(item.value) ? '--' : item.value,
                unit: '%',
              });
            } else if (item.name == 'ACOutPowerPercentage') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                deviceCode: d.device.deviceID,
                label: 'PCS1并机功率比例',
                value: isNil(item.value) ? '--' : item.value,
                unit: '%',
              });
            }
          }
        } else if (d.device.deviceID === 'PCS2') {
          for (const item of d.itemList) {
            if (item.name == 'Load_Rate') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                label: 'PCS2负载率',
                value: isNil(item.value) ? '--' : item.value,
                unit: '%',
              });
            } else if (item.name == 'ACOutPowerPercentage') {
              list.push({
                key: d.device.deviceID + item.name,
                id: item.name,
                label: 'PCS2并机功率比例',
                deviceCode: d.device.deviceID,
                value: isNil(item.value) ? '--' : item.value,
                unit: '%',
              });
            }
          }
        } /* else if (d.device.deviceID === 'PCSOverview') {
          //  交流供电详情显示
          const formattedData: any = {};
          if (d.itemList && Array.isArray(d.itemList)) {
            d.itemList.forEach((item: any) => {
              if (item.name) {
                formattedData[item.name] = item.value;
              }
            });
          }
          setDetailData((prev: any) => ({ ...prev, PCSOverview: formattedData }));
        } */
        if (deviceIDs.includes(d.device.deviceID)) {
          // 交流补能详情显示
          const formattedData: any = {};
          if (d.itemList && Array.isArray(d.itemList)) {
            d.itemList.forEach((item: any) => {
              if (item.name) {
                formattedData[item.name] = item.value;
              }
            });
          }
          setDetailData((prev: any) => ({ ...prev, [d.device.deviceID]: formattedData }));
        }
        // list.sort((a, b) => a.label.localeCompare(b.label));
        obj.itemList = [...list];
        setAcPcsData(obj);
      }
    }
  }, [data?.Devices]);

  useEffect(() => {
    const pcsDevice = acPcsData?.itemList
      ?.filter((item: any) => {
        return item.id === 'ACOutPowerPercentage';
      })
      .sort((a: any, b: any) => {
        return a.deviceCode.localeCompare(b.deviceCode);
      });
    // setAllDevice(pcsDevice);
  }, [acPcsData]);
  // 判断是否可以供电
  const canGongdian = useMemo(() => {
    if (systemStatus === 2) {
      return !!systemStatus;
    } else {
      return false;
    }
  }, [systemStatus]);

  // 判断直流是否可以供电
  const dcBtnDisabled = useMemo(() => {
    if (data.DCOutStatus != 0 || !canGongdian) {
      return true; // 不可以供电
    } else {
      return false;
    }
  }, []);

  // 判断交流是否可以供电
  const acBtnDisabled = useMemo(() => {
    if (data.ACOutStatus != 0 || !canGongdian) {
      return true; // 不可以供电
    } else {
      return false;
    }
  }, []);

  const isPrepareOutput = useMemo(() => {
    if (data.ACOutStatus !== undefined && notProgressStatusMap.includes(data.ACOutStatus)) {
      setProgressData([]);
      storage.set('progressACOut', []);
    }
    return !notProgressStatusMap.includes(data.ACOutStatus);
  }, [data.ACOutStatus]);

  /*   // 获取图标
    const getStatusIcon = (status: number) => {
      return (status == 0 || !status) ? '/home/<USER>' : '/home/<USER>';
    };
   */

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  const acOperate = useCallback(async () => {
    if (!canGongdian && data.ACOutStatus !== 2) {
      message.warning('当前状态无法供电');
      return;
    }

    if (isOperating) {
      message.warning('操作冷却中，请稍后再试');
      return;
    }
    // @ts-ignore
    setError((prev: any) => ({ ...prev, acOut: '',content:'' }));

    try {
      setProgressData([]);
      storage.set('progressACOut', []);
      if (data.ACOutStatus == 2) {
        setConfirmVisible(true);
        setConfirmContent(`确认是否停止供电？`);
        // 存储当前操作的参数，而不是函数
        const currentParams = {
          type: 'stop',
        };
        setConfirmFuncParams(currentParams);
      } else {
        setConfirmContent(`确认是否开始供电？`);
        // 存储当前操作的参数
        const currentParams = {
          type: 'start',
        };
        setConfirmFuncParams(currentParams);
        setConfirmVisible(true);
      }
    } catch (error) {
      message.error('操作异常，请检查设备状态');
      // 异常后立即可以再次点击
      setIsOperating(false);
    }
  }, [acBtnDisabled, data.ACOutStatus, canGongdian, isOperating, clearOperatingTimeout]);

  // 停止供电确认
  const handleConfirm = async (params: any) => {
    setConfirmLoading(true);
    setOpenProgressModal(true);
    try {
      setConfirmVisible(false);
      if (params.type === 'stop') {
        await handleStopPowerSupply();
      } else {
        await handleStartPowerSupply();
      }
    } catch (error) {
      handleOperationError(error, '操作异常，请检查设备状态');
    } finally {
      cleanupOperation();
    }
  };

  // 处理停止供电
  const handleStopPowerSupply = async () => {
    try {
      setIsOperating(true);
      const result = await HomeScreenService.stopCurrentOperation('ac', false);

      if (result.success) {
        message.success('停止供电成功');
        setOperationCooldown();
      } else {
        message.error(result.message || '停止供电失败');
        setIsOperating(false);
      }
    } catch (error) {
      // setError({
      //   // @ts-ignore
      //   content: error?.message || '停止供电失败，请检查设备状态',
      //   type: 'acOut',
      // });
      message.error('停止供电失败，请检查设备状态');
      setIsOperating(false);
    }
  };

  // 处理开始供电
  const handleStartPowerSupply = async () => {
    try {
      clearOperatingTimeout();
      setIsOperating(true);

      const result = await HomeScreenService.startACPowerSupply();

      if (result.success) {
        message.success('开始供电');
        setOperationCooldown();
      }
    } catch (error) {
      // setError({
      //   type: 'acOut',
      //   content: error.message || '启动供电失败',
      // });
      // @ts-ignore
      message.error(error.message || '启动供电失败');
      setIsOperating(false);
    }
  };

  // 设置操作冷却时间
  const setOperationCooldown = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOperating(false);
      timeoutRef.current = null;
    }, 3000);
  };

  // 处理操作错误
  const handleOperationError = (error: any, defaultMessage: string) => {
    message.error(defaultMessage);
    setIsOperating(false);
  };

  // 清理操作状态
  const cleanupOperation = () => {
    setProgressData([]);
    storage.set('progressACOut', []);
    setConfirmLoading(false);
    setConfirmVisible(false);
  };

  useEffect(() => {
    // 恢复本地存储流程数据
    const progressData = storage.get('progressACOut');
    if (progressData?.length > 0 && isPrepareOutput) {
      setProgressData(progressData);
    }
  }, [isPrepareOutput]);

  return (
    <div className="relative ml-4 w-full max-w-[390px]">
      <Card className="relative h-[467px] w-full border-none">
        <CardContent className="p-0">
          <div className="relative h-[467px]">
            <img
              className="absolute left-1 top-[3px] z-0 h-[460px] w-[383px]"
              alt="Rectangle"
              src="/home/<USER>"
            />

            {/* Corner decorations */}
            <img
              className="absolute left-[347px] top-[421px] z-10 h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-[348px] top-0 z-10 h-[46px] w-[42px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-0 top-0 z-10 h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-0 top-[421px] z-10 h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />

            {/* Header title */}
            <div className="absolute left-[101px] top-[3px] z-20 h-[37px] w-[185px]">
              <div className="h-[37px] w-[187px]">
                <div className="relative -top-0.5 left-[-3px] h-[39px] w-[191px]">
                  <div className="absolute left-[42px] top-[7px] w-[92px] text-center text-lg font-medium leading-[normal] tracking-[0] text-[#39abff] [font-family:'PingFang_SC-Medium',Helvetica]">
                    交流供电
                  </div>

                  <img
                    className="absolute left-[3px] top-0.5 h-9 w-[185px]"
                    alt="Vector"
                    src="/home/<USER>"
                  />
                  <div className="absolute left-[10.5px] top-[30px] h-[9px] w-[19px] rotate-180">
                    <img
                      className="absolute -top-1 left-[2px] h-[15px] w-[25px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <div className="absolute right-[11px] top-[30px] h-[9px] w-[19px] rotate-180">
                    <img
                      className="absolute -top-1 left-[-3px] h-[15px] w-[25px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <img
                    className="absolute left-0 top-0 h-[17px] w-[191px]"
                    alt="Group"
                    src="/home/<USER>"
                  />
                  <div
                    className="absolute left-[147px] top-4 h-[11px] w-[11px] rounded-[5.28px] bg-[#00ff50]"
                    // style={{ backgroundColor: acPcsData?.device?.status === 'Connect' ? '#00ff50' : '#858585' }}
                    style={{
                      backgroundColor:
                        `${data?.ACOutStatus}` === '0' || !data?.ACOutStatus
                          ? '#858585'
                          : '#00ff50',
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Main content */}
            <div className="relative z-30 flex h-full w-[390px] flex-col px-7 pt-12">
              {/* Electrical readings card */}
              <Card className="mt-2 h-[300px] w-full rounded-lg border-none [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)]">
                <CardContent className="relative z-40 flex h-[300px] flex-col items-center justify-center gap-[13px] p-4">
                  {/* gap-2 p-2 pl-3 */}
                  <div className="-mx-4 flex h-[300px] w-full flex-row flex-wrap">
                    {gongdianConfig?.LabelGoup?.map((item: any) => {
                      if (!item.Children || !item.Children?.length) {
                        const value = getFieldValue(
                          item.Name || '',
                          item.DeviceID || gongdianConfig.DeviceID,
                        );
                        const unit = item.Unit || '';
                        const valueLength = `${value}${unit}`.length;
                        return (
                          <div className="flex w-full md:w-1/2" key={item.Label}>
                            <span className="text-[18px] tracking-[0] text-[#9CCEFF] [font-family:'PingFang_SC-Medium',Helvetica]">
                              {item.Label}:
                            </span>
                            <div
                              className={`ml-[2px] tracking-[0] text-[#E3F8FF] ${valueLength > 6 ? 'text-[16px]' : 'text-[18px]'}`}
                            >
                              {value}
                              {unit ? <span className="ml-[2px]">{unit}</span> : ''}
                            </div>
                          </div>
                        );
                      }
                      return (
                        <div className="w-full" key={item.Label}>
                          <span className="text-[18px] tracking-[0] text-[#9CCEFF] [font-family:'PingFang_SC-Medium',Helvetica]">
                            {item.Label}:
                          </span>
                          <div className="flex justify-between">
                            {item.Children?.map((child: any) => {
                              const value = getFieldValue(
                                child.Name || '',
                                item.DeviceID || gongdianConfig.DeviceID,
                              );
                              const unit = child.Unit || '';
                              const valueLength = `${value}${unit}`.length;
                              return (
                                <div key={child.Label} className="flex">
                                  <span className="text-[18px] tracking-[0] text-[#E3F8FF] [font-family:'PingFang_SC-Medium',Helvetica]">
                                    {child.Label}:
                                  </span>
                                  <div
                                    className={`ml-[2px] tracking-[0] text-[#E3F8FF] ${valueLength > 6 ? 'text-[16px]' : 'text-[18px]'}`}
                                  >
                                    {value}
                                    {unit ? <span className="ml-[2px]">{unit}</span> : ''}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}
                    {/* {acPcsData?.itemList?.map?.((reading: any, index: number) => (
                      <div
                        key={`${reading.id}-${index}`}
                        className="flex max-h-[40px] w-[300px] items-center"
                        onClick={() => {
                          handleCommIdClick(reading);
                        }}
                      >
                        <span className="text-xl font-medium leading-9 tracking-[0] text-[#00c7ff] [font-family:'PingFang_SC-Medium',Helvetica]">
                          {reading.label}
                        </span>
                        <span className="ml-2 text-xl font-medium leading-9 tracking-[0] text-[#c7f2ff]">
                          {reading.value}&nbsp;{reading.unit}
                        </span>
                      </div>
                    ))} */}
                  </div>
                </CardContent>
              </Card>

              {/* Start charging button */}
              <div className="mt-3 h-16 w-full">
                {!isPrepareOutput && (
                  <Button
                    className="h-full w-full rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
                    onClick={acOperate}
                    // disabled={`${data?.ACOutStatus}` === '0' || !data?.ACOutStatus || isPrepareOutput}
                    // 只有ACOutStatus为0或为空时才允许点击开始 2点击结束
                    disabled={isPrepareOutput}
                  >
                    <span
                      className="whitespace-nowrap text-2xl font-normal leading-[18px] tracking-[0] text-[#81dc4a] [font-family:'PingFang_SC-Semibold',Helvetica]"
                      style={{ color: data.ACOutStatus == 2 ? '#fe4545' : '' }}
                    >
                      {isPrepareOutput ? '供电启动…' : getStatusText(data.ACOutStatus == 2 ? 0 : 1)}
                    </span>
                  </Button>
                )}
                {isPrepareOutput && progressData && (
                  <div className="h-full w-full">
                    <ProgressStatus
                      // @ts-ignore
                      stepData={progressData || []}
                      current={currentProgressNode}
                      // title="交流供电运行准备"
                      title={getPorcessTitle('ACOutStatus', data.ACOutStatus)}
                      openModal={isPrepareOutput && openProgressModal}
                    />
                  </div>
                )}
              </div>
              {err?.['acOut'] && (
                <div className="text-md mt-2 whitespace-pre-wrap break-all text-[#FF4545]">
                  {err?.['acOut']}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      {/* 操作确认弹窗 */}
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => {
          setConfirmVisible(false);
        }}
        onConfirm={() => handleConfirm(confirmFuncParams)}
        confirmLoading={confirmLoading}
        content={<div className="leading-7">{confirmContent}</div>}
      />
    </div>
  );
}

export default GongDianSection;
