import { Separator } from '@/components/ui/separator';
import { useEffect } from 'react';
import { StatusSection } from '../WrapperScreen/StatusSection';
import BuNengSection from './BuNengSection';
import GongDianSection from './GongDianSection';
import useRealTime from '../useRealTime';
import { useDeviceStore } from '@/store/deviceStore';
import VinSection from '../WrapperScreen/VinSection';
import useProgress from '../useProgress';

export const HomeScreenPage = (): JSX.Element => {
  const { data, /* isConnected = false */ } = useRealTime();
  // // 交流供电
  // const { data: progress } = useProgress({ type: 'ACOut' });
  // // 直流补能
  // const { data: progressDCInput } = useProgress({ type: 'DCInput' });
  // // 交流补能
  // const { data: progressACInput } = useProgress({ type: 'ACInput' });
  // // 并网放电
  // const { data: progressGridOut } = useProgress({ type: 'ACGridOut' });

  const { setGpsDevice, setCellularDevice, setSystemStatus } = useDeviceStore();

  useEffect(() => {
    console.info?.('🚀 杭州！');
    // 更新设备状态
    if (data.Devices?.length > 0) {
      const gpsDevice = data.Devices.find((d: Record<string, any>) => d.device.type === 'GPS');
      const cellularDevice = data.Devices.find((d: Record<string, any>) => d.device.type === '4G');
      setGpsDevice(gpsDevice);
      setCellularDevice(cellularDevice);
    }
    if (data) {
      setSystemStatus(data.CurrentSystemStatus);
    }
  }, [data]);

  return (
    <div className="flex w-full flex-row justify-center bg-[#001857] p-4 pb-1">
      <div className="relative w-[1280px] overflow-hidden bg-[#001857]">
        {/* Header Section */}
        <div className="relative w-full">
          <StatusSection data={data} />
        </div>
        {/* Main Content */}
        <div className="relative mt-4 w-full">
          <div className="w-full [background:linear-gradient(180deg,rgba(0,135,255,0)_0%,rgba(0,135,255,0.26)_100%)]">
            {/* Section Titles */}
            <div className="relative flex w-full justify-around px-6 py-0">
              <VinSection VINCode2={true}/>
              {/* Charging Title */}
              <div className="flex items-center pl-2">
                <div
                  className="h-[38px] w-[550px] whitespace-nowrap text-center text-[26px] font-medium tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]"
                  style={{
                    backgroundImage: `url(/home/<USER>
                    backgroundSize: '550px 38px',
                    lineHeight: '38px',
                  }}
                >
                  补能
                </div>
              </div>

              {/* Power Supply Title */}
              <div className="flex items-center pl-10">
                <div
                  className="h-[38px] w-[249px] whitespace-nowrap text-center text-[26px] font-medium tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]"
                  style={{
                    backgroundImage: `url(/home/<USER>
                    backgroundSize: '249px 38px',
                    lineHeight: '38px',
                  }}
                >
                  供电
                </div>
              </div>
            </div>

            {/* Main Content Sections */}
            <div className="flex w-full flex-wrap gap-4 px-4 py-2.5">
              {/* Left Column */}
              <div className="flex-1">
                {/* <BuNengSection data={data} progressDCInput={progressDCInput} progressACInput={progressACInput} progressGridOut={progressGridOut}/> */}
                <BuNengSection data={data} />
              </div>

              {/* Right Column */}
              <div className="flex-1">
                {/* <GongDianSection data={data} progress={progress}/> */}
                <GongDianSection data={data} />
              </div>
            </div>
          </div>
        </div>

        <Separator className="mt-1 h-[3px] w-full [background:linear-gradient(90deg,rgba(44,223,232,1)_0%,rgba(26,123,219,1)_49%,rgba(219,255,0,1)_100%),linear-gradient(0deg,rgba(217,217,217,1)_0%,rgba(217,217,217,1)_100%)]" />
      </div>
    </div>
  );
};
