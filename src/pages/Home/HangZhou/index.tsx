import React, { useState, useEffect } from 'react';
import useRealTime from '../useRealTime';
import ContentLayout from '../components/ContentLayout';
import { HeaderSection } from '../components/HeaderSection';
import { useDeviceStore } from '@/store/deviceStore';
import { httpGet } from '@/shared/http';
import { useConfigStore } from '@/store/configStore';
import AlarmModal from '@/components/AlarmModal';
import storage from '@/shared/storage';

export const HomeScreenPage = () => {
  const { data } = useRealTime();
  const { setGpsDevice, setCellularDevice, setSystemStatus } = useDeviceStore();
  const { config } = useConfigStore();
  const [visible, setVisible] = useState<boolean>(false);
  const [lastClosedTime, setLastClosedTime] = useState<number>(0); // 上次关闭时间戳

  /*  const [config, setConfig] = useState<any>(null);
  
  const getConfig = async () => {
    try {      
      const res = await httpGet('/api/configs/get?fileName=MainViewRequestAddress.cfg');
      setConfig(res);
    } catch (error) {
      // message.error('获取首页配置信息失败');
    }
  };*/

  // 组件挂载时，从storage中读取上次关闭时间
  useEffect(() => {
    const savedLastClosedTime = storage.get('batteryAlarmLastClosedTime');
    if (savedLastClosedTime) {
      setLastClosedTime(savedLastClosedTime);
    }
  }, []);

  useEffect(() => {
    // 正在补能则不提示电量低
    if (data?.CurrentSystemStatus === 3 || data?.CurrentSystemStatus === 5) {
      setVisible(false);
      return;
    }
    // 检查是否需要显示弹窗：SOC < 10 并且距离上次关闭超过10分钟
    const now = Date.now();
    const tenMinutes = 10 * 60 * 1000;
    if (data.SOC < 10 && now - lastClosedTime > tenMinutes) {
      setVisible(true);
    }
  }, [data?.SOC, data?.ts, lastClosedTime]);

  useEffect(() => {
    // 更新设备状态
    if (data.Devices?.length > 0) {
      const gpsDevice = data.Devices.find((d: Record<string, any>) => d.device.type === 'GPS');
      const cellularDevice = data.Devices.find((d: Record<string, any>) => d.device.type === '4G');
      setGpsDevice(gpsDevice);
      setCellularDevice(cellularDevice);
    }
    if (data) {
      setSystemStatus(data.CurrentSystemStatus);
    }
  }, [data]);

  const handleClose = () => {
    setVisible(false);
    const currentTime = Date.now();
    setLastClosedTime(currentTime);
    storage.set('batteryAlarmLastClosedTime', currentTime);
  };
  return (
    <div className="flex w-full flex-row justify-center bg-[#001857] p-4 pb-1">
      {config ? (
        <div className="relative w-[1280px] overflow-hidden bg-[#001857]">
          <div className="relative w-full">
            <HeaderSection data={data} config={config} />
          </div>
          <ContentLayout ViewType={config.ViewType} config={config} data={data}></ContentLayout>
        </div>
      ) : (
        <div className="flex h-[700px] w-full items-center justify-center text-2xl">
          <h1>请先配置首页需要展示的内容</h1>
        </div>
      )}
      <AlarmModal
        visible={visible}
        content={'电量过低，请及时充电'}
        onClose={handleClose}
        onConfirm={handleClose}
      />
    </div>
  );
};
