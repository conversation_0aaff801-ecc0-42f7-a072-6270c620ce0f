import { Separator } from '@/components/ui/separator';
import { useCallback, useEffect, useRef, useState } from 'react';
import { StatusSection } from '../WrapperScreen/StatusSection';
import BuNengSection from './BuNengSection';
import GongDianSection from './GongDianSection';
import useRealTime from '../useRealTime';
import { useDeviceStore } from '@/store/deviceStore';
import { AutoComplete, Button, Checkbox, Form, message, Modal } from 'antd';
import styles from './index.module.less';
import { httpGet, httpPut } from '@/shared/http';
import { CloseOutlined, DownOutlined } from '@ant-design/icons';
import Keyboard from 'react-simple-keyboard';

export const HomeScreenPage = (): JSX.Element => {
  const { data, isConnected = false } = useRealTime();
  const { setGpsDevice, setCellularDevice, setSystemStatus } = useDeviceStore()

  const [vinSetVisible, setVinSetVisible] = useState<boolean>(false);
  const [vin, setVin] = useState<string>('');
  const [vinChecked, setVinChecked] = useState<Boolean | undefined>(false);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);
  const [form] = Form.useForm();
  const [options, setOptions] = useState<any>([]);

  // 可下拉选择的vin码数据
  useEffect(() => {
    const getVinCode = async () => {
      try {
        let res = await httpGet('/api/configs/vin/get');
        const _options = res?.data?.vinItems?.map((item: any) => ({ value: item.code })) || [];
        if (res?.data?.currentSelectCode) {
          setVin(res?.data?.currentSelectCode);
          form.setFieldValue('vin', res?.data?.currentSelectCode);
          setVinChecked(!!res?.data?.currentSelectCode);
        }
        setOptions(_options);
      } catch (error) {}
    };
    getVinCode();
  }, [vinSetVisible]);

  useEffect(() => {
    console.info?.('🚀 杭州！');
    // 更新设备状态
    if (data.Devices?.length > 0) {
      const gpsDevice = data.Devices.find((d: Record<string, any>) => d.device.type === 'GPS')
      const cellularDevice = data.Devices.find((d: Record<string, any>) => d.device.type === '4G')
      setGpsDevice(gpsDevice)
      setCellularDevice(cellularDevice)
    }
    if (data) {
      setSystemStatus(data.CurrentSystemStatus);
    }
  }, [data]);

  const validator = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    const pattern = /^[A-Z0-9]{17}$/;
    if (pattern.test(value)) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('请输入17位大写字母或数字'));
  };
  const handleClose = () => {
    form.resetFields();
    // setVin('');
    setVinSetVisible(false);
    setShowKeyboard(false);
  };

  const handleOk = () => {
    try {
      form.validateFields().then(async (values) => {
        const { vin = '' } = values;
        await httpPut(`/api/configs/vin/save/code`, { code: vin });
        message.success('vin码设置成功');
        setVin(vin);
        setVinChecked(!!vin);
        setVinSetVisible(false);
        setShowKeyboard(false);
      });
    } catch (error) {}
  };

  return (
    <div className="flex w-full flex-row justify-center bg-[#001857] p-4 pb-1">
      <div className="relative w-[1280px] overflow-hidden bg-[#001857]">
        {/* Header Section */}
        <div className="relative w-full">
          <StatusSection data={data} />
        </div>
        {/* Main Content */}
        <div className="relative mt-4 w-full">
          <div className="w-full [background:linear-gradient(180deg,rgba(0,135,255,0)_0%,rgba(0,135,255,0.26)_100%)]">
            {/* Section Titles */}
            <div className="relative flex w-full justify-around px-6 py-0">
              <div
                className="to-blue-500/12 absolute left-4 flex h-10 w-[110px] items-center justify-center gap-2.5 rounded-lg border-2 border-blue-400 bg-gradient-to-br from-blue-500/75 opacity-100"
                onClick={() => {
                  setVinSetVisible(true);
                }}
              >
                vin码
                <Checkbox checked={!!vinChecked} className={styles['custom-check']}></Checkbox>
              </div>
              {/* Charging Title */}
              <div className="flex items-center pl-2">
                <div
                  className="h-[38px] w-[550px] whitespace-nowrap text-center text-[26px] font-medium tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]"
                  style={{
                    backgroundImage: `url(/home/<USER>
                    backgroundSize: '550px 38px',
                    lineHeight: '38px',
                  }}
                >
                  补能
                </div>
              </div>

              {/* Power Supply Title */}
              <div className="flex items-center pl-10">
                <div
                  className="h-[38px] w-[249px] whitespace-nowrap text-center text-[26px] font-medium tracking-[0] text-[#3ae353] [font-family:'PingFang_SC-Medium',Helvetica]"
                  style={{
                    backgroundImage: `url(/home/<USER>
                    backgroundSize: '249px 38px',
                    lineHeight: '38px',
                  }}
                >
                  供电
                </div>
              </div>
            </div>

            {/* Main Content Sections */}
            <div className="flex w-full flex-wrap gap-4 px-4 py-2.5">
              {/* Left Column */}
              <div className="flex-1">
                <BuNengSection data={data} />
              </div>

              {/* Right Column */}
              <div className="flex-1">
                <GongDianSection data={data} />
              </div>
            </div>
          </div>
        </div>

        <Separator className="mt-1 h-[3px] w-full [background:linear-gradient(90deg,rgba(44,223,232,1)_0%,rgba(26,123,219,1)_49%,rgba(219,255,0,1)_100%),linear-gradient(0deg,rgba(217,217,217,1)_0%,rgba(217,217,217,1)_100%)]" />
      </div>
      {/* vin码设置弹窗 */}
      <Modal
        className={styles['custom-modal']}
        open={vinSetVisible}
        title="vin码"
        // onOk={handleOk}
        onCancel={handleClose}
        maskClosable={false}
        footer={[
          <Button key="cancel" type="default" ghost size="large" onClick={handleClose}>
            取消
          </Button>,
          <Button
            type="primary"
            key="submit"
            size="large"
            // loading={loading}
            onClick={handleOk}
          >
            确定
          </Button>,
        ]}
        destroyOnClose
      >
        <p className="relative mb-5 flex items-center">
          当前VIN码: {vin}
          {vin && (
            <div
              className="absolute left-1/2 p-5"
              onClick={() => {
                setVin('');
                form.setFieldValue('vin', '');
              }}
            >
              <img className="h-[30px] w-[30px]" alt="Frame" src="/home/<USER>" />
            </div>
          )}
        </p>
        <Form form={form} onFinish={handleOk}>
          <Form.Item name="vin" validateTrigger="onBlur" rules={[{ validator }]}>
            <AutoComplete
              size="large"
              defaultValue={vin}
              value={vin}
              options={options}
              placeholder="请输入VIN码"
              onFocus={() => setShowKeyboard(true)}
              onSelect={(v) => {
                setVin(v);
                form.setFieldValue('vin', v);
              }}
              suffixIcon={<DownOutlined />}
            />
          </Form.Item>
        </Form>
      </Modal>
      {showKeyboard && (
        <div className="fixed bottom-0 left-0 z-[1001] w-full bg-gray-100">
          <div className="flex justify-end p-2">
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => setShowKeyboard(false)}
              style={{ color: '#000' }}
            />
          </div>
          <Keyboard
            keyboardRef={(r) => (keyboardRef.current = r)}
            input={form.getFieldValue('vin') || ''}
            onChange={(input) => {
              form.setFieldValue('vin', input);
            }}
            // onKeyPress={() => {}}
            onKeyPress={(button) => {
              // 处理删除按钮
              if (button === '{bksp}') {
                const current = form.getFieldValue('vin');
                let newInput = current?.slice(0, -1) || '';
                form.setFieldValue('vin', newInput);
              }
            }}
            theme="hg-theme-default custom-keyboard"
            layout={{
              default: [
                '1 2 3 4 5 6 7 8 9 0',
                'Q W E R T Y U I O P',
                'A S D F G H J K L',
                'Z X C V B N M',
                '{bksp} {space} {enter}',
              ],
            }}
          />
        </div>
      )}
    </div>
  );
};
