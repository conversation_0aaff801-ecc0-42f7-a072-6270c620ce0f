import { ChargingStatusSection } from "../WrapperScreen/ChargingStatusSection";
import { PowerDetailsSection } from "../WrapperScreen/PowerDetailsSection";
import { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { Button, message, Modal } from 'antd';
import { useDeviceStore } from '@/store/deviceStore';
import { HomeScreenService } from '../HomeScreenService';
import useRecharge from '../useRecharge';
import BatteryCard from './BatteryCard';
import { getStatusIcon } from '../utils';
import storage from '@/shared/storage';

// 获取状态文本 // status 1:开始、2:补能中，0:停止 | 启动1，0为关闭，2为进行中
const getStatusText = (status: number) => {
  switch (status) {
    case 1: return '开始补能';
    case 2: return '补能中';
    case 0: return '停止补能';
    default: return '停止补能';
  }
};

function BuNengSection({ activeTab = 'buneng', data = {} }: any) {
  const [chargingPower, setChargingPower] = useState<number>(220);
  const [dcChargingDockData, setDCChargingDockData] = useState<any>({});
  const [dcChargingDockData2, setDCChargingDockData2] = useState<any>({});
  const [pcsData, setPcsData] = useState<any>({});
  const [isChecking, setIsChecking] = useState(false);
  const { data: wsData } = useRecharge();

  // "CurrentSystemStatus":1, //当前系统状态，1为关机，2为待机，3为补能，4为供电，5 为补能并供电
  // 补能可以供电 供电不能补能
  const { systemStatus, acInputMaxPower = 380, acInputDefaultPower = 220 } = useDeviceStore();

  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 默认值
  useEffect(() => {
    const power = storage.get('chargingPower') || acInputDefaultPower;
    setChargingPower(power);
  }, [acInputDefaultPower]);

  const handlePowerChange = (power: number) => {
    setChargingPower(power);
  };


  useEffect(() => {
    if (data.Devices?.length > 0) {
      // 电池组
      for (const d of data.Devices) {
        if (d.device.deviceID === 'DcChargingDock1') {
          setDCChargingDockData(d)
        }
        else if (d.device.deviceID === 'DcChargingDock2') {
          setDCChargingDockData2(d)
        }
        else if (d.device.deviceID === 'PCS1') {
          const obj: any = { device: d.device, itemList: [] };
          for (const item of d.itemList) {
            if (["Grid_PhaseA_V", "Grid_PhaseB_V", "Grid_PhaseC_V", "Grid_PhaseA_I", "Grid_PhaseB_I", "Grid_PhaseC_I"].includes(item.name)) {
              obj.itemList.push(item)
            }
          }
          // list  根据label 排序
          obj.itemList?.sort((a: any, b: any) => a.description?.localeCompare?.(b.description));
          setPcsData(obj)
        }
      }
    }
  }, [data?.Devices]);

  // 判断是否可以补能
  const canCharge = useMemo(() => {
    // 补能可以供电 供电不能补能
    if (systemStatus == 2 || (systemStatus != 4 && systemStatus != 1)) {
      return !!systemStatus;
    } else {
      return false
    }
  }, [systemStatus]);


  // 判断pcs可以补能
  const chargeDisabled = useMemo(() => {
    if (data.ACInputStatus != 0 || !canCharge) {
      return true // 不可以补能
    } else {
      return false
    }
  }, [canCharge]);

  const isCharging = useMemo(() => {
    return data.ACInputStatus == 2
  }, [data.ACInputStatus])

  const isPrepareCharging = useMemo(() => {
    return data.ACInputStatus == 1
  }, [data.ACInputStatus]);

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  const acOperate = useCallback(async () => {
    if (!canCharge) {
      message.warning('当前状态无法补能');
      return;
    }

    if (isOperating) {
      message.warning('操作冷却中，请稍后再试');
      return;
    }

    if (data.ACInputStatus == 2) {
      // 停止补能确认
      Modal.confirm({
        title: '停止补能确认',
        content: '是否停止补能？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          // 清除之前的定时器（如果有的话）
          clearOperatingTimeout();
          setIsOperating(true);
          const result = await HomeScreenService.stopCurrentOperation('ac', true);
          if (result.success) {
            message.success(result.message || '停止补能成功');
            // 成功后3秒冷却时间
            timeoutRef.current = setTimeout(() => {
              setIsOperating(false);
              timeoutRef.current = null;
            }, 3000);
          } else {
            message.error(result.message || '停止补能失败');
            // 失败后立即可以再次点击
            setIsOperating(false);
          }
        }
      });
      return;
    }

    // 开始补能
    if (!chargingPower) {
      message.warning('请设置补能功率');
      return;
    }

    try {
      setIsChecking(true);
      // 清除之前的定时器（如果有的话）
      clearOperatingTimeout();
      setIsOperating(true);
      const hideLoading = message.loading('正在补能启动...');

      // 开始补能
      const result = await HomeScreenService.startACCharging(chargingPower);
      hideLoading();
      if (result.success) {
        message.success(result.message || '开始补能');
        storage.set('chargingPower', chargingPower || acInputMaxPower);
        // 成功后3秒冷却时间
        timeoutRef.current = setTimeout(() => {
          setIsOperating(false);
          timeoutRef.current = null;
        }, 3000);
      } else {
        message.error(result.message || '启动补能失败');
        // 失败后立即可以再次点击
        setIsOperating(false);
      }
    } catch (error) {
      message.error('补能异常，请检查设备状态');
      // 异常后立即可以再次点击
      setIsOperating(false);
    } finally {
      setIsChecking(false);
    }
  }, [data.ACInputStatus, chargingPower, canCharge, isOperating, clearOperatingTimeout, acInputMaxPower, setIsChecking]);

  return (
    <div className="flex mt-2 gap-4">
      {/* Left Column */}
      <div className="flex-1">
        <div className="flex flex-col items-center justify-center">
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center gap-[5px] p-1 rounded-[7px]">
              <img
                className="relative w-10 h-9"
                alt="Frame"
                src="/home/<USER>"
              />

              <div className="inline-flex items-center gap-2.5">
                <div className="mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#3ae353] text-[24px] leading-5 tracking-[0]">
                  直流补能
                </div>
                <div className="w-[30px] h-[30px] bg-[100%_100%]"
                  style={{
                    backgroundImage: `url(${getStatusIcon(data.DCInputStatus)})`,
                    backgroundSize: '100% 100%'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-row items-center justify-center pb-2 mt-1 gap-8">
          <BatteryCard data={dcChargingDockData}
            key={"dcChargingDockData1"}
            index={1}
            BatteryGroupSOC={data.BatteryGroupSOC1}
            acInputStatus={data.ACInputStatus}
          />
          <BatteryCard data={dcChargingDockData2}
            key={"dcChargingDockData2"}
            BatteryGroupSOC={data.BatteryGroupSOC2}
            index={2}
            acInputStatus={data.ACInputStatus} />
        </div>
      </div>

      {/* Right Column */}
      <div className="flex flex-col gap-4 w-[430px] mr-3">
        <div className="flex justify-center items-center gap-[5px]">
          <div className="inline-flex items-start px-2.5 py-1 rounded-[7px]">
            <img
              className="relative w-5 h-7"
              alt="Union"
              src="/home/<USER>"
            />
          </div>

          <div className=" inline-flex justify-center items-center gap-2.5">
            <div className="w-fit mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#3ae353] text-[24px] leading-5 whitespace-nowrap tracking-[0]">
              交流补能
            </div>
            <div className="w-[30px] h-[30px] bg-[100%_100%]"
              style={{
                backgroundImage: `url(${getStatusIcon(data.ACInputStatus)})`,
                backgroundSize: '100% 100%'
              }}
            />
          </div>
        </div>

        <PowerDetailsSection data={pcsData} />
        <ChargingStatusSection
          maxPower={acInputMaxPower}
          value={chargingPower}
          onPowerChange={handlePowerChange}
          disabled={isCharging || isChecking}
        />

        <Button
          className="w-full h-16 mt-1 rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
          onClick={acOperate}
          disabled={!chargingPower || isChecking || isPrepareCharging}
        >
          <span
            className="[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-2xl leading-[18px] whitespace-nowrap tracking-[0]"
            style={{
              color: isCharging ? '#fe4545' : '#81dc4a'
            }}
          >
            {(isChecking || isPrepareCharging) ? '补能启动...' : (isCharging ? '停止补能' : '开始补能')}
          </span>
        </Button>
        <div className='text-xs text-[#fe4545] overflow-hidden'>{wsData.msg || wsData.acChargeErrMsg}</div>
      </div>
    </div>
  );
}

export default BuNengSection;
