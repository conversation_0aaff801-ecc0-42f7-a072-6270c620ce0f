import { BatterySlots } from '@/components/BatterySlots';
import { EchartsProgress } from '@/components/EchartsProgress';
import { Card, CardContent } from '@/components/ui/card';
import { httpPost } from '@/shared/http';
import { formatNullValue, formatterValue } from '@/utils';
import { formatMilliseconds } from '@/utils/formatDate';
import { toFixed } from '@/utils/number';
import { message, Modal } from 'antd';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';


// Data for battery metrics
const batteryMetrics = [
  { label: "电压", unit: "V", field: "VoltageOutputValue" },
  { label: "电流", unit: "A", field: "CurrentOutputValue" },
  { label: "功率", unit: "kw", field: "DCInputBatteryPower" },
  // { label: "本次补能量", unit: "kw", field: "DCInputPowerCapacity" },
  { label: "本次补能时长", unit: "", field: "CumulativeChargingTime", formatter: (v = 0) => formatMilliseconds(Number(v) * 60 * 1000) },
];

const BatteryCard = ({ data = {}, BatteryGroupSOC, index, acInputStatus }: any): JSX.Element => {
  const [soc, setSoc] = useState<any>(0);
  const [detail, setDetail] = useState<any>({});
  const [isOperating, setIsOperating] = useState<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setSoc(toFixed(BatteryGroupSOC || 0))
  }, [BatteryGroupSOC]);

  useEffect(() => {
    if (data?.itemList?.length > 0) {
      const obj: any = {};
      for (const item of data.itemList) {
        if (item.name?.indexOf('BatteryGroupSOC') === 0) {
          setSoc(toFixed(item.value));
        }
        obj[item.name] = item.value;
      }
      setDetail(obj);
    }
  }, [JSON.stringify(data)]);

  const status = useMemo(() => {
    return data?.device?.status === 'Connect';
  }, [data?.device]);

  const dcCharging = useMemo(() => {
    return acInputStatus == 2 || acInputStatus == 1
  }, [acInputStatus])

  // 清除定时器的函数
  const clearOperatingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearOperatingTimeout();
    };
  }, [clearOperatingTimeout]);

  // 充电座补能复位
  const operateDcStop = useCallback((deviceId: string) => {
    // if (!dcCharging) {
    //   message.warning('当前状态不是补能中，无法补能复位');
    //   return;
    // }

    if (isOperating) {
      message.warning('操作冷却中，请稍后再试');
      return;
    }

    Modal.confirm({
      title: '操作确认',
      content: `是否要操作补能复位？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 清除之前的定时器（如果有的话）
          clearOperatingTimeout();
          setIsOperating(true);
          const res = await httpPost('EMS/DCInputPower/Stop', {
            "chargingDockId": deviceId || "DcChargingDock1", //充电座的充电口id
          });
          if (res.result == "successful") {
            message.info('补能复位操作成功！');
            // 成功后3秒冷却时间
            timeoutRef.current = setTimeout(() => {
              setIsOperating(false);
              timeoutRef.current = null;
            }, 3000);
          } else {
            message.error(res?.resultInfo || '补能复位操作失败！');
            // 失败后立即可以再次点击
            setIsOperating(false);
          }
        } catch (error) {
          message.error('操作失败，请重试');
          // 异常后立即可以再次点击
          setIsOperating(false);
        }
      }
    });

  }, [dcCharging, isOperating, clearOperatingTimeout]);

  return (
    <div className="relative w-full max-w-[313px]">
      <div className="relative w-full h-full">
        {/* Background and corner images */}
        <img
          className="w-full h-auto"
          alt="Rectangle"
          src="/home/<USER>"
        />

        <img
          className="absolute bottom-0 right-0 w-[35px] h-[42px]"
          alt="Group"
          src="/home/<USER>"
        />

        <img
          className="absolute top-0 right-0 w-[34px] h-[42px]"
          alt="Group"
          src="/home/<USER>"
        />

        <img
          className="absolute top-0 left-0 w-[35px] h-[42px]"
          alt="Group"
          src="/home/<USER>"
        />

        <img
          className="absolute bottom-0 left-0 w-[35px] h-[42px]"
          alt="Group"
          src="/home/<USER>"
        />

        {/* Battery group header */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[149px] h-[33px]">
          <div className="relative w-[155px] h-[35px]">
            <img
              className="absolute w-[149px] h-[33px] top-0.5 left-[3px]"
              alt="Vector"
              src="/home/<USER>"
            />

            <div className="absolute top-1.5 left-[33px] font-medium text-[#39abff] text-lg text-center tracking-[0] leading-normal [font-family:'PingFang_SC-Medium',Helvetica]">
              {index || 2}#电池组
            </div>

            <div className="absolute w-[9px] h-2 top-[27px] left-[9px]">
              <img
                className="absolute w-[15px] h-3.5 -top-0.5 left-[-1px]"
                alt="Group"
                src="/home/<USER>"
              />
            </div>

            <div className="absolute w-[9px] h-2 top-[27px] left-[136px] rotate-180">
              <img
                className="absolute w-[25px] h-3.5 -top-1 left-[-1px] -rotate-180"
                alt="Group"
                src="/home/<USER>"
              />
            </div>

            <img
              className="absolute w-[155px] h-4 top-0 left-0"
              alt="Group"
              src="/home/<USER>"
            />

            <div className="absolute w-[9px] h-[9px] top-[15px] left-[118px] bg-[#858585] rounded-[4.5px]" style={{ backgroundColor: status ? '#00ff50' : '#858585' }} />
          </div>
        </div>
      </div>
      <Card className="absolute top-[2px] left-[2px] w-full border-0">
        <CardContent className="p-0">
          <div className="relative">
            {/* Content container */}
            <div className="flex flex-col w-[269px] mx-auto mt-9 gap-3">
              {/* SoC section */}
              <div className="relative w-full h-[93px]">
                <div className="flex w-full h-[93px] items-center gap-3.5 pt-[15px] pb-[41px] px-6 rounded-b-lg [background:linear-gradient(0deg,rgba(21,94,216,0.4)_0%,rgba(23,63,129,0)_100%)]">
                  <div className="inline-flex items-center gap-2.5">
                    <div className="w-fit mt-[-1.00px] font-medium text-[#00c7ff] text-base leading-5 whitespace-nowrap tracking-[0]">
                      SoC
                    </div>
                    <div className="[font-family:'PingFang_SC-Medium',Helvetica]  font-medium text-[#d9d9d9] text-2xl tracking-[0] leading-[26px] whitespace-nowrap">
                      {formatNullValue(soc)}
                    </div>
                    <div className="font-medium text-[#00c7ff] text-xl tracking-[0] leading-5 whitespace-nowrap">
                      %
                    </div>
                  </div>
                </div>

                {/* Progress bar */}
                <div className="flex w-[219px] h-[24px] items-center absolute top-14 left-[25px]">
                  <div className="flex w-full">
                    <EchartsProgress value={soc} height={20} colors={['#68BC34', '#81DD4B', '#FFDD36']} />
                  </div>
                </div>
              </div>

              {/* Battery metrics section */}
              <Card className="w-full rounded-lg border-0 [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)]">
                <CardContent className="flex flex-col gap-[18px] pt-4 pb-[21px] px-6">
                  {batteryMetrics.map((metric, index) => (
                    <div key={index} className="inline-flex items-end gap-1">
                      <div className="inline-flex items-center">
                        <div
                          className={`${index > 2 ? "w-[113px]" : "w-[57px]"} mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#00c7ff] text-l leading-[18px] tracking-[0]`}
                        >
                          {metric.label}
                        </div>
                      </div>

                      <div className="inline-flex items-end">
                        <div className="inline-flex items-center">
                          <div className="mt-[-1.00px] [font-family:'PingFang_SC-Medium',Helvetica] font-medium text-[#c7f2ff] text-l leading-[18px] whitespace-nowrap tracking-[0]">
                            {formatterValue({ value: detail[metric.field], formatter: metric.formatter }, metric.unit)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
              {/* <BatterySlots height={'50px'} status={[status]} /> */}
              <BatterySlots height={'80px'} status={[status]} >
                <div style={{ width: '70px', height: '26px', padding: 3, background: 'linear-gradient(175deg, rgba(0, 135.47, 255, 0.65) 0%, rgba(0, 135.47, 255, 0.08) 100%)', borderRadius: 7.76, outline: '0.78px #39ABFF solid', outlineOffset: '-0.78px', justifyContent: 'center', alignItems: 'center', gap: 3.10, display: 'inline-flex' }}>
                  <div
                    onClick={() => operateDcStop(data?.device?.deviceID)}
                    style={{
                      color: isOperating ? '#999' : '#FE4545',
                      cursor: isOperating ? 'not-allowed' : 'pointer',
                      fontSize: '12px',
                      fontWeight: '400',
                      wordWrap: 'break-word',
                      opacity: isOperating ? 0.6 : 1
                    }}
                  >
                    {'补能复位'}
                  </div>
                </div>
              </BatterySlots>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


export default BatteryCard;
