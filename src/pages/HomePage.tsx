import React from 'react';
import styles from './HomePage.module.less';

export const HomePage: React.FC = () => {
    const stats = [
        { label: '剩余电量', value: '124', unit: 'kwh' },
        { label: '装机容量', value: '256', unit: 'kwh' },
        { label: '今日充电量', value: '256', unit: 'kwh' },
        { label: '今日放电量', value: '256', unit: 'kwh' },
        { label: '累计充电量', value: '19036', unit: 'kwh' },
        { label: '累计放电量', value: '19036', unit: 'kwh' },
    ];

    const deviceStats = [
        { label: 'BMS', value: '5/5' },
        { label: 'PCS', value: '3/5' },
        { label: '液冷设备', value: '在线' },
        { label: '充电桩', value: '3/2' },
        { label: '告警', value: '1' },
    ];

    return (
        <div className={styles.homePage}>
            <div className={styles.topStats}>
                {stats.map((item, index) => (
                    <div key={index} className={styles.statItem}>
                        <div className={styles.value}>
                            {item.value}
                            <span className={styles.unit}>{item.unit}</span>
                        </div>
                        <div className={styles.label}>{item.label}</div>
                    </div>
                ))}
            </div>

            <div className={styles.socBar}>
                <div className={styles.label}>SoC 65%</div>
                <div className={styles.progressBar}>
                    <div className={styles.progress} style={{ width: '65%' }} />
                </div>
            </div>

            <div className={styles.deviceStats}>
                {deviceStats.map((item, index) => (
                    <div key={index} className={styles.deviceItem}>
                        <div className={styles.value}>{item.value}</div>
                        <div className={styles.label}>{item.label}</div>
                    </div>
                ))}
            </div>

            <div className={styles.mainContent}>
                {/* 左侧直流充电座 */}
                <div className={styles.leftPanel}>
                    <div className={styles.title}>直流充电座</div>
                    {/* 电池组内容 */}
                </div>

                {/* 右侧交流快插连接 */}
                <div className={styles.rightPanel}>
                    <div className={styles.title}>交流快插连接</div>
                    {/* 充电口状态内容 */}
                </div>
            </div>
        </div>
    );
};
