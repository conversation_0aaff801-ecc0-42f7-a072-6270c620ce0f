import { RhButton } from '@/components/ui/pagbtn';
import ProgressStatus from '../Home/WrapperScreen/ProgessStatus';
import {
  CounterReducer,
  ContextProvider,
  PrevPropsState,
  EffectTest,
  ButtonWithTooltip,
} from './components';
import { MemoUseCallback, Memo } from './components';
import { SearchRefs, ButtonRefs } from './components';
// import RCModal from '@/components/RCModal/index.jsx';
import { useState } from 'react';
// import RCModal from '@/components/RCModal';
import CronEditor from '@/components/CronEditor';

export const TestPage: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [cronValue, setCronValue] = useState<string>('* * * * * ?');

  const handleCronChange = (value: string) => {
    console.log('Cron表达式变更为:', value);
    setCronValue(value);
  };

  return (
    <div className="grid grid-flow-col grid-rows-7 gap-4 lg:grid-rows-3 p-6">
      {/* <CounterReducer />

      <ContextProvider />

      <SearchRefs />

      <ButtonRefs />

      <PrevPropsState />

      <MemoUseCallback />

      <Memo />

      <EffectTest />

      <ButtonWithTooltip /> */}
      <div className="h-[60px] w-[400px]">
        {/* @ts-ignore */}
        {/* <ProgressStatus stepData={testdata} current={testdata[2]} title='交流供电充电运行准备' /> */}
      
      </div>
      
      {/* CronEditor测试用例 */}
      <div className="col-span-full p-6  rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Cron表达式编辑器测试</h2>
        <div className="mb-4">
          <p className="text-gray-500 mb-2">点击"编辑"按钮打开Cron表达式编辑器</p>
          <p className="text-gray-500">当前Cron表达式: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{cronValue}</span></p>
        </div>
        <CronEditor
          value={cronValue}
          onChange={handleCronChange}
          disabled={false}
          defaultValue="0 0/30 * * * ?"
        />
      </div>
    </div>
  );
};
