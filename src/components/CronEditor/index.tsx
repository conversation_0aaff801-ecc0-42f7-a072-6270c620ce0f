import React, { useState, useRef, useEffect } from 'react';
import { Tabs, Button, Input, message } from 'antd';
import RCModal from '@/components/RCModal';
import parser from 'cron-parser';
import moment from 'moment';
import { unset } from 'lodash';
import { RhKeyboard } from '../RhKeyboard';

import SecondEditor from './components/SecondEditor';
import MinuteEditor from './components/MinuteEditor';
import HourEditor from './components/HourEditor';
import DayEditor from './components/DayEditor';
import MonthEditor from './components/MonthEditor';
import WeekEditor from './components/WeekEditor';
import YearEditor from './components/YearEditor';
import './index.less';

interface CronEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  defaultValue?: string;
  onFocus?: () => void;
  [key: string]: any;
}

const radioStyle = {
  display: 'block',
  lineHeight: '30px',
  padding: '5px',
};
export const t = (defaultMsg: string) => {
  return defaultMsg;
};

const CronEditor: React.FC<CronEditorProps> = ({
  value: propValue,
  onChange,
  disabled = false,
  defaultValue,
  onFocus,
  second,
  minute,
  hour,
  day,
  month,
  week,
  year,
  ...config
}) => {
  const [cron, setCron] = useState<string[]>(['*', '*', '*', '*', '*', '?', '*']);
  const [cronText, setCronText] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [position, setPosition] = useState<'top' | 'bottom'>('bottom');
  // 共享键盘状态和引用
  const keyboardRef = useRef<any>(null);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [activeInputType, setActiveInputType] = useState<
    'cronText' | 'between' | 'fromEvery' | 'lastWeekDay'
  >('cronText');
  const [activeInputId, setActiveInputId] = useState<string>('');
  const [keyboardInputHandler, setKeyboardInputHandler] = useState<(input: string) => void>(
    () => {},
  );

  useEffect(() => {
    if (propValue) {
      const arr = propValue.split(' ');
      setCron(arr);
      setCronText(propValue);
    } else if (defaultValue) {
      const arr = defaultValue.split(' ');
      setCron(arr);
      setCronText(defaultValue);
    }
  }, [propValue, defaultValue]);

  // 处理键盘输入变化
  const handleKeyboardChange = (input: string) => {
    if (keyboardInputHandler) {
      keyboardInputHandler(input);
    }
  };

  // 配置共享键盘用于主输入框
  const configureKeyboardForCronText = () => {
    setPosition('top');
    setActiveInputType('cronText');
    setActiveInputId('cronText');
    setKeyboardInputHandler(() => {
      return (input: string) => {
        setCronText(input);
      };
    });
    setShowKeyboard(true);
  };

  // 配置共享键盘用于子组件
  const configureKeyboardForChild = (
    type: 'between' | 'fromEvery' | 'lastWeekDay',
    id: string,
    handler: (input: string) => void,
    initialValue: string,
  ) => {
    setActiveInputType(type);
    setActiveInputId(id);
    setKeyboardInputHandler(() => handler);
    setShowKeyboard(true);
    setPosition('bottom');
    // 设置键盘初始值
    setTimeout(() => {
      keyboardRef.current?.setInput?.(initialValue);
    }, 0);
  };

  const cronChange = (index: number, value: string) => {
    const newCron = [...cron];
    newCron[index] = value;
    setCron(newCron);
    // 互斥
    if (index === 3 && value !== '?' && value !== '*') {
      newCron[5] = '?';
    }
    if (index === 5 && value !== '?' && value !== '*') {
      newCron[3] = '?';
    }
    const newCronText = newCron.join(' ');
    setCronText(newCronText);
  };

  const secondChange = (value: string) => {
    cronChange(0, value);
  };

  const minuteChange = (value: string) => {
    cronChange(1, value);
  };

  const hourChange = (value: string) => {
    cronChange(2, value);
  };

  const dayChange = (value: string) => {
    cronChange(3, value);
  };

  const monthChange = (value: string) => {
    cronChange(4, value);
  };

  const weekChange = (value: string) => {
    cronChange(5, value);
  };

  const yearChange = (value: string) => {
    cronChange(6, value);
  };

  const validateCronExpression = (expression: string | null): any => {
    if (showModal && expression !== null && !expression) {
      message.error('请输入cron表达式');
      return false;
    }
    if (expression === null) {
      return false;
    }
    const arr = expression.split(' ');
    if (arr.length > 7) {
      message.error('Cron表达式不能超过7位');
      return false;
    }

    // 检查年份格式 (如果存在)
    if (arr.length === 7) {
      const yearValue = arr[6];
      // 年份格式：只允许 YYYY-YYYY 格式

      const yearRegex = /^(\d{4}-\d{4}|\*)$/;
      if (!yearRegex.test(yearValue)) {
        throw new Error(t('年份格式必须为 YYYY-YYYY 或 *'));
      }

      // 检查年份范围的合理性
      const [startYear, endYear] = yearValue.split('-').map(Number);
      if (startYear >= endYear) {
        throw new Error(t('结束年份必须大于开始年份'));
      }
    }
    // // // 校验年份
    // if (arr[6] !== '*' && arr[6] !== '?' && !arr[6].match(/^\d+$/)) {
    //   message.error('年份格式不正确');
    //   return false;
    // }
    try {
      // 解析前6位
      const cronForParsing = arr.slice(0, 6).join(' ');
      return parser.parseExpression(cronForParsing);
      // return parser.parseExpression(expression, {
      //   currentDate: new Date(),
      //   tz: 'Asia/Shanghai',
      // });
    } catch (e) {
      message.error('Cron表达式格式不正确');
      return false;
    }
  };

  const handleOk = () => {
    const interval = validateCronExpression(cronText);
    if (interval && cronText !== null) {
      onChange && onChange(cronText);
      setShowModal(false);
    }
  };

  const CronText = ({ text, value: _value }: { text: string; value: string }) => (
    <div>
      <div>{text}</div>
      <Input disabled value={_value} />
    </div>
  );

  // 向子组件传递的配置
  const childComponentProps = {
    configureKeyboard: configureKeyboardForChild,
  };

  return (
    <>
      <RCModal
        showModal={showModal}
        onCancel={() => setShowModal(false)}
        width={1060}
        title={t('表达式')}
        onOk={handleOk}
        className="cst"
      >
        <div>
          <Tabs
            className="cron-tab"
            defaultActiveKey="second"
            items={[
              {
                key: 'second',
                label: t('秒'),
                children: (
                  <SecondEditor
                    onChange={secondChange}
                    value={cron[0]}
                    radioStyle={radioStyle}
                    {...config}
                    {...second}
                    {...childComponentProps}
                  />
                ),
              },
              {
                key: 'minute',
                label: t('分'),
                children: (
                  <MinuteEditor
                    onChange={minuteChange}
                    value={cron[1]}
                    radioStyle={radioStyle}
                    {...config}
                    {...minute}
                    {...childComponentProps}
                  />
                ),
              },
              {
                key: 'hour',
                label: t('时'),
                children: (
                  <HourEditor
                    onChange={hourChange}
                    value={cron[2]}
                    radioStyle={radioStyle}
                    {...config}
                    {...hour}
                    {...childComponentProps}
                  />
                ),
              },
              {
                key: 'day',
                label: t('日'),
                children: (
                  <DayEditor
                    onChange={dayChange}
                    value={cron[3]}
                    radioStyle={radioStyle}
                    {...config}
                    {...day}
                    {...childComponentProps}
                  />
                ),
              },
              {
                key: 'month',
                label: t('月'),
                children: (
                  <MonthEditor
                    onChange={monthChange}
                    value={cron[4]}
                    radioStyle={radioStyle}
                    {...config}
                    {...month}
                    {...childComponentProps}
                  />
                ),
              },
              {
                key: 'week',
                label: t('周'),
                children: (
                  <WeekEditor
                    onChange={weekChange}
                    value={cron[5]}
                    radioStyle={radioStyle}
                    {...config}
                    {...week}
                    {...childComponentProps}
                  />
                ),
              },
              {
                key: 'year',
                label: t('年'),
                children: (
                  <YearEditor
                    onChange={yearChange}
                    value={cron[6]}
                    radioStyle={radioStyle}
                    {...config}
                    {...year}
                    {...childComponentProps}
                  />
                ),
              },
            ]}
          />
          <div>
            <div className="mb-2.5 font-medium">{t('表达式')}</div>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gridGap: '8px' }}>
              <CronText text={t('秒')} value={cron[0]} />
              <CronText text={t('分')} value={cron[1]} />
              <CronText text={t('时')} value={cron[2]} />
              <CronText text={t('日')} value={cron[3]} />
              <CronText text={t('月')} value={cron[4]} />
              <CronText text={t('周')} value={cron[5]} />
              <CronText text={t('年')} value={cron[6]} />
            </div>
            <div className="mt-2">
              <div className="mb-2.5 font-medium">{t('Cron表达式')}</div>
              <div className="flex items-center">
                <Input
                  value={cronText || ''}
                  onChange={(e) => setCronText(e.target.value)}
                  onFocus={configureKeyboardForCronText}
                />
              </div>
            </div>
          </div>
          <div className="mt-2">
            <div className="mb-2.5 font-medium">{t('最近5次运行时间')}</div>
            <div className="">
              {(() => {
                try {
                  const interval = validateCronExpression(cronText);
                  return Array(5)
                    .fill(0)
                    .map((_, i) => {
                      const next = interval.next();
                      return (
                        <div key={i} className="theme-color mb-1">
                          • {moment(next.toDate()).format('YYYY-MM-DD HH:mm:ss')}
                        </div>
                      );
                    });
                } catch (e) {
                  return null;
                }
              })()}
            </div>
          </div>
        </div>
        {/* 共享键盘组件 */}
        <RhKeyboard
          init={(r: any) => (keyboardRef.current = r)}
          show={showKeyboard}
          onClose={() => setShowKeyboard(false)}
          onChange={handleKeyboardChange}
          // layoutType={'chinese'}
          position={position}
        />
      </RCModal>
      <div
        className="flex w-full items-center justify-between"
        // style={{ backgroundColor: '#F3F6F9' }}
      >
        <Input
          className="param-input"
          style={{ maxWidth: '330px', width: '330px' }}
          type="text"
          onClick={() => setShowModal(true)}
          onFocus={() => onFocus && onFocus()}
          value={propValue || ''}
          placeholder={`请输入`}
        />
      </div>
    </>
  );
};

export default CronEditor;
