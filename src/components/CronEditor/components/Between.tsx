import React from 'react';
import { Input, Space } from 'antd';
import type { InputProps } from 'antd';

const style: React.CSSProperties = {
  width: 70,
  textAlign: 'center',
  paddingRight: '0px',
  paddingLeft: '0px',
};

interface BetweenProps {
  value: string;
  onChange: (value: string) => void;
  min?: number;
  max: number;
  // 从父组件接收的共享键盘配置方法（改为可选）
  configureKeyboard?: (
    type: 'between' | 'fromEvery',
    id: string,
    handler: (input: string) => void,
    initialValue: string,
  ) => void;
}

const Between: React.FC<BetweenProps> = ({ value, onChange, min = 0, max, configureKeyboard }) => {
  const splits = value.split('-');
  const minValue = parseInt(splits[0], 0);
  const maxValue = parseInt(splits[1], 0);

  const notifyChange = (_minValue: number, _maxValue: number) => {
    const s = `${_minValue}-${_maxValue}`;
    onChange(s);
  };

  const handleMinChange = (_minValue: number) => {
    notifyChange(_minValue, maxValue);
  };

  const handleMaxChange = (_maxValue: number) => {
    notifyChange(minValue, _maxValue);
  };

  // 处理键盘输入
  const handleMinKeyboardInput = (input: string) => {
    // 允许空字符串输入
    if (input === '') {
      handleMinChange(0);
      return;
    }

    const numValue = parseInt(input || '0', 10);
    if (!isNaN(numValue)) {
      // 放宽范围限制，只确保值在组件的整体min和max范围内
      const validValue = Math.max(min, Math.min(max, numValue));
      handleMinChange(validValue);
    }
  };

  const handleMaxKeyboardInput = (input: string) => {
    // 允许空字符串输入
    if (input === '') {
      handleMaxChange(max);
      return;
    }

    const numValue = parseInt(input || '0', 10);
    if (!isNaN(numValue)) {
      // 放宽范围限制，只确保值在组件的整体min和max范围内
      const validValue = Math.max(min, Math.min(max, numValue));
      handleMaxChange(validValue);
    }
  };

  // 处理输入框聚焦
  const handleInputFocus = (inputType: 'min' | 'max') => {
    if (configureKeyboard) {
      configureKeyboard(
        'between',
        `between-${inputType}`,
        inputType === 'min' ? handleMinKeyboardInput : handleMaxKeyboardInput,
        inputType === 'min' ? minValue.toString() : maxValue.toString(),
      );
    }
  };

  return (
    <>
      <Space.Compact
        style={{ display: 'inline-block', verticalAlign: 'middle', marginLeft: '5px' }}
      >
        <Input
          style={style}
          placeholder="Minimum"
          min={min}
          max={maxValue}
          value={minValue}
          onChange={(e) => handleMinChange(parseInt(e.target.value, 0) || 0)}
          type="number"
          onFocus={() => handleInputFocus('min')}
        />
        <Input
          style={{
            width: 30,
            borderLeft: 0,
            pointerEvents: 'none',
            backgroundColor: 'transparent',
          }}
          placeholder="~"
          disabled
        />
        <Input
          style={{ ...style, borderLeft: 0 }}
          placeholder="Maximum"
          min={minValue}
          max={max}
          value={maxValue}
          onChange={(e) => handleMaxChange(parseInt(e.target.value, 0) || 0)}
          type="number"
          onFocus={() => handleInputFocus('max')}
        />
      </Space.Compact>
    </>
  );
};

export default Between;
