import React from 'react';
import InputNumber from './InputNumber';

// 定义 WeekDay 的 Props 接口
export interface WeekDayProps {
  value: string;
  onChange: (value: string) => void;
  configureKeyboard?: (type: 'between' | 'fromEvery', id: string, handler: (input: string) => void, initialValue: string) => void;
}

const inputNumberStyle: React.CSSProperties = { margin: '0 5px' };
const t = (defaultMsg: string): string => {
  return defaultMsg;
};

// @i18n('transmit')
const WeekDay: React.FC<WeekDayProps> = ({ value, onChange, configureKeyboard }) => {
  const splits = value.split('#');
  const week = splits[0];
  const weekDay = splits[1];

  const notifyChange = (_week: number, _weekDay: number): void => {
    const s = `${_week}#${_weekDay}`;
    onChange && onChange(s);
  };

  const handleWeekChange = (v: number): void => {
    notifyChange(v, weekDay ? parseInt(weekDay, 10) : 1);
  };

  const handleWeekDayChange = (v: number): void => {
    notifyChange(week ? parseInt(week, 10) : 1, v);
  };

  const handleWeekFocus = () => {
    if (configureKeyboard) {
      configureKeyboard('between', 'week', (input) => {
        if (input === '') return;
        const num = parseInt(input, 10);
        if (!isNaN(num)) {
          handleWeekChange(num);
        }
      }, week || '1');
    }
  };

  const handleWeekDayFocus = () => {
    if (configureKeyboard) {
      configureKeyboard('between', 'weekDay', (input) => {
        if (input === '') return;
        const num = parseInt(input, 10);
        if (!isNaN(num)) {
          handleWeekDayChange(num);
        }
      }, weekDay || '1');
    }
  };

  return (
    <span>
      {t('第')}
      <InputNumber
        min={1}
        max={4}
        style={inputNumberStyle}
        value={week}
        onChange={handleWeekChange}
        onFocus={handleWeekFocus}
      />
      {t('周的星期')}
      <InputNumber
        min={1}
        max={7}
        style={inputNumberStyle}
        value={weekDay}
        onChange={handleWeekDayChange}
        onFocus={handleWeekDayFocus}
      />
    </span>
  );
};

export default WeekDay;