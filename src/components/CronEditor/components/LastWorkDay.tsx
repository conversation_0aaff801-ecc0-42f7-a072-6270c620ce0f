import React from 'react';
import { Input } from 'antd';

// 定义 LastWorkDay 的 Props 接口
export interface LastWorkDayProps {
  onChange: (value: string) => void;
  value: string;
}

const LastWorkDay: React.FC<LastWorkDayProps> = ({ onChange, value }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = e.target.value;
    // 确保输入是数字且在范围内
    const numValue = parseInt(v, 10);
    if (!isNaN(numValue) && numValue >= 1 && numValue <= 31) {
      const s = `${numValue}W`;
      onChange && onChange(s);
    }
  };

  const splits = value.split('W');

  return (
    <Input
      type="number"
      min={1}
      max={31}
      value={splits[0]}
      onChange={handleChange}
      style={{ width: '80px' }}
    />
  );
};

export default LastWorkDay;