import React from 'react';
import Radio from 'antd/lib/radio';
import Between from './Between';
import FromEvery from './FromEvery';
import Reg, { index } from './reg';
import BaseEditor, { BaseEditorProps } from './BaseEditor';
import moment from 'moment';

const RadioGroup = Radio.Group;

interface DefaultRadioKeyValueType {
  [key: string]: string;
}
const MIN_YEAR = moment().year();
const MAX_YEAR = 2099;

const defaultRadioKeyValue: DefaultRadioKeyValueType = {};
defaultRadioKeyValue[index.EVERY] = '*';
defaultRadioKeyValue[index.BETWEEN] = `${MIN_YEAR}-${MAX_YEAR}`;
// defaultRadioKeyValue[index.FROM_EVERY] = '1970/1';

export interface YearEditorProps extends BaseEditorProps {
  radioStyle?: React.CSSProperties;
  value?: string;
}

const t = (defaultMsg: string): string => {
  return defaultMsg;
};

class YearEditor extends BaseEditor {
  state = {
    radio: index.EVERY,
    value: defaultRadioKeyValue,
  };

  render() {
    const { radioStyle, value: defaultValue, configureKeyboard, ...config } = this.props as YearEditorProps;
    const { radio, value } = this.state;

    return (
      <RadioGroup onChange={this.handleRadioChange} value={radio}>
        <Reg value={defaultValue || ''} currentIndex={radio} onChange={this.handleRegChange} />
        <Radio style={radioStyle} value={index.EVERY}>
          {t('每年')} {t('允许的通配符[, - * /]')}
        </Radio>
        <Radio style={radioStyle} value={index.BETWEEN}>
          {t('周期')}{' '}
          <Between
            min={1970}
            max={2099}
            value={value[index.BETWEEN]}
            {...config}
            onChange={(v) => this.handleValueChange(index.BETWEEN, v)}
            configureKeyboard={configureKeyboard}
          />
        </Radio>
        {/* <Radio style={radioStyle} value={index.FROM_EVERY}>
          <FromEvery
            front={t('从')}
            middle={t('开始')}
            back={t('年执行一次')}
            value={value[index.FROM_EVERY]}
            fromMin={1970}
            fromMax={2099}
            everyMin={1}
            everyMax={100}
            onChange={(v) => this.handleValueChange(index.FROM_EVERY, v)}
            configureKeyboard={configureKeyboard}
          />
        </Radio> */}
      </RadioGroup>
    );
  }
}

export default YearEditor;