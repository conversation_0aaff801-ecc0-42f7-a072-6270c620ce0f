import React from 'react';
import { Radio } from 'antd';
import type { RadioChangeEvent, SelectProps } from 'antd';

import Between from './Between';
import CheckBoxEditor from './CheckBoxEditor';
import WeekDay from './WeekDay';
import LastWeekDay from './LastWeekDay';
import Reg, { index } from './reg';
import BaseEditor, { BaseEditorProps } from './BaseEditor';
import FromEvery from './FromEvery';

const RadioGroup = Radio.Group;
// const { Option } = Select;

interface DefaultRadioKeyValueType {
  [key: string]: string;
}

const defaultRadioKeyValue: DefaultRadioKeyValueType = {};
defaultRadioKeyValue[index.EVERY] = '*';
defaultRadioKeyValue[index.ANY] = '?';
defaultRadioKeyValue[index.BETWEEN] = '1-2';
defaultRadioKeyValue[index.WEEK_DAY] = '1#1';
defaultRadioKeyValue[index.LAST_WEEK_DAY] = '1L';
defaultRadioKeyValue[index.CHECK_BOX] = '*';

export interface WeekEditorProps extends BaseEditorProps {
  radioStyle?: React.CSSProperties;
  value?: string;
}

const t = (defaultMsg: string): string => {
  return defaultMsg;
};

class WeekEditor extends BaseEditor {
  state = {
    radio: index.EVERY,
    value: defaultRadioKeyValue,
  };

  handleSpecificChange = (v: string) => {
    this.handleValueChange(index.WEEK_DAY, v);
  };

  render() {
    const {
      radioStyle,
      value: defaultValue,
      configureKeyboard,
      ...config
    } = this.props as WeekEditorProps;
    const { radio, value } = this.state;
    const selectProps: SelectProps = {
      value: value[index.WEEK_DAY],
      onChange: this.handleSpecificChange,
      style: { width: '50px' },
    };

    return (
      <RadioGroup onChange={this.handleRadioChange} value={radio}>
        <Reg value={defaultValue || ''} currentIndex={radio} onChange={this.handleRegChange} />
        <Radio style={radioStyle} value={index.EVERY}>
          {t('每周')} {t('允许的通配符[, - * /]')}
        </Radio>
        <Radio style={radioStyle} value={index.ANY}>
          {t('不指定')}
        </Radio>
        <Radio style={radioStyle} value={index.BETWEEN}>
          {t('周期')}
          <Between
            min={1}
            max={7}
            value={value[index.BETWEEN]}
            {...config}
            onChange={(v) => this.handleValueChange(index.BETWEEN, v)}
            configureKeyboard={configureKeyboard}
          />
        </Radio>
        <Radio style={radioStyle} value={index.WEEK_DAY}>
          <WeekDay
            // eslint-disable-next-line react/jsx-no-bind
            onChange={this.handleValueChange.bind(this, index.WEEK_DAY)}
            value={value[index.WEEK_DAY]}
            {...config}
            configureKeyboard={configureKeyboard}
          />
        </Radio>
        <Radio style={radioStyle} value={index.LAST_WEEK_DAY}>
          {t('本月最后一个星期')}{' '}
          <LastWeekDay
            value={value[index.LAST_WEEK_DAY]}
            {...config}
            onChange={(v) => this.handleValueChange(index.LAST_WEEK_DAY, v)}
            configureKeyboard={configureKeyboard}
          />
        </Radio>
        <Radio style={radioStyle} value={index.CHECK_BOX}>
          {t('指定')}
          <CheckBoxEditor
            min={1}
            max={7}
            value={value[index.CHECK_BOX]}
            {...config}
            onChange={(v) => this.handleValueChange(index.CHECK_BOX, v)}
          />
        </Radio>
      </RadioGroup>
    );
  }
}

export default WeekEditor;
