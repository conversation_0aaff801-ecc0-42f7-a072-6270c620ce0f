import React from 'react';
import { Radio } from 'antd';
import type { RadioChangeEvent } from 'antd';

import Between from './Between';
import CheckBoxEditor from './CheckBoxEditor';
import FromEvery from './FromEvery';
import LastWorkDay from './LastWorkDay';
import Reg, { index } from './reg';
import BaseEditor, { BaseEditorProps } from './BaseEditor';

const RadioGroup = Radio.Group;

interface DefaultRadioKeyValueType {
  [key: string]: string;
}

const defaultRadioKeyValue: DefaultRadioKeyValueType = {};
defaultRadioKeyValue[index.EVERY] = '*';
defaultRadioKeyValue[index.ANY] = '?';
defaultRadioKeyValue[index.BETWEEN] = '1-31';
defaultRadioKeyValue[index.FROM_EVERY] = '1/1';
defaultRadioKeyValue[index.CHECK_BOX] = '*';
defaultRadioKeyValue[index.LAST_WORK_DAY] = 'L';

export interface DayEditorProps extends BaseEditorProps {
  radioStyle?: React.CSSProperties;
  value?: string;
}

const t = (defaultMsg: string): string => {
  return defaultMsg;
};

class DayEditor extends BaseEditor {
  state = {
    radio: index.EVERY,
    value: defaultRadioKeyValue,
  };

  render() {
    const { radioStyle, value: defaultValue, configureKeyboard, ...config } = this.props as DayEditorProps;
    const { radio, value } = this.state;

    return (
      <RadioGroup onChange={this.handleRadioChange} value={radio}>
        <Reg value={defaultValue || ''} currentIndex={radio} onChange={this.handleRegChange} />
        <Radio style={radioStyle} value={index.EVERY}>
          {t('每日')} {t('允许的通配符[, - * / ?]')}
        </Radio>
        <Radio style={radioStyle} value={index.ANY}>
          {t('不指定')} {t('允许的通配符[, - * / ?]')}
        </Radio>
        <Radio style={radioStyle} value={index.BETWEEN}>
          {t('周期')}{' '}
          <Between
            min={1}
            max={31}
            value={value[index.BETWEEN]}
            {...config}
            onChange={(v) => this.handleValueChange(index.BETWEEN, v)}
            configureKeyboard={configureKeyboard}
          />
        </Radio>
        <Radio style={radioStyle} value={index.FROM_EVERY}>
          <FromEvery
            front={t('从')}
            middle={t('日开始,每')}
            back={t('日执行一次')}
            value={value[index.FROM_EVERY]}
            fromMin={1}
            fromMax={31}
            everyMin={1}
            everyMax={31}
            onChange={(v) => this.handleValueChange(index.FROM_EVERY, v)}
            configureKeyboard={configureKeyboard}
          />
        </Radio>
        <Radio style={radioStyle} value={index.CHECK_BOX}>
          {t('指定')}
          <CheckBoxEditor
            min={1}
            max={31}
            value={value[index.CHECK_BOX]}
            onChange={(v) => this.handleValueChange(index.CHECK_BOX, v)}
          />
        </Radio>
        <Radio style={radioStyle} value={index.LAST_WORK_DAY}>
          {t('每月最后一个工作日')}
          <LastWorkDay
            onChange={(v) => this.handleValueChange(index.LAST_WORK_DAY, v)}
            value={value[index.LAST_WORK_DAY]}
          />
        </Radio>
      </RadioGroup>
    );
  }
}

export default DayEditor;