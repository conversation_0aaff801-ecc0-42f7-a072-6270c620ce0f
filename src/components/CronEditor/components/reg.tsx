import React from 'react';

// 定义正则表达式常量
const EVERY = /^\*$/;
const ANY = /^\?$/;
const BETWEEN = /^\d+-\d+$/;
const FROM_EVERY = /^\d+\/\d+$/;
const CHECK_BOX = /^(\*|((\d+,)*\d+))$/;
const LAST_WORK_DAY = /^\d+W$/;
const LAST_MONTH_DAY = /^L$/;
const LAST_WEEK_DAY = /^\d+L$/;
const WEEK_DAY = /^\d+#\d+$/;

// 定义 index 常量的类型
export interface IndexType {
  EVERY: 'EVERY';
  ANY: 'ANY';
  BETWEEN: 'BETWEEN';
  FROM_EVERY: 'FROM_EVERY';
  CHECK_BOX: 'CHECK_BOX';
  LAST_WORK_DAY: 'LAST_WORK_DAY';
  LAST_MONTH_DAY: 'LAST_MONTH_DAY';
  LAST_WEEK_DAY: 'LAST_WEEK_DAY';
  WEEK_DAY: 'WEEK_DAY';
}

// 定义 index 对象
export const index: IndexType = {
  EVERY: 'EVERY',
  ANY: 'ANY',
  BETWEEN: 'BETWEEN',
  FROM_EVERY: 'FROM_EVERY',
  CHECK_BOX: 'CHECK_BOX',
  LAST_WORK_DAY: 'LAST_WORK_DAY',
  LAST_MONTH_DAY: 'LAST_MONTH_DAY',
  LAST_WEEK_DAY: 'LAST_WEEK_DAY',
  WEEK_DAY: 'WEEK_DAY',
};

// 定义 REG 对象的类型
type RegType = Record<keyof IndexType, RegExp>;

// 定义 REG 对象
const REG: RegType = {} as RegType;
REG[index.EVERY] = EVERY;
REG[index.ANY] = ANY;
REG[index.BETWEEN] = BETWEEN;
REG[index.FROM_EVERY] = FROM_EVERY;
REG[index.CHECK_BOX] = CHECK_BOX;
REG[index.LAST_WORK_DAY] = LAST_WORK_DAY;
REG[index.LAST_MONTH_DAY] = LAST_MONTH_DAY;
REG[index.LAST_WEEK_DAY] = LAST_WEEK_DAY;
REG[index.WEEK_DAY] = WEEK_DAY;

// 定义 Reg 组件的 Props 接口
export interface RegProps {
  value: string;
  currentIndex?: keyof IndexType;
  onChange?: (index: keyof IndexType, value: string) => void;
  [key: string]: any;
}

class Reg extends React.Component<RegProps> {
  private value: string;

  constructor(props: RegProps) {
    super(props);
    const { value, currentIndex, onChange } = props;
    this.value = value;
    this.updateCron(value, currentIndex, onChange);
  }

  componentWillReceiveProps(nextProps: RegProps) {
    const { value, currentIndex, onChange } = nextProps;
    if (this.value !== value) {
      this.value = value;
      this.updateCron(value, currentIndex, onChange);
    }
  }

  updateCron = (cronText: string, currentIndex: keyof IndexType | undefined, onChange?: (index: keyof IndexType, value: string) => void) => {
    if (currentIndex && REG[currentIndex].test(cronText)) {
      onChange && onChange(currentIndex, cronText);
      return;
    }
    // eslint-disable-next-line
    for (const key in REG) {
      const reg = REG[key as keyof IndexType];
      if (reg.test(cronText)) {
        onChange && onChange(key as keyof IndexType, cronText);
        return;
      }
    }
    onChange && onChange(index.EVERY, '*');
  };

  render() {
    return <div />;
  }
}

export default Reg;