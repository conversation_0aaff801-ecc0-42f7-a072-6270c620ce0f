import React from 'react';
import InputNumber from './InputNumber';

// 定义 LastWeekDay 的 Props 接口
export interface LastWeekDayProps {
  value: string;
  onChange: (value: string) => void;
  configureKeyboard?: (type: 'between' | 'fromEvery' | 'lastWeekDay', id: string, handler: (input: string) => void, initialValue: string) => void;
}

const LastWeekDay: React.FC<LastWeekDayProps> = ({ value, onChange, configureKeyboard }) => {
  const splits = value.split('L');

  const notifyChange = (v: number): void => {
    const s = `${v}L`;
    onChange && onChange(s);
  };

  const handleChange = (_value: number): void => {
    notifyChange(_value);
  };

  // 处理键盘输入
  const handleKeyboardInput = (input: string) => {
    // 允许空字符串输入
    if (input === '') {
      notifyChange(1); // 空值时返回最小值1
      return;
    }
    
    const numValue = parseInt(input || '1', 10);
    if (!isNaN(numValue)) {
      // 确保值在有效范围内
      const validValue = Math.max(1, Math.min(7, numValue));
      notifyChange(validValue);
    }
  };

  // 处理输入框聚焦
  const handleFocus = () => {
    if (configureKeyboard) {
      configureKeyboard(
        'lastWeekDay', 
        'last-week-day', 
        handleKeyboardInput,
        splits[0].toString()
      );
    }
  };

  return (
    <InputNumber
      min={1}
      max={7}
      value={splits[0]}
      onChange={handleChange}
      onFocus={handleFocus}
    />
  );
};

export default LastWeekDay;