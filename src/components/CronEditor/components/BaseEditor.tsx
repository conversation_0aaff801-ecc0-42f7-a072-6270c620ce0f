import React from 'react';
import Reg, { index, IndexType } from './reg';
import type { RadioChangeEvent } from 'antd';

// 定义BaseEditor的Props接口
export interface BaseEditorProps {
  onChange?: (value: string) => void;
  // 共享键盘配置方法
  configureKeyboard?: (type: 'between' | 'fromEvery'|'lastWeekDay', id: string, handler: (input: string) => void, initialValue: string) => void;
  // 允许其他未知属性
  [key: string]: any;
}

// 定义BaseEditor的State接口
interface BaseEditorState {
  value: Record<string, string>;
  radio: IndexType[keyof IndexType];
}

class BaseEditor extends React.Component<BaseEditorProps, BaseEditorState> {
  notifyChange = (radio: IndexType[keyof IndexType], value: string) => {
    const { onChange } = this.props;
    onChange && onChange(value);
  };

  handleRadioChange = (e: RadioChangeEvent) => {
    const radio = e.target.value as IndexType[keyof IndexType];
    const { value } = this.state;
    this.setState({ radio });
    this.notifyChange(radio, value[radio]);
  };

  handleValueChange = (radio: IndexType[keyof IndexType], v: string) => {
    const { value } = this.state;
    value[radio] = v;
    this.setState({ radio, value });
    this.notifyChange(radio, v);
  };

  handleRegChange = (radio: IndexType[keyof IndexType], v: string) => {
    const { value } = this.state;
    if (value[radio] === undefined) {
      console.error('cannot get radio index from radioMap', radio, value);
      this.notifyChange(index.EVERY, '*');
      return;
    }
    value[radio] = v;
    this.setState({ value, radio });
    this.notifyChange(radio, v);
  };
};

export default BaseEditor;