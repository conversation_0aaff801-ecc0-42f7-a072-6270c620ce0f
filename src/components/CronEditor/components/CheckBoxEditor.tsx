import React from 'react';
import { Checkbox, Row, Col } from 'antd';

// 定义 CheckBoxEditor 的 Props 接口
interface CheckBoxEditorProps {
  value: string;
  onChange: (value: string) => void;
  max: number;
  min?: number;
  span?: number;
}

// 定义CheckboxValueType类型
type CheckboxValueType = string | number;

const CheckBoxEditor: React.FC<CheckBoxEditorProps> = ({
  value,
  onChange,
  max,
  min = 0,
  span = 2,
}) => {
  const checkBoxs = (_min: number, _max: number) => {
    const items = [];
    for (let i = _min; i <= _max; i += 1) {
      items.push(
        <Col span={span} key={i}>
          <Checkbox value={i}>{i}</Checkbox>
        </Col>,
      );
    }
    return items;
  };

  const handleChange = (values: CheckboxValueType[]) => {
    if (values.length === 0) onChange('*');
    else onChange(values.sort((a, b) => Number(a) - Number(b)).join(','));
  };

  let checked: number[] = [];
  if (value !== '*' && value) {
    checked = value
      .split(',')
      .map((i) => parseInt(i, 10))
      .filter((v) => v >= min && v <= max)
      .sort((a, b) => a - b);

    const s = checked.join(',');
    s !== value && onChange(s);
  }

  return (
    <div
      style={{
        display: 'inline-block',
        padding: '2px 10px',
        lineHeight: '25px',
        width: 'calc(100% - 80px)',
        verticalAlign: 'top',
        paddingTop: '12px',
      }}
    >
      <Checkbox.Group
        onChange={handleChange}
        value={checked as CheckboxValueType[]}
        style={{ width: '100%' }}
      >
        <Row>{checkBoxs(min, max)}</Row>
      </Checkbox.Group>
    </div>
  );
};

export default CheckBoxEditor;
