import React from 'react';
import { Input } from 'antd';

// 定义 FromEvery 的 Props 接口
export interface FromEveryProps {
  value: string;
  onChange: (value: string) => void;
  front: string;
  middle: string;
  back: string;
  fromMin?: number;
  fromMax?: number;
  everyMin?: number;
  everyMax?: number;
  // 从父组件接收的共享键盘配置方法（改为可选）
  configureKeyboard?: (type: 'between' | 'fromEvery', id: string, handler: (input: string) => void, initialValue: string) => void;
}

const inputNumberStyle: React.CSSProperties = {
  margin: '0 5px',
  width: '80px',
};

const FromEvery: React.FC<FromEveryProps> = ({
  value,
  onChange,
  front,
  middle,
  back,
  fromMin = 0,
  fromMax = 59,
  everyMin = 1,
  everyMax = 59,
  configureKeyboard
}) => {
  const splits = value ? value.split('/') : ['0', '1'];
  const from = splits[0];
  const every = splits[1];

  const notifyChange = (_from: string | number, _every: string | number) => {
    const s = `${_from}/${_every}`;
    onChange && onChange(s);
  };

  const handleFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = e.target.value;
    // 确保输入是数字且在范围内
    const numValue = parseInt(v, 10);
    if (!isNaN(numValue) && numValue >= fromMin && numValue <= fromMax) {
      notifyChange(numValue, every);
    }
  };

  const handleEveryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = e.target.value;
    // 确保输入是数字且在范围内
    const numValue = parseInt(v, 10);
    if (!isNaN(numValue) && numValue >= everyMin && numValue <= everyMax) {
      notifyChange(from, numValue);
    }
  };

  // 处理键盘输入
  const handleFromKeyboardInput = (input: string) => {
    const numValue = parseInt(input || '0', 10);
    if (!isNaN(numValue) && numValue >= fromMin && numValue <= fromMax) {
      notifyChange(numValue, every);
    }
  };

  const handleEveryKeyboardInput = (input: string) => {
    const numValue = parseInt(input || '1', 10);
    if (!isNaN(numValue) && numValue >= everyMin && numValue <= everyMax) {
      notifyChange(from, numValue);
    }
  };

  // 处理输入框聚焦
  const handleInputFocus = (inputType: 'from' | 'every') => {
    if (configureKeyboard) {
      configureKeyboard(
        'fromEvery',
        `fromEvery-${inputType}`,
        inputType === 'from' ? handleFromKeyboardInput : handleEveryKeyboardInput,
        inputType === 'from' ? from : every
      );
    }
  };

  return (
    <span style={{ marginLeft: '5px' }}>
      {front}
      <Input
        type="number"
        style={inputNumberStyle}
        value={from}
        onChange={handleFromChange}
        onFocus={() => handleInputFocus('from')}
        min={fromMin}
        max={fromMax}
      />
      {middle}
      <Input
        type="number"
        style={inputNumberStyle}
        value={every}
        onChange={handleEveryChange}
        onFocus={() => handleInputFocus('every')}
        min={everyMin}
        max={everyMax}
      />
      {back}
    </span>
  );
};

export default FromEvery;