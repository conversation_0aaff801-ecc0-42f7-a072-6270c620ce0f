import React from 'react';
import { Radio } from 'antd';
import type { RadioChangeEvent } from 'antd';

import Between from './Between';
import FromEvery from './FromEvery';
import Reg, { index } from './reg';
import BaseEditor, { BaseEditorProps } from './BaseEditor';
import CheckBoxEditor from './CheckBoxEditor';

const RadioGroup = Radio.Group;

interface DefaultRadioKeyValueType {
  [key: string]: string;
}

const defaultRadioKeyValue: DefaultRadioKeyValueType = {};
defaultRadioKeyValue[index.EVERY] = '*';
defaultRadioKeyValue[index.BETWEEN] = '0-59';
defaultRadioKeyValue[index.FROM_EVERY] = '0/1';
defaultRadioKeyValue[index.CHECK_BOX] = '*';

export interface SecondEditorProps extends BaseEditorProps {
  radioStyle?: React.CSSProperties;
  value?: string;
}

const t = (defaultMsg: string): string => {
  return defaultMsg;
};

class SecondEditor extends BaseEditor {
  state = {
    radio: index.EVERY,
    value: defaultRadioKeyValue,
  };

  render() {
    const {
      radioStyle,
      value: defaultValue,
      configureKeyboard,
      ...config
    } = this.props as SecondEditorProps;
    const { radio, value } = this.state;

    return (
      <RadioGroup onChange={this.handleRadioChange} value={radio}>
        <Reg value={defaultValue || ''} currentIndex={radio} onChange={this.handleRegChange} />
        <Radio style={radioStyle} value={index.EVERY}>
          {t('每秒')} {t('允许的通配符[, - * /]')}
        </Radio>
        <Radio style={radioStyle} value={index.BETWEEN}>
          {t('周期')}{' '}
          <Between
            min={0}
            max={59}
            value={value[index.BETWEEN]}
            {...config}
            onChange={(v) => this.handleValueChange(index.BETWEEN, v)}
            configureKeyboard={configureKeyboard}
          />
        </Radio>
        <Radio style={radioStyle} value={index.FROM_EVERY}>
          <FromEvery
            front={t('从')}
            middle={t('秒开始,每')}
            back={t('秒执行一次')}
            value={value[index.FROM_EVERY]}
            fromMin={0}
            fromMax={59}
            everyMin={1}
            everyMax={59}
            onChange={(v) => this.handleValueChange(index.FROM_EVERY, v)}
            configureKeyboard={configureKeyboard}
          />
        </Radio>
        <Radio style={radioStyle} value={index.CHECK_BOX}>
          {t('指定')}
          <CheckBoxEditor
            max={59}
            value={value[index.CHECK_BOX]}
            {...config}
            onChange={(v) => this.handleValueChange(index.CHECK_BOX, v)}
          />
        </Radio>
      </RadioGroup>
    );
  }
}

export default SecondEditor;
