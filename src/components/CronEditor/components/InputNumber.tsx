import React from 'react';
import { Input } from 'antd';
import type { InputProps } from 'antd';

// 定义 InputNumber 的 Props 接口
export interface InputNumberProps extends Omit<InputProps, 'onChange' | 'value'> {
  value: string | number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  placeholder?: string;
  style?: React.CSSProperties;
}

// 将InputNumber重构为函数组件，移除内部状态管理
const InputNumber: React.FC<InputNumberProps> = ({ 
  value: propValue, 
  onChange, 
  min, 
  max, 
  placeholder = 'number', 
  style, 
  ...restProps 
}) => {
  // 确保值在有效范围内
  const getValue = (value: string | number): number => {
    if (typeof value === 'string' && /^\d+$/.test(value)) {
      value = parseInt(value, 10);
    }
    if (typeof value === 'number') {
      if (value < min) return min;
      if (value > max) return max;
      return value;
    }
    return min;
  };

  // 格式化显示值
  const displayValue = propValue !== undefined ? String(getValue(propValue)) : '';

  // 处理输入变化 - 直接调用onChange传递格式化后的值
  const handleChange = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>): void => {
    // 只接受数字和空字符串
    if (value === '' || /^\d*$/.test(value)) {
      if (value === '') {
        onChange(min); // 空值时返回最小值
      } else {
        const numValue = parseInt(value, 10);
        if (!isNaN(numValue)) {
          onChange(getValue(numValue));
        }
      }
    }
  };

  return (
    <Input
      {...restProps}
      style={{ width: '75px', ...style }}
      placeholder={placeholder}
      onChange={handleChange}
      value={displayValue}
    />
  );
};

export default InputNumber;