interface BatterySlotsProps {
    height?: string;
    status?: boolean[];
    children?: any;
}

export const BatterySlots = ({ height, status = [true, false], children }: BatterySlotsProps): JSX.Element => {
    const multiple = status.length > 1;
    return (
        <div className="flex w-full gap-4 z-[99] relative">
            {status.map((item, index) => (
                <div
                    key={index}
                    className={`flex-1 h-[74px] border-2 rounded flex items-center ${multiple ? 'px-1' : 'px-4'} ${item
                        ? 'border-[#39abff]'
                        : 'border-[#4264a8]'
                        }`}
                    style={height ? { height } : {}}
                >
                    {
                        !(item && children) && (<>
                            <div className="flex min-w-[66px] items-center gap-2">
                                <img
                                    className="w-6 h-6"
                                    alt="Battery"
                                    src={item ? '/home/<USER>' : '/home/<USER>'}
                                />
                                <span
                                    className={`font-medium text-base ${item ? 'text-[#39abff]' : 'text-[#4365a9]'
                                        }`}
                                >
                                    {index + 1}#
                                </span>
                            </div>
                            <div className="ml-auto flex items-center gap-1  min-w-[66px]">
                                <div
                                    className={`w-2 h-2 rounded-full ${item ? 'bg-[#3AE353]' : 'bg-[#4365A9]'
                                        }`}
                                />
                                <span className={`text-xs ${item ? 'text-[#3AE353]' : 'text-[#4365A9]'
                                    }`}>
                                    {item ? '已连接' : '未连接'}
                                </span>
                            </div></>)
                    }
                    {
                        (item && children) && <>
                            <div className="flex flex-row justify-between items-center gap-2 w-[100%]">
                                <div className="flex flex-col justify-between min-w-[66px] min-h-[50px]">
                                    <div className="flex items-center gap-2">
                                        <img
                                            className="w-6 h-6"
                                            alt="Battery"
                                            src={item ? '/home/<USER>' : '/home/<USER>'}
                                        />
                                        <span
                                            className={`font-medium text-base ${item ? 'text-[#39abff]' : 'text-[#4365a9]'
                                                }`}
                                        >
                                            {index + 1}#
                                        </span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <div
                                            className={`w-2 h-2 rounded-full ${item ? 'bg-[#3AE353]' : 'bg-[#4365A9]'
                                                }`}
                                        />
                                        <span className={`text-xs ${item ? 'text-[#3AE353]' : 'text-[#4365A9]'
                                            }`}>
                                            {item ? '已连接' : '未连接'}
                                        </span>
                                    </div>
                                </div>
                                {children}
                            </div>
                        </>
                    }
                </div>
            ))}
        </div>
    );
};