import React, { useState } from 'react';
import { Modal, Upload, Button, Progress, Space, Typography, message } from 'antd';
import { CloseOutlined, CloudUploadOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { RhButton } from '@/components/ui/pagbtn';
import './index.less';

export interface UploadModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onUpload?: (file: File, type?: any) => void;
  tips?: string;
  title?: string;
  type?: any;
  accept?: string;
  fileSize?: number;
  showUploadList?: boolean;
}

const UploadModal: React.FC<UploadModalProps> = ({
  isOpen,
  onClose,
  onUpload,
  title,
  tips,
  type,
  accept,
  fileSize = 10,
  showUploadList = true,
}) => {
  // const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<any>(null);

  const handleUpload = async () => {
    if (!uploadedFile) {
      message.warning('请先选择文件上传');
      return;
    }
    onUpload?.(uploadedFile, type);
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: accept,
    maxCount: 1,
    showUploadList: showUploadList,
    beforeUpload: (file) => {
      if (!!accept) {
        const isValidType = accept
          ?.split(',')
          .some((type) => file.name.toLowerCase().endsWith(type));

        if (!isValidType) {
          message.error(`请上传 ${accept} 格式的文件`);
          return false;
        }
      }

      const isLimit = file.size < fileSize * 1024 * 1024;
      if (!isLimit) {
        message.error(`文件大小超过${fileSize}MB`);
        return false;
      }

      //   setFileList([{ ...file, uid: Date.now().toString() }]);
      // setUploadProgress(0);
      setUploadedFile(file);
      return false; // 阻止自动上传
    },
    // onRemove: () => {
    //   //   setFileList([]);
    //   setUploadProgress(0);
    // },
  };

  //   const selectedFile = fileList[0];

  const handleClose = () => {
    // setFileList([]);
    setUploadedFile(null);
    onClose?.();
  };

  return (
    <Modal
      title={title || '升级'}
      open={isOpen}
      onCancel={handleClose}
      width={500}
      centered
      footer={null}
      className="device-modal-common upload-modal"
      closeIcon={<CloseOutlined className="modal-close-icon" />}
      styles={{
        mask: { backgroundColor: 'rgba(0, 0, 0, 0.45)' },
        content: {
          backgroundColor: '#143F8C',
          borderRadius: '8px',
          boxShadow: '0px 2px 8px 0px rgba(33, 37, 46, 0.15)',
          padding: '32px 24px',
        },
      }}
      destroyOnClose={true}
    >
      <Space direction="vertical" size={24} style={{ width: '100%', paddingBottom: 24 }}>
        {tips && <span className="text-xl text-[#76AAD9]">{tips}</span>}
        <Upload
          // beforeUpload={(file) => {
          //   setUploadedFile(file);
          //   return false;
          // }}
          //   onChange={(info) => {
          //     if (info.fileList.length > 0) {
          //       setUploadedFile(info.file);
          //     } else {
          //       setUploadedFile(null);
          //     }
          //   }}
          className="custom-upload"
          maxCount={1}
          showUploadList={true}
          style={{ width: '100%' }}
          {...uploadProps}
        >
          <RhButton
            icon={<CloudUploadOutlined />}
            style={{
              color: '#6DE875',
              fontSize: 22,
              minWidth: 300,
              width: '100%',
              height: 60,
            }}
          >
            上传文件
          </RhButton>
        </Upload>
      </Space>
      <div className="confirm-footer flex w-full flex-row justify-between gap-8">
        <RhButton key="cancel" onClick={onClose} style={{ width: '50%' }}>
          取消
        </RhButton>
        <RhButton key="upload" onClick={handleUpload} style={{ width: '50%' }}>
          {'确定'}
        </RhButton>
      </div>
    </Modal>
  );
};

export default UploadModal;
