# EchartsLineChart 多曲线组件

这是一个基于 Echarts 和 Ant Design 的 React 多曲线图表组件，支持同时显示多条数据曲线，并提供实时数据联动、交互式图例控制、Y轴范围自定义等功能。

## 功能特性

- ✅ **多曲线支持**：可同时显示多条数据曲线
- ✅ **图例控制**：支持动态开关显示/隐藏曲线
- ✅ **Y轴范围**：支持手动设置或自动调整Y轴范围
- ✅ **数据联动**：图表与表格实时联动
- ✅ **交互响应**：点击表格行查看详细数据
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **深色主题**：支持深色背景主题
- ✅ **实时更新**：支持动态数据更新

## 使用示例

### 基础用法

```tsx
import EchartsLineChart from '@/components/EchartsLineChart';

const Demo = () => {
  const data = [
    { time: '10:00', temperature: 25.5, humidity: 65, pressure: 1013 },
    { time: '10:05', temperature: 26.2, humidity: 63, pressure: 1012 },
    // ...更多数据
  ];

  const seriesConfig = [
    {
      name: '温度',
      dataKey: 'temperature',
      color: '#ff6b6b',
      areaColor: 'rgba(255, 107, 107, 0.3)',
      type: 'line',
      smooth: true,
      showSymbol: true
    },
    {
      name: '湿度',
      dataKey: 'humidity',
      color: '#4ecdc4',
      areaColor: 'rgba(78, 205, 196, 0.3)',
      type: 'line',
      smooth: true,
      showSymbol: true
    }
  ];

  return (
    <EchartsLineChart
      data={data}
      title="环境监测数据"
      xAxisKey="time"
      series={seriesConfig}
      height={400}
    />
  );
};
```

### 完整配置示例

```tsx
const seriesConfig = [
  {
    name: '温度',
    dataKey: 'temperature',
    color: '#ff6b6b',
    areaColor: 'rgba(255, 107, 107, 0.3)',
    type: 'line',
    smooth: true,
    showSymbol: true
  },
  {
    name: '湿度',
    dataKey: 'humidity',
    color: '#4ecdc4',
    areaColor: 'rgba(78, 205, 196, 0.3)',
    type: 'line',
    smooth: true,
    showSymbol: true
  },
  {
    name: '气压',
    dataKey: 'pressure',
    color: '#45b7d1',
    areaColor: 'rgba(69, 183, 209, 0.3)',
    type: 'line',
    smooth: true,
    showSymbol: false
  },
  {
    name: '风速',
    dataKey: 'windSpeed',
    color: '#f9ca24',
    areaColor: 'rgba(249, 202, 36, 0.3)',
    type: 'line',
    smooth: false,
    showSymbol: true
  }
];

<EchartsLineChart
  data={data}
  title="多参数监控"
  xAxisKey="time"
  series={seriesConfig}
  height={500}
  onDataClick={(data) => console.log('点击数据:', data)}
  showTable={true}
/>
```

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | DataPoint[] | - | 图表数据数组 |
| title | string | '曲线图' | 图表标题 |
| xAxisKey | string | 'time' | X轴数据字段名 |
| series | SeriesConfig[] | - | 曲线配置数组（必填） |
| height | number | 400 | 图表高度 |
| onDataClick | function | - | 数据点击回调函数 |
| showTable | boolean | true | 是否显示数据表格 |

### DataPoint 接口

```typescript
interface DataPoint {
  [key: string]: any; // 任意字段，根据series配置匹配
}
```

### SeriesConfig 接口

```typescript
interface SeriesConfig {
  name: string;           // 曲线名称（必填）
  dataKey: string;        // 数据字段名（必填）
  color: string;          // 曲线颜色（必填）
  areaColor?: string;     // 区域填充颜色
  type?: 'line' | 'bar';  // 图表类型，默认为'line'
  smooth?: boolean;       // 是否平滑曲线，默认为true
  showSymbol?: boolean;   // 是否显示数据点，默认为true
}
```

## 功能说明

### 1. 图例控制
- 支持通过下拉菜单选择显示/隐藏特定曲线
- 支持多选，最多显示3个标签
- 实时更新图表和Y轴范围

### 2. Y轴范围控制
- **自动模式**：根据当前显示的数据自动计算合适的范围
- **手动模式**：通过输入框设置最小值和最大值
- **应用按钮**：确认手动设置的Y轴范围
- **自动按钮**：切换回自动范围模式

### 3. 数据表格
- 自动显示所有配置的曲线数据
- 包含额外字段（非曲线数据）
- 支持点击行查看详情
- 每页显示10条数据

### 4. 图表交互
- 鼠标悬停显示详细数据
- 支持图例点击切换曲线显示
- 响应式布局，适配不同屏幕

## 访问演示

组件已集成到系统中，可通过以下路径访问：
- 路径：`/system-maintenance/echarts-demo`
- 演示包含：温度、湿度、气压、风速四条模拟曲线
- 数据每5秒自动更新
- 支持点击表格行查看详细数据

## 集成步骤

1. **导入组件**
```typescript
import EchartsLineChart from '@/components/EchartsLineChart';
```

2. **准备数据**
确保数据格式正确，包含时间字段和各个曲线的数据字段。

3. **配置曲线**
为每条曲线定义名称、数据字段、颜色等配置。

4. **使用组件**
传入数据、曲线配置和其他可选参数。

## 注意事项

- 确保数据中的字段名与series配置中的dataKey匹配
- 颜色建议使用十六进制格式（如：#ff6b6b）
- 对于大量数据（>1000条），建议优化性能或分页显示
- 组件已适配深色主题，背景色为 #0f172a

## 更新日志

### v2.0.0 (当前版本)
- ✅ 新增多曲线支持
- ✅ 新增图例选择功能
- ✅ 优化Y轴范围计算
- ✅ 新增series配置接口
- ✅ 改进数据表格显示
- ✅ 优化响应式布局

### v1.0.0
- ✅ 基础单曲线功能
- ✅ 数据表格联动
- ✅ Y轴范围设置