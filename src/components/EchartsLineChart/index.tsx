import React, { useEffect, useRef, useState, useMemo } from 'react';
import * as echarts from 'echarts';
import { Table, Select, message, Empty } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { formatDate } from '@/utils/formatDate';

interface DataPoint {
  time: string;
  [key: string]: any;
}

interface SeriesConfig {
  name: string;
  dataKey: string;
  color: string;
  areaColor?: string;
  type?: 'line' | 'bar';
  smooth?: boolean;
  showSymbol?: boolean;
}

interface EchartsLineChartProps {
  data: DataPoint[];
  title?: string;
  xAxisKey?: string;
  series: SeriesConfig[];
  height?: number;
  onDataClick?: (data: any) => void;
}

const EchartsLineChart: React.FC<EchartsLineChartProps> = ({
  data,
  title = '',
  xAxisKey = 'time',
  series,
  height = 289,
  onDataClick,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  // 添加series状态以确保内部变化能被捕获
  const [seriesState, setSeriesState] = useState(series);
  // 初始化图表
  useEffect(() => {
    if (!chartRef.current) return;

    chartInstance.current = echarts.init(chartRef.current, null, {
      useCoarsePointer: true,
    });

    return () => {
      if (!!chartInstance.current) {
        chartInstance.current.dispose?.();
      }
    };
  }, []);

  // 防抖定时器引用
  const dataZoomTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setSeriesState(series);
  }, [series]);

  // 更新图表
  useEffect(() => {
    if (!chartInstance.current) return;

    // 当seriesState数据为空时不更新图表，只显示Empty组件
    if (seriesState.length <= 0) return;

    const xData = data.map((item) => item[xAxisKey]);

    const option: echarts.EChartsOption = {
      color: [
        '#3476FF',
        '#FBB938',
        '#F27821',
        '#FFFFFF',
        '#940DFC',
        '#0EDB41',
        '#FC1E1E',
        '#1EE6FC',
        '#FC0DD0',
      ],
      title: {
        text: title,
        textStyle: {
          color: '#fff',
          fontSize: 16,
        },
        left: 'center',
      },
      legend: {
        type: 'scroll',
        data: seriesState.map((s) => s.name),
        textStyle: {
          color: '#fff',
          fontSize: 18,
          lineHeight: 12,
        },
        top: 0,
        icon: 'rect', // 正方形图标
        itemGap: 20,
        itemWidth: 12, // 图标宽度（可选）
        itemHeight: 12,
        z: 1000,
        pageIconSize: 20,
        pageIconColor: '#fff',
        pageIconInactiveColor: '#7d98ce',
        pageTextStyle: {
          color: '#fff',
          fontSize: '18',
        },
      },
      grid: {
        left: '16',
        right: '16',
        bottom: '0',
        top: '60',
        containLabel: true,
      },
      tooltip: {
        triggerOn: 'mousemove|click',
        // position: function (pt) {
        //   return [pt[0], 130];
        // },
        // 自定义位置计算：优先显示在鼠标右侧，右侧不够则显示在左侧
        position: function (point, params, dom, rect, size) {
          const [x, y] = point;
          const containerWidth = size.viewSize[0];
          // 若鼠标右侧空间足够（>200px），显示在右侧，否则显示在左侧
          return x + 200 > containerWidth ? [x - 200, y] : [x + 10, y];
        },
        backgroundColor: 'rgba(0, 25, 88, 1)',
        borderWidth: 1,
        // 修改文本样式
        textStyle: {
          fontSize: 16, // 文本字体大小
          color: '#76AAD9', // 文本颜色
          fontWeight: 'normal',
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xData,
        axisLabel: {
          color: '#76AAD9',
          formatter: function (value: string) {
            const dateTime = formatDate(value).split(' ');
            if (dateTime.length === 2) {
              return `${dateTime[0]}\n${dateTime[1]}`;
            }
            return value;
          },
        },
        axisPointer: {
          lineStyle: {
            color: '#78B2FF',
            width: 4,
            type: 'solid',
          },
          handle: {
            show: true,
            color: '#78B2FF',
            size: 0,
            throttle: 50,
            margin: 40,
          },
          label: {
            show: false,
            formatter: function (params: any) {
              const dateTime = formatDate(params.value).split(' ');
              if (dateTime.length === 2) {
                return `${dateTime[0]}\n${dateTime[1]}`;
              }
              return params.value;
            },
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#254AA6',
            type: 'dashed',
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#254AA6',
            type: 'solid',
          },
        },
      },
      yAxis: {
        type: 'value',
        // min: finalMin,
        // max: finalMax,
        axisLabel: {
          color: '#76AAD9',
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#254AA6',
            type: 'dashed',
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#254AA6',
            type: 'solid',
          },
        },
      },
      dataZoom: [
        {
          type: 'inside',
          throttle: 50,
        },
      ],
      // .filter((s) => selectedSeries.includes(s.name))
      series: seriesState.map((s) => ({
        name: s.name,
        type: s.type || 'line',
        smooth: s.smooth !== false,
        symbol: s.showSymbol !== false ? 'circle' : 'none',
        symbolSize: 8,
        lineStyle: {
          color: s.color,
          width: 2,
        },
        itemStyle: {
          color: s.color,
        },
        connectNulls: true,
        // ...(s.areaColor && {
        //   areaStyle: {
        //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        //       { offset: 0, color: s.areaColor },
        //       { offset: 1, color: 'rgba(0, 0, 0, 0)' }
        //     ])
        //   }
        // }),
        data: data.map((item) => item[s.dataKey]),
        // 关闭当前系列的高亮效果
        emphasis: {
          disabled: true,
        },
        triggerLineEvent: true,
        // large: false,
      })),
    };

    chartInstance.current.setOption(option, {
      notMerge: true,
      lazyUpdate: false,
    });

    // 添加点击事件 - 优化触控面板支持
    if (onDataClick && chartInstance.current) {
      const clickHandler = (params: any) => {
        if (params.componentType === 'series') {
          const dataIndex = params.dataIndex;
          if (data[dataIndex]) {
            // message.info(`点击了${data[dataIndex][xAxisKey]}的数据`);
            onDataClick(data[dataIndex]);
          }
        }
      };

      // 确保移除所有之前的事件监听器
      chartInstance.current.off('click');

      // 添加事件监听器
      chartInstance.current.on('click', clickHandler);

      // 监听鼠标按下事件（开始拖动）
      // chartInstance.current.getZr().on('mousedown', function (params) {
      // });

      // 监听鼠标释放事件（结束拖动）
      // chartInstance.current.getZr().on('mouseup', function (params) {

      //   // 获取当前视图窗口
      //   const option = chartInstance.current?.getOption();
      //   // @ts-ignore
      //   const currentXIndex = option?.xAxis?.[0].axisPointer.value;

      //   if (currentXIndex !== undefined && data[currentXIndex]) {
      //     const currentData = data[currentXIndex];
      //     const currentXValue = currentData[xAxisKey];

      //     // 获取所有series在该数据点的值
      //     // .filter((s) => selectedSeries.includes(s.name))
      //     const seriesData = series.map((s) => ({
      //       name: s.name,
      //       dataKey: s.dataKey,
      //       value: currentData[s.dataKey],
      //       color: s.color,
      //     }));

      //     // 显示当前位置的tooltip
      //     chartInstance.current?.dispatchAction({
      //       type: 'showTip',
      //       seriesIndex: 0,
      //       dataIndex: currentXIndex,
      //     });

      //     // 触发onDataClick回调
      //     if (onDataClick) {
      //       onDataClick({
      //         axisPointerValue: currentXValue,
      //         index: currentXIndex,
      //         data: currentData,
      //         seriesValues: seriesData.reduce(
      //           (acc, s) => {
      //             acc[s.dataKey] = s.value;
      //             return acc;
      //           },
      //           {} as Record<string, any>,
      //         ),
      //       });
      //     }
      //   } else {
      //     console.warn('无法获取当前X轴索引对应的数据');
      //   }
      // });
    }
    // 响应式调整
    const resizeHandler = () => {
      chartInstance.current?.resize();
    };
    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
      if (chartInstance.current && onDataClick) {
        chartInstance.current.off('click');
        chartInstance.current.off('dataZoom');
      }
      // 清理防抖定时器
      if (dataZoomTimeoutRef.current) {
        clearTimeout(dataZoomTimeoutRef.current);
        dataZoomTimeoutRef.current = null;
      }
    };
  }, [data, title, xAxisKey, seriesState, onDataClick, height]);

  // 添加height变化监听
  useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  }, [height]);

  return (
    <div className="h-full w-full">
      <div className="flex h-full flex-col">
        <div
          ref={chartRef}
          style={{
            flex: 1,
            width: '1020px',
            height,
            display: !seriesState || seriesState.length <= 0 ? 'none' : 'block',
          }}
        />
        {(!seriesState || seriesState.length <= 0) && (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            imageStyle={{
              height: 60,
            }}
            description={<span className="text-[14px] text-[#fff]">暂无数据</span>}
          />
        )}
      </div>
    </div>
  );
};

export default EchartsLineChart;
