
import { Input } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useDebounceFn } from 'ahooks';
import { SearchIcon } from '../Icons';
import './index.less';

export type RhSearchInputProps = {
  /**
   * 是否显示border
   */
  bordered?: boolean;
  /**
   * 输入提示
   */
  defaultValue?: string;
  /**
   * 输入提示
   */
  placeholder?: string;
  /**
   * 防抖延迟时间（ms)
   * @default 200
   */
  delayTime?: number;
  /**
   * 是否为表单，如果是表单，会保留原生input的事件交互
   * @default false
   */
  isFormItem?: boolean;
  /**
   * 样式
   */
  className?: string;
  /**
   * 搜索回调，已设置防抖200ms（仅回车和图标点击触发）
   */
  onSearch?: (v: string) => void;
  /**
   * change回调，已设置防抖200ms，文字变化即触发
   */
  onChange?: (v: string) => void;
  [key: string]: any;
  isExtend?: boolean;
};

function RhSearchInput(
  {
    defaultValue,
    placeholder = '请输入',
    bordered = true,
    delayTime = 200,
    className = '',
    isExtend = false,
    isFormItem = false,
    onSearch = () => { },
    onChange,
    ...restProps
  }: RhSearchInputProps,
  ref: React.Ref<any>,
) {
  const [inputValue, setInputValue] = useState<string>(defaultValue ?? '');

  useEffect(() => {
    setInputValue(restProps.value);
  }, [restProps.value]);

  const { run: onSearchRun } = useDebounceFn(async () => {
    onSearch(inputValue);
  }, { wait: delayTime });

  const { run: onChangeRun } = useDebounceFn(async (v) => {

    if (typeof onChange === 'function') {
      onChange(v);
    }
    // 如果是清除输入框，并且没有绑定change时，主动触发search回调
    if (v === '' && !onChange) {
      onSearch(v);
    }
    // 兼容交互调整
    if (v === '' && isExtend) {
      onSearch(v);
    }
  }, { wait: delayTime });

  /**
   * 暴露value
   */
  useImperativeHandle(ref, () => {
    return {
      inputValue,
      setInputValue,
    };
  });

  return (
    <Input
      allowClear
      value={inputValue}
      placeholder={placeholder}
      bordered={bordered}
      className={`rh-search-input ${className}`}
      onChange={(e) => {
        const v = e.target?.value;
        // 作为表单时，会有延迟情况，所以区分表单的情况
        if (isFormItem) {
          onChange?.(v)
        } else {
          setInputValue(v);
          onChangeRun(v);
        }
      }}
      onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
        // Dropdown 下拉面板的回车键触发失效，bug，原因未知
        if (e.key === 'Enter' && !isFormItem) {
          onSearchRun();
        }
      }}
      {...restProps}
      suffix={
        restProps?.suffix || restProps?.suffix === null ? (
          restProps?.suffix
        ) : (
          <SearchIcon />
          /*   <IconFont type='icon-search' size={22} onClick={() => {
              if (!isFormItem) {
                onSearchRun();
              }
            }} style={{ cursor: 'pointer', color: '#9EA5B2' }} /> */
        )
      }
    />
  );
}

export default forwardRef(RhSearchInput);
