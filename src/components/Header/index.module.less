.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 82px;
    padding: 0 20px 0 0;
    // background: linear-gradient(90deg, #001529 0%, #002140 100%);
    color: #fff;
    position: relative;
    // width: 680px; // 设置合适的宽度
    height: 80px; // 设置合适的高度
    // 只显示背景图的右半部分
    background-size: 1180px 130px; // 宽度设置为原来的2倍，高度不变
    background-position: -530px -24px; // 背景图向右偏移到最右边
    // background-size: 228% 130px; // 宽度设置为原来的2倍，高度不变
    // background-position: 100% 0; // 背景图向右偏移到最右边
    background-repeat: no-repeat; // 背景图不重复

    &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 4px;
        // background: linear-gradient(90deg, #0066CC 0%, #0099FF 100%);
    }
}

.left {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px;


    .logo {
        height: 50px;
    }

    .title {
        font-size: 20px;
        font-weight: bold;
        background: linear-gradient(90deg, #FFFFFF 0%, #76AAD9 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

.nav {
    display: flex;
    position: absolute;
    left: 320px;
    top: 30px;
}

.navItem {
    width: 140px;
    height: 42px;
    // 可以添加其他样式，如文本居中、鼠标指针样式等
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #5A85D8; // 设置未高亮时的字体颜色
    margin-left: -18px;
    background-size: 140px 42px;

    &.active {
        color: #6DE875;

        //     &::before {
        //         content: '';
        //         position: absolute;
        //         bottom: -4px;
        //         left: 50%;
        //         transform: translateX(-50%);
        //         width: 8px;
        //         height: 8px;
        //         background: #00FF51;
        //         border-radius: 50%;
        //     }
    }
}

.right {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 300px;
    height: 44px;
}

.systemInfo {
    width: 24px;
    height: 24px;
    margin-left: 2px;
    //   background-repeat: no-repeat;
    background-size: 24px 24px;
}

.moreMenu {
    width: 30px;
    height: 30px;
    margin-left: -4px;
    background-size: 34px 34px;
}

.info,
.time {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #76AAD9;

    span:first-child {
        font-size: 16px;
        color: #fff;
    }
}

.systemMenus {

    :global {
        .ant-dropdown-menu {
            .ant-dropdown-menu-item {
                padding: 12px 20px; // 增加内边距使菜单项更高
                font-size: 18px; // 增大字体
                line-height: 24px; // 增加行高
            }
        }
    }
}