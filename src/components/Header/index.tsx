import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Divider, Dropdown, Modal, Popover, message } from 'antd';
import { httpGet } from '@/shared/http';
import { clearUserInfo, toLoginPage } from '@/shared/auth';
import { useDeviceStore } from '@/store/deviceStore';
import { TimeBlock } from '@/components/TimeBlock';
import logo from '@/assets/logo.svg';
import logoBg from '@/assets/logo_bg.png';
import menuActive from '@/assets/menu_active.png';
import menu from '@/assets/menu.png';
// import cellularIcon from '@/assets/cellular.png'
import styles from './index.module.less';
import { PoweroffOutlined } from '@ant-design/icons';
import { ConfirmModal } from '@/pages/DeviceStatus/components/ConfirmModal';
import { useConfigStore } from '@/store/configStore';

const SYSTEMMAINTENANCEROOT = '/system-maintenance';
const navItems = [
  { label: '首页', path: '/' },
  { label: '监控', path: '/device-status' },
  { label: '统计', path: '/business-statistics' },
  { label: '告警', path: '/alarm' },
  { label: '运维', path: `${SYSTEMMAINTENANCEROOT}/bms` },
];

// 添加信号强度判断函数
const getSignalLevel = (rsrp: number, sinr: number) => {
  if (rsrp > -85 && sinr > 25) return 'strong';
  if (rsrp >= -95 && rsrp <= -85 && sinr >= 16 && sinr <= 25) return 'fine';
  if (rsrp >= -105 && rsrp <= -95 && sinr >= 11 && sinr <= 15) return 'moderate';
  if (rsrp >= -115 && rsrp <= -105 && sinr >= 3 && sinr <= 10) return 'weak';
  if (rsrp < -115 && sinr < 3) return 'veryWeak';
  return 'zeroSignal';
};

// 添加信号强度判断函数
const getSignalLevel2 = (rsrp: number) => {
  if (rsrp == null || rsrp == undefined) {
    return 'zeroSignal';
  }
  const v = Number(rsrp);
  if (v > -85) return 'strong';
  if (v >= -95 && v <= -85) return 'fine';
  if (v >= -105 && v <= -95) return 'moderate';
  if (v >= -115 && v <= -105) return 'weak';
  if (v < -115) return 'veryWeak';
  return 'zeroSignal';
};

export const Header: React.FC = () => {
  const [signalLevel, setSignalLevel] = useState('moderate');
  const [gpsStatus, setGpsStatus] = useState(false);
  const [gpsLocation, setGpsLocation] = useState<number[]>([]);
  const navigate = useNavigate();
  const location = useLocation();
  const { systemStatus, gpsDevice, cellularDevice } = useDeviceStore();
  const { config } = useConfigStore();

  // 操作确认
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmContent, setConfirmContent] = useState('');
  const [confirmFuncParams, setConfirmFuncParams] = useState<any>(null);

  const newNavItems = useMemo(() => {
    if (!config.NavItems) {
      return navItems;
    }
    const res = (config.NavItems || [])?.filter((item: any) => {
      if (item.ViewType === 'Hide') {
        return false;
      }
      return true;
    });
    return res.map((item: any) => {
        item.path = navItems.find((i: any) => i.label === item.label)?.path || '';
      return item;
    })
  }, [config.NavItems]);


  useEffect(() => {
    if (cellularDevice?.device?.status === 'Connect' && cellularDevice?.itemList) {
      const v = cellularDevice.itemList.find((item: any) => item.name === 'RSRP')?.value;
      // const v = Number(cellularDevice.itemList.find((item: any) => item.name === 'RSRQ')?.value || 0)
      // const v = Number(value)
      // const sinr = Number(cellularDevice.itemList.find((item: any) => item.name === 'SINR')?.value || 0)
      setSignalLevel(getSignalLevel2(v));
    } else {
      setSignalLevel('zeroSignal');
    }
  }, [JSON.stringify(cellularDevice)]);

  useEffect(() => {
    setGpsStatus(gpsDevice?.device?.status === 'Connect');
    const location = [];
    for (const item of gpsDevice?.itemList || []) {
      if (item.name === 'RMC_LongitudeRaw') {
        // 经度
        location[0] = Number(item.value);
      } else if (item.name === 'RMC_LatitudeRaw') {
        // 纬度
        location[1] = Number(item.value);
      }
    }
    setGpsLocation(location);
  }, [JSON.stringify(gpsDevice)]);

  // 处理设备关机
  const handleShutdown = useCallback(() => {
    // 检查是否在首页
    if (location.pathname !== '/') {
      message.warning('请回到首页再进行关机操作');
      return;
    }

    if (systemStatus === 1) {
      message.warning('设备已是关机状态');
      return;
    }
    if (systemStatus !== 2) {
      message.warning('设备处于补能或供电状态，不能关机');
      return;
    }
    Modal.confirm({
      title: '关机确认',
      content: '是否要关闭电源车的设备？关闭后无法进行储能或者供电。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await httpGet('/EMS/System/Stop', {});
          if (res.code === 0) {
            message.success('设备已关机');
          } else {
            message.error(res.message || res.msg || '关机失败');
          }
        } catch (error) {
          message.error('操作失败，请重试');
        }
      },
    });
  }, [systemStatus, location.pathname]);

  // 处理设备开机
  const handleStartup = async () => {
    // 检查是否在首页
    if (location.pathname !== '/') {
      message.warning('请回到首页再进行开机操作');
      return;
    }

    try {
      const res = await httpGet('/EMS/System/Start', {});
      if (res.code === 0) {
        message.success('设备已开机');
      } else {
        Modal.error({
          title: '开机失败',
          content: res.message || res.msg || '设备开机异常，请检查设备状态',
        });
      }
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 处理用户退出
  const handleLogout = () => {
    clearUserInfo();
    toLoginPage();
  };
  const [version, setVersion] = useState('');
  const [No, setNo] = useState('');

  // 获取当前版本
  const getSystemVersion = async () => {
    try {
      const res = await httpGet('/api/system/info');
      if (res.code === 0) {
        const version = res.data?.version || '';
        setVersion(version);
      }
    } catch (error) {
      message.error('获取版本失败');
    }
    try {
      const res2 = await httpGet('/api/configs/get?fileName=PowerEMSInfo.cfg');
      const No = res2?.ProductNumber || '';
      setNo(No);
    } catch (error) {
      message.error('获取出厂编号失败');
    }
  };
  // useEffect(() => {
  //   getSystemVersion();
  // }, []);

  const handelOperation = (content: string, type: 'start' | 'stop' | 'logout') => {
    setConfirmContent(content);
    setConfirmFuncParams({ type });
    setConfirmVisible(true);
  };
  const menuItems: any[] = useMemo(() => {
    const items: any = [
      {
        key: 'version',
        label: `版本号：${version}`,
        onClick: () => {},
      },
      {
        key: 'appearanceNumber',
        label: `出厂编号：${No}`,
        onClick: () => {},
      },
      {
        key: 'shutdown',
        label: '电源车设备关机',
        onClick: () => handelOperation('确认是否要将电源车设备关机？', 'stop'), // handleShutdown,
      },
      {
        key: 'logout',
        label: '用户退出',
        onClick: () => handelOperation('确认是否要退出登录', 'logout'), //handleLogout,
        extra: (
          <div
            style={{
              backgroundImage: `url(/images/Vector.svg)`,
              width: '28px',
              height: '28px',
              backgroundRepeat: 'round',
            }}
          />
        ),
      },
    ];
    if (systemStatus === 2) {
      items[2] = {
        key: 'shutdown',
        label: (
          <div className="flex w-full items-center justify-between">
            <span>电源车设备关机</span>
            <PoweroffOutlined style={{ color: '#5B7DC4', fontSize: '28px' }} />
          </div>
        ),
        onClick: () => handelOperation('确认是否要将电源车设备关机？', 'stop'), //handleShutdown
      };
      // items.unshift({
      //   key: 'shutdown',
      //   label: '电源车设备关机',
      //   onClick: handleShutdown,
      // });
    }
    if (systemStatus === 1 || !systemStatus) {
      items[2] = {
        key: 'startup',
        label: (
          <div className="flex w-full items-center justify-between">
            <span>电源车设备开机</span>
            <PoweroffOutlined style={{ color: '#5B7DC4', fontSize: '28px' }} />
          </div>
        ),
        onClick: () => handelOperation('确认是否要将电源车设备开机？', 'start'), //handleStartup,
      };
      // items.unshift({
      //   key: 'startup',
      //   label: '电源车设备开机',
      //   onClick: handleStartup,
      // });
    }

    return items;
  }, [systemStatus, handelOperation]);

  const handleConfirm = async (params: { type: 'start' | 'stop' | 'logout' }) => {
    setConfirmVisible(false);
    setConfirmLoading(true);
    try {
      let res: any;
      if (params.type === 'stop') {
        res = await handleShutdown();
      }
      if (params.type === 'start') {
        res = await handleStartup();
      }
      if (params.type === 'logout') {
        handleLogout();
      }
    } catch (error) {
    } finally {
      setConfirmLoading(false);
    }
  };
  return (
    <>
      <div className={styles.header} style={{ backgroundImage: `url(${logoBg})` }}>
        <div className={styles.left}>
          <img src={logo} alt="SANY" className={styles.logo} />
          {/* <span className={styles.title}>LEMS控制系统</span> */}
        </div>
        <div className={styles.nav}>
          {newNavItems.map((item:any) => {
            const active =
              location.pathname === item.path ||
              (location.pathname.includes(SYSTEMMAINTENANCEROOT) &&
                item.path.includes(SYSTEMMAINTENANCEROOT));
            return (
              <div
                key={item.path}
                className={`${styles.navItem} ${active ? styles.active : ''}`}
                onClick={() => navigate(item.path)}
                style={{
                  backgroundImage: `url(${active ? menuActive : menu})`,
                }}
              >
                {item.label}
              </div>
            );
          })}
        </div>
        <div className={styles.right}>
          {/*   <div className={styles.info}>
          <span>32℃</span>
          <span>温度</span>
        </div>
        <Divider type="vertical" style={{ background: '#FFFFFF4D', height: '20px' }} />
        <div className={styles.info}>
          <span>65%</span>
          <span>湿度</span>
        </div> */}
          <Divider type="vertical" style={{ background: '#FFFFFF4D', height: '20px' }} />
          <TimeBlock />
          <Divider type="vertical" style={{ background: '#FFFFFF4D', height: '20px' }} />
          <Popover
            content={
              gpsLocation.length === 2 ? (
                <div style={{ color: '#333' }}>
                  <div>经度: {gpsLocation[0]}</div>
                  <div>纬度: {gpsLocation[1]}</div>
                </div>
              ) : (
                '未获取到GPS经纬度信息'
              )
            }
            trigger={['click']}
            placement="bottom"
          >
            <div
              className={styles.systemInfo}
              style={{ backgroundImage: `url(/images/${gpsStatus ? 'gps_on' : 'gps_off'}.svg)` }}
            />
          </Popover>
          <div
            className={styles.systemInfo}
            style={{ backgroundImage: `url(/images/${signalLevel}.svg)` }}
          />
          <Divider
            type="vertical"
            style={{ background: '#FFFFFF4D', height: '20px', marginLeft: '20px' }}
          />
          <Dropdown
            menu={{ items: menuItems }}
            placement="bottomRight"
            trigger={['click']}
            overlayClassName={styles.systemMenus}
          >
            <div className="flex h-full flex-1 items-center pl-4">
              <div
                className={styles.systemInfo}
                style={{
                  backgroundImage: `url(/home/<USER>
                  cursor: 'pointer',
                  marginLeft: '0px',
                  paddingLeft: '12px',
                }}
              />
            </div>
          </Dropdown>
        </div>
      </div>
      <ConfirmModal
        open={confirmVisible}
        onCancel={() => {
          setConfirmVisible(false);
        }}
        onConfirm={() => handleConfirm(confirmFuncParams)}
        confirmLoading={confirmLoading}
        content={<div className="leading-7">{confirmContent}</div>}
      />
    </>
  );
};
