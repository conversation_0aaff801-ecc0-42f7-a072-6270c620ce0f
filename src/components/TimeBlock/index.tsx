import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import styles from './index.module.less';

export const TimeBlock: React.FC = () => {
    const [currentTime, setCurrentTime] = useState(new Date());

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    return (
        <div className={styles.timeBlock}>
            <span>{format(currentTime, 'HH:mm:ss')}</span>
            <span>{format(currentTime, 'MM/dd | EEEE', { locale: zhCN })}</span>
        </div>
    );
};