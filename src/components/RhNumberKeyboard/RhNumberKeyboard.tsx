import React from 'react';
import Keyboard from 'react-simple-keyboard';
import 'react-simple-keyboard/build/css/index.css';
import { Button } from 'antd';

export interface RhNumberKeyboardProps {
  /**
   * 键盘是否可见
   */
  visible: boolean;
  /**
   * 键盘输入变化时的回调
   */
  onChange: (input: string) => void;
  /**
   * 键盘按键按下时的回调
   */
  onKeyPress: (button: string) => void;
  /**
   * 关闭键盘的回调
   */
  onClose: () => void;
  /**
   * 键盘的引用
   */
  keyboardRef?: React.RefObject<any>;
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 键盘容器宽度
   */
  width?: string | number;
  position?: 'top' | 'bottom';
}

/**
 * 数字键盘输入组件
 * 提供数字输入和基本操作的虚拟键盘
 */
export const RhNumberKeyboard: React.FC<RhNumberKeyboardProps> = ({
  visible,
  onChange,
  onKeyPress,
  onClose,
  keyboardRef,
  className = '',
  width = '600px',
  position='top',
}) => {
  if (!visible) {
    return null;
  }

  return (
    <div 
      className={`fixed inset-[10px] z-[9999999] flex flex-col ${position==='top'?'justify-start':'justify-end'} items-center ${className} `}
      onTouchStart={(e) => e.stopPropagation()}
      onTouchMove={(e) => e.preventDefault()}
    >
      <div className="shadow-[0_4px_12px_rgba(0,0,0,0.25)] rounded-[10px] bg-[#1B53B7]" style={{ width }}>
        <div className="flex justify-end p-2">
          <Button
            size="large"
            type="text"
            onClick={onClose}
            style={{ color: '#fff',fontSize:'22px' }}
          >
            取消
          </Button>
        </div>
        <Keyboard
          keyboardRef={(r) => {
            if (keyboardRef) {
              (keyboardRef as any).current = r;
            }
          }}
          layout={{
            default: ["1 2 3", "4 5 6", "7 8 9", ". 0 {bksp}", "- {enter}"]
          }}
          display={{
            '{bksp}': '删除',
            '{enter}': '确认'
          }}
          onChange={onChange}
          onKeyPress={onKeyPress}
          theme="hg-theme-default custom-keyboard"
          // useTouchEvents={true}
          useMouseEvents={true}
          disableCaretPositioning={true}
        />
      </div>
    </div>
  );
};

export default RhNumberKeyboard;