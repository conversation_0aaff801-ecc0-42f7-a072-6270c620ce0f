import { Card, CardContent } from '@/components/ui/card';
import { Button } from 'antd';
import { useState, useRef } from 'react';
import { ReactNode } from 'react';

interface BackgroundComponentProps {
  data?: any;
  title?: string | ReactNode;
  titleStatus?: boolean; //titleStatus true表示在线
  ButtonText?: string;
  buttonProps?: any;
  disabled?: boolean;
  onButtonClick?: () => void;
  middlePart?: any; //补能功率设置部分占位
  CardClassName?: string;
  content?:any;
  
}

function BackgroundComponent({
  data = {},
  title = '交流供电',
  titleStatus,
  ButtonText = '开始供电',
  disabled,
  buttonProps,
  onButtonClick,
  middlePart,
  CardClassName='',
  content
}: BackgroundComponentProps) {

  return (
    <div className="relative ml-4 w-full max-w-[390px]">
      <Card className="relative h-[467px] w-full border-none">
        <CardContent className="p-0">
          <div className="relative h-[467px]">
            <img
              className="absolute left-1 top-[3px] z-0 h-[460px] w-[383px]"
              alt="Rectangle"
              src="/home/<USER>"
            />

            {/* Corner decorations */}
            <img
              className="absolute left-[347px] top-[421px] z-10 h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-[348px] top-0 z-10 h-[46px] w-[42px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-0 top-0 z-10 h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />
            <img
              className="absolute left-0 top-[421px] z-10 h-[46px] w-[43px]"
              alt="Group"
              src="/home/<USER>"
            />

            {/* Header title */}
            <div className="absolute left-[101px] top-[3px] z-20 h-[37px] w-[185px]">
              <div className="h-[37px] w-[187px]">
                <div className="relative -top-0.5 left-[-3px] h-[39px] w-[191px]">
                  <div className="absolute left-[42px] top-[7px] w-[92px] text-center text-lg font-medium leading-[normal] tracking-[0] text-[#39abff] [font-family:'PingFang_SC-Medium',Helvetica]">
                    {title}
                  </div>

                  <img
                    className="absolute left-[3px] top-0.5 h-9 w-[185px]"
                    alt="Vector"
                    src="/home/<USER>"
                  />
                  <div className="absolute left-[10.5px] top-[30px] h-[9px] w-[19px] rotate-180">
                    <img
                      className="absolute -top-1 left-[2px] h-[15px] w-[25px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <div className="absolute right-[11px] top-[30px] h-[9px] w-[19px] rotate-180">
                    <img
                      className="absolute -top-1 left-[-3px] h-[15px] w-[25px] -rotate-180"
                      alt="Group"
                      src="/home/<USER>"
                    />
                  </div>
                  <img
                    className="absolute left-0 top-0 h-[17px] w-[191px]"
                    alt="Group"
                    src="/home/<USER>"
                  />
                  <div
                    className="absolute left-[147px] top-4 h-[11px] w-[11px] rounded-[5.28px] bg-[#00ff50]"
                    style={{
                      backgroundColor: `${titleStatus ? '#00ff50' : '#858585'}`,
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Main content */}
            <div className="relative z-30 flex h-full w-[390px] flex-col px-7 pt-10">
              {/* Electrical readings card */}
             {content && <Card className={`mt-2 w-full rounded-lg border-none [background:linear-gradient(0deg,rgba(14,80,188,0.2)_0%,rgba(23,63,129,0)_100%),linear-gradient(0deg,rgba(8,53,133,1)_0%,rgba(8,53,133,1)_100%)] ${CardClassName}`}>
                <CardContent className="relative z-40 flex flex-col items-center justify-center gap-[13px] p-4">
                  <div className="flex  w-[324px] flex-col gap-2 p-2 pl-3">
                   {content}
                  </div>
                </CardContent>
              </Card>}
              {/* Start charging button */}
              {!!middlePart && middlePart}

              <Button
                className="mt-3 h-16 w-full rounded-[10px] border-[3px] border-solid border-[#39abff] [background:linear-gradient(175deg,rgba(0,135,255,0.65)_0%,rgba(0,135,255,0.08)_100%)!important] hover:[background:linear-gradient(175deg,rgba(0,165,255,0.85)_0%,rgba(0,105,255,0.25)_100%)]"
                onClick={onButtonClick}
                disabled={disabled}
                {...buttonProps}
              >
                <span
                  className="whitespace-nowrap text-2xl font-normal leading-[18px] tracking-[0] text-[#81dc4a] [font-family:'PingFang_SC-Semibold',Helvetica]"
                  style={{ color: data.ACOutStatus == 2 ? '#fe4545' : '' }}
                >
                  {ButtonText}
                </span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default BackgroundComponent;
