import React, { ReactNode } from 'react';
import './index.less';

interface AlarmModalProps {
  visible: boolean;
  title?: string;
  content?: string | ReactNode;
  deviceName?: string;
  onClose?: () => void;
  onConfirm?: () => void;
}

const AlarmModal: React.FC<AlarmModalProps> = ({
  visible,
  title = '报警提示',
  content = '',
  onClose,
  onConfirm,
}) => {
  if (!visible) return null;

  return (
    <div className="alarm-modal-overlay">
      <div className="alarm-modal">
        {/* 背景装饰 */}
        <div className="alarm-modal-bg">
          <img src="/figma_images/vector-left.svg" alt="" className="bg-vector bg-vector-left" />
          <img src="/figma_images/vector-right.svg" alt="" className="bg-vector bg-vector-right" />
        </div>

        {/* 关闭按钮 */}
        <button className="alarm-modal-close" onClick={onClose}>
          <img src="/figma_images/close-icon.svg" alt="关闭" />
        </button>

        {/* 标题区域 */}
        <div className="alarm-modal-header">
          <img src="/figma_images/alert-icon.svg" alt="报警图标" className="alarm-icon" />
          <h2 className="alarm-title">{title}</h2>
        </div>

        {/* 警告图标组 */}
        <div className="warning-icons">
          <img src="/figma_images/warning-icon.svg" alt="警告" className="warning-icon" />
        </div>

        {/* 内容区域 */}
        <div className="alarm-modal-content">
          <p className="alarm-text whitespace-pre-wrap break-all">{content}</p>
        </div>

        {/* 确认按钮 */}
        <div className="alarm-modal-footer">
          <button className="alarm-confirm-btn" onClick={onConfirm || onClose}>
            知道了
          </button>
        </div>
      </div>
    </div>
  );
};

export default AlarmModal;
