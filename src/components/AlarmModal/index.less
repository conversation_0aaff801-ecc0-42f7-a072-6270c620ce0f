.alarm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000001;
}




.alarm-modal {
  position: relative;
  width: 433px;
  height: 241px;
  background: linear-gradient(180deg, rgba(171, 15, 15, 0.6) 0%, rgba(171, 15, 15, 0.42) 100%);
  border: 2px solid #FF5353;
  border-radius: 0;
  box-shadow: inset 0px 0px 32px #FF0000;
  padding: 0;
  overflow: hidden;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.alarm-modal-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-vector {
  position: absolute;
  top: 1px;
  width: 22px;
  height: 240px;
  opacity: 0.8;
}

.bg-vector-left {
  left: 1px;
}

.bg-vector-right {
  right: 1px;
}

.alarm-modal-close {
  position: absolute;
  top: 15.99px;
  right: 20.49px;
  width: 20.49px;
  height: 19.54px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px;
  transition: opacity 0.2s ease;
}

.alarm-modal-close:hover {
  opacity: 0.8;
}

.alarm-modal-close img {
  width: 20px;
  height: 20px;
}

.alarm-modal-header {
  position: absolute;
  top: 12px;
  left: 37px;
  display: flex;
  align-items: center;
  gap: 11px;
  z-index: 5;
}

.alarm-icon {
  width: 34px;
  height: 33.98px;
}

.alarm-title {
  font-family: 'PingFang SC', sans-serif;
  font-size: 26px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 1;
  margin: 0;
  padding-top: 7.99px;
}

.warning-icons {
  position: absolute;
  top: 16.01px;
  right: 32px;
  display: flex;
  z-index: 5;
}



.alarm-modal-content {
  position: absolute;
  top: 76px;
  left: 37px;
  width: 347px;
  height: 72px;
  z-index: 5;
}

.alarm-text {
  font-family: 'PingFang SC', sans-serif;
  font-size: 22px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 1.636;
  margin: 0;
  text-align: left;
}

.alarm-modal-footer {
  position: absolute;
  bottom: 20px;
  left: 40px;
  z-index: 5;
}

.alarm-confirm-btn {
  width: 352px;
  height: 50px;
  background: #FF2B2B;
  border: 1px solid #FF2B2B;
  border-radius: 6px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 20px;
  font-weight: 400;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alarm-confirm-btn:hover {
  background: rgba(255, 43, 43, 0.1);
  border-color: #FF5353;
}

.alarm-confirm-btn:active {
  background: rgba(255, 43, 43, 0.2);
  transform: scale(0.98);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alarm-modal {
    width: 90vw;
    max-width: 433px;
    height: auto;
    min-height: 241px;
    padding: 20px;
  }

  .alarm-modal-content {
    position: relative;
    top: auto;
    left: auto;
    width: 100%;
    margin: 20px 0;
  }

  .alarm-text {
    font-size: 18px;
  }

  .alarm-modal-footer {
    position: relative;
    bottom: auto;
    left: auto;
    margin-top: 20px;
  }

  .alarm-confirm-btn {
    width: 100%;
    max-width: 352px;
  }

  .alarm-modal-header {
    position: relative;
    top: auto;
    left: auto;
    margin-bottom: 15px;
  }

  .warning-icons {
    position: relative;
    top: auto;
    right: auto;
    margin: 15px 0;
    justify-content: center;
  }
}