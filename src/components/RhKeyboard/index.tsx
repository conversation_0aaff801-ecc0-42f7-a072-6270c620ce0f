import { Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import React, { useRef } from 'react';
import { omit } from 'lodash';
import Keyboard from 'react-simple-keyboard';
import chineseLayout from "simple-keyboard-layouts/build/layouts/chinese";
import 'react-simple-keyboard/build/css/index.css';

interface RhKeyboardProps {
    show: boolean;
    onClose: () => void;
    init?: (r: any) => void;
    onChange: (input: string) => void;
    layoutType?: string;
}

// 添加自定义布局配置
const keyboardLayout = {
    default: [
        "` 1 2 3 4 5 6 7 8 9 0 - = {bksp}",
        "{tab} q w e r t y u i o p [ ] \\",
        "{lock} a s d f g h j k l ; ' {enter}",
        "{shift} z x c v b n m , . / {shift}",
        "{space}",
    ],
    shift: [
        "~ ! @ # $ % ^ & * ( ) _ + {bksp}",
        "{tab} Q W E R T Y U I O P { } |",
        '{lock} A S D F G H J K L : " {enter}',
        "{shift} Z X C V B N M < > ? {shift}",
        "{space}",
    ],
    ...chineseLayout.layout  // 保留中文布局
};

export const RhKeyboard: React.FC<RhKeyboardProps> = ({
    show,
    onClose,
    init,
    onChange,
    layoutType,
}) => {
    const keyboardRef = useRef<any>(null);

    if (!show) return null;

    return (
        <div className="fixed bottom-0 left-0 w-full z-50 bg-gray-100"
            onTouchStart={(e) => e.stopPropagation()}
            onTouchMove={(e) => e.preventDefault()}
        >
            <div className="flex justify-end p-2">
                <Button
                    type="text"
                    icon={<CloseOutlined />}
                    onClick={onClose}
                    style={{ color: '#000' }}
                />
            </div>

            <Keyboard
                keyboardRef={(r) => {
                    (keyboardRef.current = r);
                    init?.(r);
                }}
                layout={keyboardLayout}
                {...(layoutType === 'chinese' ? omit(chineseLayout, 'layout') : {})}
                layoutName="default"
                onChange={onChange}
                useTouchEvents={true}              // 启用触摸事件支持
                // useMouseEvents={true}              // 启用鼠标事件
                disableCaretPositioning={true}     // 禁用光标定位，避免触摸冲突
                onKeyPress={(button: string) => {
                    if (button === "{shift}") {
                        const currentLayout = keyboardRef.current.options.layoutName;
                        const layoutName = currentLayout === "default" ? "shift" : "default";
                        keyboardRef.current.setOptions({ layoutName });
                    }
                }}
                theme="hg-theme-default custom-keyboard"
                buttonTheme={[
                    {
                        class: "hg-red",
                        buttons: "{shift} {lock}"
                    }
                ]}
                display={{
                    "{bksp}": "退格",
                    "{enter}": "回车",
                    "{shift}": "Shift",
                    "{space}": "空格",
                    "{tab}": "Tab",
                    "{lock}": "Lock"
                }}
            />
        </div>
    );
};