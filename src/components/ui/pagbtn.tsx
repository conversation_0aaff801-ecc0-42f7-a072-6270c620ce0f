import { But<PERSON>, <PERSON> } from "antd";

const RhButton = ({ disabled, onClick, text,children, loading,...restBtnProps }: any) => {
  return (
    <Button
      className="cst custom-btn h-[48px] w-28 flex items-center justify-center rounded-[6px] border-[1px] border-solid border-[#39ABFF]"
      onClick={onClick}
      disabled={disabled||loading}
      {...restBtnProps}
    >
      <span className="whitespace-nowrap text-[22px] font-normal leading-[22px] tracking-[0] text-[#81dc4a]">
        {loading?<Spin/>:''}{text||children}
      </span>
    </Button>
  );
};

export { RhButton };