import { RhButton } from '../ui/pagbtn';

export interface RhPaginationPros {
  total: number;
  current: number;
  pageSize?: number;
  onPageChange: (page: number) => void;
}
const RhPagination = ({ total, current, pageSize=10, onPageChange }: RhPaginationPros) => {
  const handlePageChange = (page: number) => {
    onPageChange(page);
  };
const totalPages = Math.ceil(total / pageSize);

  return (
    totalPages >1 &&(
    <div className="mt-4 flex w-full items-center justify-end gap-2">
      <span className="text-[20px] text-[#76AAD9]">共{total}条数据</span>
      <span className="text-[20px] text-[#76AAD9]">{current}/{totalPages}页</span>
      <RhButton onClick={() => handlePageChange(1)} disabled={current === 1} text={'首页'} />
      <RhButton
        onClick={() => handlePageChange(current - 1)}
        disabled={current === 1}
        text={'上一页'}
      />
      <RhButton
        onClick={() => handlePageChange(current + 1)}
        disabled={current === totalPages}
        text={'下一页'}
      />
      <RhButton
        onClick={() => handlePageChange(totalPages)}
        disabled={current === totalPages}
        text={'最后一页'}
      />
    </div>
    )
  );
};

export default RhPagination;
