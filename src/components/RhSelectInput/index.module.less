.custom-autoSelect {
    :global {
        .ant-select-arrow {
            height: 48px;
            padding: 0;
            top: 0;
            right: 0;
            margin: 0;
            width: 48px;
            padding-left: 8px;
        }

        .ant-select-dropdown .ant-select-item {
            font-size: 18px;
        }

        // 设置下拉菜单的最大高度，移除外层滚动条以避免嵌套滚动条问题
        .ant-select-dropdown {
            max-height: 175px !important;
            overflow: hidden !important;
        }

        // 仅为虚拟列表设置最大高度和滚动属性
        .ant-select-dropdown .rc-virtual-list {
            max-height: 175px !important;
            overflow-y: auto !important;

            .rc-virtual-list-scrollbar.rc-virtual-list-scrollbar-vertical {
                width: 0 !important;
            }
        }
    }
}