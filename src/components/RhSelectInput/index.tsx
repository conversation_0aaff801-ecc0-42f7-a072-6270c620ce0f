import React, { useEffect, useRef, useState } from 'react';
import { AutoComplete } from 'antd';
import { CloseCircleFilled, DownOutlined } from '@ant-design/icons';
import styles from './index.module.less';
import { RhKeyboard } from '../RhKeyboard';
import clsx from 'clsx';

interface RhSelectInputProps {
  value?: string;
  onChange?: (val: string) => void;
  options?: {
    label: string;
    value: string;
  }[];
  placeholder?: string;
  className?: any;
  allowClear?: boolean;
}

const RhSelectInput = ({
  value = '',
  onChange,
  options: _options = [],
  placeholder = '请输入',
  className = '',
  allowClear = false,
}: RhSelectInputProps) => {
  const keyboardRef = useRef<any>(null);
  const autoCompleteRef = useRef<any>(null);
  //   const [selectValue, setSelectValue] = useState(value);
  const [inputValue, setInputValue] = useState(value);
  const [showKeyboard, setShowKeyboard] = useState(false);
  const [selectOpen, setSelectOpen] = useState(false);

  // 同步外部 value 到内部
  useEffect(() => {
    setInputValue(value);
    if (showKeyboard) {
      keyboardRef.current?.setInput(value.toString());
    }
  }, [value, showKeyboard]);

  // 触发外部 onChange
  const handleChange = (val: string) => {
    setInputValue(val);
    setSelectOpen(false);
    onChange?.(val);
  };
  return (
    <>
      <AutoComplete
        ref={autoCompleteRef}
        size="large"
        value={inputValue}
        options={_options}
        open={selectOpen}
        placeholder={placeholder}
        onFocus={() => {
          setShowKeyboard(true);
          setSelectOpen(true);
        }}
        onBlur={() => {
          setSelectOpen(false);
        }}
        onSelect={(val) => {
          handleChange(val);
          // setShowKeyboard(false);
          setSelectOpen(false);
        }}
        onChange={handleChange}
        className={clsx(className, styles['custom-autoSelect'])}
        suffixIcon={
          <div className={`relative flex items-center ${allowClear && inputValue ? '' : ''}`}>
            {allowClear && inputValue && (
              <div
                className="absolute right-[-14px] flex h-[48px] justify-center px-3"
                onClick={() => {
                  handleChange('');
                  // setShowKeyboard(false)
                }}
              >
                <CloseCircleFilled
                  style={{
                    fontSize: '18px',
                    color: '#bbb0c7',
                  }}
                />
              </div>
            )}
            <div className="absolute right-[-40px] flex h-[48px] justify-center px-3">
              <DownOutlined
                onClick={() => {
                  setSelectOpen((prev) => !prev);
                  // 定位焦点到输入框
                  if (autoCompleteRef.current) {
                    const input = autoCompleteRef.current;
                    if (input) {
                      input.focus();
                    }
                  }
                }}
              />
            </div>
          </div>
        }
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
      />

      <RhKeyboard
        init={(r: any) => (keyboardRef.current = r)}
        show={showKeyboard}
        // layoutType={'chinese'}
        onClose={() => {
          setShowKeyboard(false);
          setSelectOpen(false);
        }}
        onChange={handleChange}
      />
    </>
  );
};

export default RhSelectInput;
