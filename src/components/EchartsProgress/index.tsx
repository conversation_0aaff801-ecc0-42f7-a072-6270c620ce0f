import { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface EchartsProgressProps {
    value: number;
    height?: number;
    width?: string;
    colors?: string[];
}

export const EchartsProgress = ({ value = 0, height = 20, colors = [], width = '100%' }: EchartsProgressProps) => {
    const chartRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!chartRef.current) return;

        const chart = echarts.init(chartRef.current);

        // 根据值确定颜色
        const getItemStyle = (value: number) => {
            if (colors && colors.length > 0) {
                // 使用传入的渐变色
                return {
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, colors.map((color, index) => ({
                        offset: index === 0 ? 0 : (index === colors.length - 1 ? 1 : 0.5),
                        color: color
                    })))
                    /*  color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                         { offset: 0, color: '#68BC34' },
                         { offset: 0.5, color: '#81DD4B' },
                         { offset: 1, color: '#FFDD36' }
                     ]) */
                };
            } else {
                // 使用默认的颜色逻辑
                const color = value <= 20 ? '#FE4545' :
                    value <= 70 ? '#FFBA00' :
                        '#3AE353';
                return { color };
            }
        };

        const option = {
            animation: false,
            grid: {
                left: 0,
                right: 0,
                top: 0,
                bottom: 0
            },
            xAxis: {
                type: 'value',
                show: false,
                min: 0,
                max: 100
            },
            yAxis: {
                type: 'category',
                show: false
            },
            series: [
                {
                    type: 'bar',
                    data: [value],
                    barWidth: height,
                    showBackground: true,
                    backgroundStyle: {
                        color: '#315292'
                    },
                    itemStyle: getItemStyle(value)
                }
            ]
        };

        chart.setOption(option);

        return () => {
            chart.dispose();
        };
    }, [value, height, colors]);

    return <div ref={chartRef} style={{ width, height: `${height}px` }} />;
};