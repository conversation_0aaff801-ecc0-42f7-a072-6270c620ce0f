import { useState } from "react";
import CheckedIcon from '@/assets/checked-icon.png'

interface BatterySlotsProps {
  height?: string;
  status?: boolean[];
  children?: any;
  config?: any;
  rechargeReset: (deviceID: string) => void;
  onChangeChecked?: (item: any) => void;
}

export const BatterySlotsSimple = ({
  height,
  status = [true, false],
  config,
  children,
  rechargeReset,
  onChangeChecked
}: BatterySlotsProps): JSX.Element => {
  const multiple = status.length > 1;
  const batteryConfig = config?.InputPowerGoups;
  const InputPowerStyle = config?.InputPowerStyle;
  const isColumn = InputPowerStyle?.layout == 'column';
  const className = `relative z-[98] flex w-full gap-4 flex-1 ${isColumn ? 'flex-col' : 'flex-row'}`;
  const [checkedIndx, setCheckedIndx] = useState<number>(0);
  const changeChecked = (index: number) => {
    setCheckedIndx(index);
    onChangeChecked && onChangeChecked(batteryConfig[index])
  };
  const renderCheckContent = (config: any, index: number) => {
    if (!config.showCheck) {
      return null;
    }
    return (
      <div className="self-center">
        { checkedIndx !== index && (
          <div className="w-[40px] h-[40px] rounded-full border-[1px] border-[#3176d3] border-solid"></div>
        )}
        { checkedIndx === index && (
          <div className="w-[40px] h-[40px]">
            <img src={CheckedIcon}></img>
          </div>
        )}
      </div>
    )
  }
  return (
    <div className={className}>
      {batteryConfig?.map((item: any, index: number) => (
        <div className="flex h-[68px] flex-1 gap-4" style={height ? { height } : {}} onClick={() => changeChecked(index)}>
          {renderCheckContent(item, index)}
          <div
            key={index}
            className={`flex flex-1 items-center rounded-[10px] border-[1px] ${multiple && !isColumn ? 'px-1' : 'px-4'} ${
              item ? 'border-[#39abff]' : 'border-[#4264a8]'
            }`}
            style={height ? { height } : {}}
          >
            {!status[index] && (
              <>
                <div className="flex min-w-[66px] items-center gap-2">
                  <img
                    className="h-6 w-6"
                    alt="Battery"
                    src={item ? '/home/<USER>' : '/home/<USER>'}
                  />
                  <span
                    className={`text-base font-medium ${
                      status[index] ? 'text-[#39abff]' : 'text-[#4365a9]'
                    }`}
                  >
                    {item.Title}
                  </span>
                </div>
                <div className="ml-auto flex min-w-[66px] items-center gap-1">
                  <div
                    className={`h-2 w-2 rounded-full ${
                      status[index] ? 'bg-[#3AE353]' : 'bg-[#4365A9]'
                    }`}
                  />
                  <span className={`text-xs ${status[index] ? 'text-[#3AE353]' : 'text-[#4365A9]'}`}>
                    {status[index] ? '已插枪' : '未插枪'}
                  </span>
                </div>
              </>
            )}
            {status[index] && (
              <>
                <div className="flex w-[100%] flex-row items-center justify-between gap-2">
                  <div className="flex min-h-[50px] min-w-[66px] flex-col justify-between">
                    <div className="flex items-center gap-2">
                      <img
                        className="h-6 w-6"
                        alt="Battery"
                        src={status[index] ? '/home/<USER>' : '/home/<USER>'}
                      />
                      <span
                        className={`text-base font-medium ${
                          status[index] ? 'text-[#39abff]' : 'text-[#4365a9]'
                        }`}
                      >
                        {item.Title}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div
                        className={`h-2 w-2 rounded-full ${
                          status[index] ? 'bg-[#3AE353]' : 'bg-[#4365A9]'
                        }`}
                      />
                      <span
                        className={`text-xs ${status[index] ? 'text-[#3AE353]' : 'text-[#4365A9]'}`}
                      >
                        {status[index] ? '已插枪' : '未插枪'}
                      </span>
                    </div>
                  </div>
                  {/* {children} */}
                  <div
                    style={{
                      width: '90%',
                      height: '54px',
                      background: '#CE121266',
                      borderRadius: 10,
                      outline: '0.78px #39ABFF solid',
                      outlineOffset: '-0.78px',
                      justifyContent: 'center',
                      alignItems: 'center',
                      gap: 3.1,
                      display: 'inline-flex',
                      marginRight: 8,
                    }}
                    onClick={() => rechargeReset(item?.DeviceID || config.DeviceID)}
                  >
                    {item.Button.ButtonLabel}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};
