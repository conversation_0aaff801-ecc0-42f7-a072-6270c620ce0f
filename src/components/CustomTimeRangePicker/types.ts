import type { Dayjs } from 'dayjs';

export interface TimeRangeValue {
  startTime: Dayjs | undefined | null | '';
  unit: TimeUnit;
  number: number | undefined | null | '';
  calcType: 'before' | 'after';
}

export type TimeUnit = 'day' | 'hour' | 'minute' | 'second';

export type TimeUnitOptions = TimeUnit[];

export interface CustomTimeRangePickerProps {
  value?: TimeRangeValue | null;
  onChange?: (value: TimeRangeValue | null) => void;
  defaultTimeUnit?: TimeUnit;
  defaultNumber?: number;
  disabled?: boolean;
  placeholder?: string;
  showTime?: boolean;
  format?: string;
  timeUnitOptions?: TimeUnitOptions;
}
