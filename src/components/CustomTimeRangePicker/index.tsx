import { ConfigProvider, DatePicker, Form, InputNumber, message, Radio, Space } from 'antd';
import type { FormItemProps } from 'antd/es/form';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import weekday from 'dayjs/plugin/weekday';
import React, { useCallback, useEffect, useState, Component, ReactNode, useRef } from 'react';
import type { TimeRangeValue, TimeUnit, TimeUnitOptions } from './types';
import styles from './index.module.less';
import RhNumberKeyboard from '../RhNumberKeyboard/RhNumberKeyboard';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

dayjs.extend(weekday);

export type { TimeRangeValue, TimeUnit, TimeUnitOptions };
// 计算结束时间
export const calculateEndTime = ({
  startTime,
  calcNumber = 0,
  unit,
  calcType,
}: {
  startTime: any;
  calcNumber?: number | undefined | null | '';
  unit: TimeUnit;
  calcType: 'before' | 'after';
}): Dayjs => {
  calcNumber = calcNumber || 0;
  if(calcType === 'before') {
    calcNumber = -calcNumber;
  }

  if (!dayjs.isDayjs(startTime)) {
    throw new Error('Invalid dayjs object provided to calculateEndTime');
  }

  if (typeof startTime.add !== 'function') {
    throw new Error('dayjs object does not have add method');
  }

  switch (unit) {
    case 'day':
      return startTime.add(calcNumber, 'day');
    case 'hour':
      return startTime.add(calcNumber, 'hour');
    case 'minute':
      return startTime.add(calcNumber, 'minute');
    case 'second':
      return startTime.add(calcNumber, 'second');
    default:
      return startTime.add(calcNumber, 'hour');
  }
};

// Error boundary for dayjs-related errors
class DayjsErrorBoundary extends Component<{ children: ReactNode }, { hasError: boolean }> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    if (error.message.includes('dayjs')) {
      return { hasError: true };
    }
    return null;
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Dayjs error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: 16, border: '1px solid #ff4d4f', borderRadius: 6 }}>
          <p style={{ color: '#ff4d4f', margin: 0 }}>时间选择器出现错误，请刷新页面重试</p>
        </div>
      );
    }

    return this.props.children;
  }
}

export interface CustomTimeRangePickerProps {
  value?: TimeRangeValue | null;
  onChange?: (value: TimeRangeValue | null) => void;
  defaultTimeUnit?: TimeUnit;
  defaultNumber?: number;
  disabled?: boolean;
  placeholder?: string;
  showTime?: boolean;
  format?: string;
  timeUnitOptions?: TimeUnitOptions;
  max?: number;
}
const defaultTimeUnitOptions: TimeUnitOptions = ['day', 'hour', 'minute', 'second'];

const TimeUnitOptionsMap = {
  day: { label: '天', value: 'day' },
  hour: { label: '时', value: 'hour' },
  minute: { label: '分', value: 'minute' },
  second: { label: '秒', value: 'second' },
};

const ensureDayjs = (value: any): Dayjs | null => {
  if (!value) return null;

  try {
    if (dayjs.isDayjs(value)) {
      if (typeof value.add === 'function' && typeof value.format === 'function') {
        return value;
      }
    }

    if (typeof value === 'string' || value instanceof Date) {
      const parsed = dayjs(value);
      if (parsed.isValid()) {
        return parsed;
      }
    }

    return null;
  } catch (error) {
    console.error('Error ensuring dayjs object:', error);
    return null;
  }
};
const min = 1;
// const max = 9999;

export const CustomTimeRangePicker: React.FC<CustomTimeRangePickerProps> = ({
  value,
  onChange,
  defaultTimeUnit = 'hour',
  defaultNumber = 1,
  disabled = false,
  placeholder = '请选择时间',
  showTime = true,
  format = 'YYYY-MM-DD HH:mm:ss',
  timeUnitOptions = defaultTimeUnitOptions,
  max = 24,
}) => {
  const [startTime, setStartTime] = useState<Dayjs | null>(ensureDayjs(value?.startTime));
  const [number, setNumber] = useState<number | null | undefined | ''>(
    value?.number ?? defaultNumber,
  );
  const [calcType, setCalcType] = useState<'before' | 'after'>('after');
  const [timeUnit, setTimeUnit] = useState<TimeUnit>(value?.unit ?? defaultTimeUnit);
  const [error, setError] = useState<string>('');
  const [showNumberKeyboard, setShowNumberKeyboard] = useState(false);
  const keyboardRef = useRef<any>(null);

  // 使用 useCallback 来避免不必要的重新渲染
  const handleChange = useCallback(
    (newValue: TimeRangeValue | null) => {
      if (newValue?.unit === 'hour' && newValue?.number && newValue?.number > max) {
        message.error(`时间范围不能超过${max}小时`);
        newValue.number = max;
        // return;
      }
      if (newValue?.unit === 'minute' && newValue?.number && newValue?.number > max * 60) {
        message.error(`时间范围不能超过${max}小时`);
        newValue.number = max * 60;
        // return;
      }
      if (newValue?.unit === 'second' && newValue?.number && newValue?.number > max * 60 * 60) {
        message.error(`时间范围不能超过${max}小时`);
        newValue.number = max * 60 * 60;
        // return;
      }
      onChange?.(newValue);
    },
    [onChange],
  );

  useEffect(() => {
    if (value) {
      const safeStartTime = ensureDayjs(value.startTime);
      if (safeStartTime) {
        setStartTime(safeStartTime);
        setNumber(value.number);
        setTimeUnit(value.unit);
      } else {
        console.warn('Invalid dayjs object in value prop');
        setStartTime(null);
        setNumber(defaultNumber);
        setTimeUnit(defaultTimeUnit);
      }
    } else {
      setStartTime(null);
      setNumber(defaultNumber);
      setTimeUnit(defaultTimeUnit);
    }
  }, [value, defaultNumber, defaultTimeUnit]);

  const refreshValue = (
    _startTime: Dayjs | null = startTime,
    _number: number | null | undefined | '' = number,
    unit: TimeUnit = timeUnit,
    _calcType: 'before' | 'after' = calcType,
  ) => {
    if (!_startTime) {
      handleChange?.({
        startTime: null,
        unit,
        number: _number,
        calcType: _calcType,
      });
      return;
    }
    if (_startTime && dayjs.isDayjs(_startTime) && _number && _number > 0) {
      try {
        const newValue: TimeRangeValue = {
          startTime: _startTime,
          unit,
          number: _number,
          calcType: _calcType,
        };
        handleChange?.(newValue);
      } catch (error) {
        console.error('Error in refresh:', error);
      }
    }
  };

  // 处理开始时间变化
  const handleStartTimeChange = (time: any) => {
    try {
      const _startTime = ensureDayjs(time);
      setStartTime(_startTime);
      // if (_startTime) {
      refreshValue(_startTime, number, timeUnit, calcType);

      // }
    } catch (error) {
      console.error('Error handling start time change:', error);
      setStartTime(null);
    }
  };

  // 处理数字输入变化
  const handleNumberChange = (num: number | null) => {
    setNumber(num || 0);
    // if (startTime && dayjs.isDayjs(startTime)) {
    refreshValue(startTime, num || 0, timeUnit, calcType);
    // }
  };

  const handleChangeCalcType = (type: 'before' | 'after') => {
    // 变更计算方式 往前、往后
    setCalcType(type);
    refreshValue(startTime, number, timeUnit, type);
  };
  const handleCurrentEdit = (value: string) => {
    const numValue = Number(value);
    if (isNaN(numValue)) {
      setError('请输入有效的数字');
      return;
    }
    if (numValue < min) {
      setError(`最小值不能小于 ${min}`);
      handleNumberChange(numValue);
      keyboardRef.current.setInput(numValue.toString());
      return;
    }
    // if (numValue > max) {
    //   setError(`最大值不能超过 ${max}`);
    //   handleNumberChange(max);
    //   keyboardRef.current.setInput(max.toString());
    //   return;
    // }
    if (numValue <= 0) {
      handleNumberChange(numValue);
      keyboardRef.current.setInput(numValue.toString());
      setError('最小值不能小于0');
      return;
    }
    handleNumberChange(numValue);
    keyboardRef.current.setInput(numValue.toString());

    setError('');
  };

  // 处理时间单位变化
  const handleTimeUnitChange = (unit: TimeUnit) => {
    setTimeUnit(unit);
    if (startTime && dayjs.isDayjs(startTime)) {
      refreshValue(startTime, number, unit, calcType);
    }
  };

  return (
    <DayjsErrorBoundary>
      <Space size="middle" align="center" className={`cst ${styles['custom-time-range-picker']}`}>
        <div className="relative flex items-center gap-4 text-[20px]">
          <span>{calcType === 'before' ? '结束时间' : '开始时间'}</span>
          <DatePicker
            onChange={handleStartTimeChange}
            value={startTime}
            disabled={disabled}
            placeholder={placeholder}
            format={format}
            className="custom-time-picker"
            style={{ width: 245, height: 48, borderRadius: '6px !important' }}
            showTime={showTime}
          />
        </div>
        <div className="flex items-center justify-center rounded-md border border-[#39ABFF]">
          {/* <div className="p-3" onClick={() => handleNumberChange(number ? number - 1 : 1)}> */}
          <div className="p-3" onClick={() => handleChangeCalcType('before')}>
            <LeftOutlined style={{ color: '#6596EA', width: 24, height: 24 }} />
          </div>
          <InputNumber
            value={number}
            // onChange={handleNumberChange}
            disabled={disabled}
            onFocus={() => setShowNumberKeyboard(true)}
            style={{ width: 80, height: 30, borderRadius: '6px !important' }}
            className="custom-input"
          />
          <div className="p-3" onClick={() => handleChangeCalcType('after')}>
            <RightOutlined style={{ color: '#6596EA', width: 24, height: 24 }} />
          </div>
        </div>
        <Radio.Group
          value={timeUnit}
          onChange={(e) => handleTimeUnitChange(e.target.value)}
          disabled={disabled}
          optionType="button"
          buttonStyle="solid"
          size="small"
          className={`custom-radio-group flex w-full justify-center`}
        >
          {timeUnitOptions.map((option: TimeUnit) => (
            <Radio.Button
              key={TimeUnitOptionsMap[option].value}
              value={TimeUnitOptionsMap[option].value}
              className={'custom-radio'}
              style={{ width: '70px', height: '48px' }}
            >
              {TimeUnitOptionsMap[option].label}
            </Radio.Button>
          ))}
        </Radio.Group>
      </Space>
      {error && (
        <div className="z-1 absolute bottom-[-15px] left-0 text-sm text-red-500">{error}</div>
      )}
      <RhNumberKeyboard
        keyboardRef={keyboardRef}
        position={'bottom'}
        visible={showNumberKeyboard}
        onChange={handleCurrentEdit}
        onClose={() => {
          refreshValue(startTime, value?.number, timeUnit, calcType);

          setNumber(value?.number);
          setShowNumberKeyboard(false);
        }}
        onKeyPress={(button) => {
          if (button === '{enter}') {
            setShowNumberKeyboard(false);
            refreshValue(startTime, number, timeUnit, calcType);
          }
        }}
      />
    </DayjsErrorBoundary>
  );
};

export const CustomTimeRangePickerFormItem: React.FC<
  CustomTimeRangePickerProps & Omit<FormItemProps, 'children'>
> = ({
  defaultTimeUnit,
  defaultNumber,
  disabled,
  placeholder,
  showTime,
  format,
  timeUnitOptions,
  ...formItemProps
}) => {
  return (
    <Form.Item {...formItemProps}>
      <CustomTimeRangePicker
        defaultTimeUnit={defaultTimeUnit}
        defaultNumber={defaultNumber}
        disabled={disabled}
        placeholder={placeholder}
        showTime={showTime}
        format={format}
        timeUnitOptions={timeUnitOptions}
      />
    </Form.Item>
  );
};

export default CustomTimeRangePicker;
