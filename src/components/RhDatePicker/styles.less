.rh-datepicker {
  &-wrapper {
    overflow-x: auto;

    .ant-tabs-nav-wrap {
      width: 150px;

      .ant-tabs-tab+.ant-tabs-tab {
        margin: 0 !important;
      }

      .ant-tabs-tab {
        padding: 8px 8px 8px 12px !important;
        width: 150px !important;
        justify-content: space-between;

        &-active,
        &:hover {
          color: #505363 !important;
          // background: #f9f9fb;
          background: linear-gradient(174deg, rgba(0, 135, 255, 0.13) 25.25%, rgba(0, 135, 255, 0.26) 93.59%) !important;
        }

        &-active {
          .ant-tabs-tab-btn {
            color: #3C7DB6 !important;
            // color: #505363 !important;
          }
        }
      }

      .ant-tabs-ink-bar {
        width: 0;
      }
    }

    .ant-tabs-tabpane {
      padding-left: 0 !important;
    }

    .ant-tabs-content {
      height: 100%;
      min-height: 274px;

      .ant-dropdown-menu {
        box-shadow: none;
      }
    }

    .selectTitle {
      width: 136px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
    }
  }

  &-quickselect {
    position: relative;
    height: 100%;
    width: 300px;
    overflow: hidden;

    &>.row {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      margin: 16px 16px 0;

      &>span {
        flex: 0 0 auto;
      }

      &>.ant-input-group-wrapper {
        flex: 1 1 0;
        margin-left: 8px;
      }

      .label {
        display: inline-block;
        height: 32px;
        margin-top: 1px;
        line-height: 32px;
      }
    }

    .ant-form-item-explain {
      position: absolute;
      top: 100%;
      left: 0;
    }

    .confirm {
      position: absolute;
      right: 16px;
      bottom: 16px;
    }
  }

  .calendar {
    &>div {
      position: relative !important;
    }

    &.ant-calendar-picker-container {
      position: relative !important;
      top: 0 !important;
    }
  }

  .overlay {
    background: #fff;
    box-shadow: 0 2px 8px rgb(80 83 99 / 20%);
  }

  .fixup {
    .ant-picker-range-arrow {
      display: none;
    }

    .ant-picker-ok button {
      height: 24px !important;
    }
  }

  .dpHook {
    & div:nth-child(2) {
      position: relative !important;
    }
  }
}

.custom-input {
  border-radius: 0 !important;
  border: 1px solid rgba(0, 243, 255, 0.3) !important;
  background: linear-gradient(174deg, rgba(0, 135, 255, 0.13) 25.25%, rgba(0, 135, 255, 0.26) 93.59%) !important;
  color: #E3F8FF !important;

  .ant-input-suffix {
    display: none;
  }

  &:hover>.ant-input-suffix {
    display: flex;
  }
}