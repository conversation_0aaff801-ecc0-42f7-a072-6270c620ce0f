import {
  CalendarOutlined,
  CloseCircleFilled,
  ExclamationCircleOutlined,
  RightOutlined
} from '@ant-design/icons';
import {
  Button,
  DatePicker,
  Dropdown,
  Form,
  Input,
  Menu,
  message,
  Select,
  Tabs,
} from 'antd';
import { noop } from 'lodash';
import moment, { Moment } from 'moment';
import {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import './styles.less';

const formatter = (ts1: number, holder: string | number) => {
  if (ts1) return moment(ts1).format('YYYY-MM-DD HH:mm:ss');
  const holder4 = `${holder}${holder}${holder}${holder}`;
  const holder2 = `${holder}${holder}`;
  return `${holder4}-${holder2}-${holder2} ${holder2}:${holder2}:${holder2}`;
};

const t = (id: string, defaultMsg: string) => {
  return defaultMsg
}

function InputWrapper({ defaultValue = '', onChange = noop, hide = noop, allowEndTimeEmpty = false }) {
  const [input, setInput] = useState('');
  const [unit, setUnit] = useState('minutes');
  const [help, setHelp] = useState<ReactNode>(undefined);

  const ErrorTip = useMemo(() => {
    return (
      <span className="fs body-text color-danger">
        <ExclamationCircleOutlined className="color inherit" />
        &nbsp;
        <span className="fs inherit">
          {'请输入正确的格式'}
          {/* {t('common.form.rightFormat', '请输入正确的格式')} */}
        </span>
      </span>
    );
  }, [t]);

  const changeExtValue = useCallback(() => {
    if (input === '') return; // prevent the default change
    const num = Number(input);
    if (Number.isNaN(num)) return;
    // eslint-disable-next-line no-new-func
    const fn = new Function(
      'm',
      'num',
      'unit',
      `return m().subtract(${num}, '${unit}');`,
    );
    if (allowEndTimeEmpty) {
      // 自定义动态时间为空--暂无
      onChange([fn(moment, num, unit).valueOf(), null]);
    } else {
      onChange([fn(moment, num, unit).valueOf(), Date.now()]);
    }

    hide();
  }, [hide, input, onChange, unit]);

  useEffect(() => {
    if (!/^[1-9][\d]*$/.test(input)) {
      setHelp(ErrorTip);
    } else {
      setHelp(undefined);
    }
  }, [ErrorTip, input]);

  const onCancel = useCallback(() => {
    setInput('');
    setUnit('minutes');
    hide();
  }, [hide]);

  return (
    <div className="rh-datepicker-quickselect">
      <div className="row">
        <span className="label">
          {t('component.datepicker.past', '过去')}&nbsp;
        </span>
        <Form.Item help={help} status={help ? 'error' : undefined}>
          <Input
            addonAfter={
              <Select value={unit} onChange={setUnit}>
                <Select.Option value="minutes">
                  {t('common.min', '分钟')}
                </Select.Option>
                <Select.Option value="hours">
                  {t('common.hr', '小时')}
                </Select.Option>
                <Select.Option value="days">
                  {t('common.d', '天')}
                </Select.Option>
                <Select.Option value="weeks">
                  {t('common.wk', '周')}
                </Select.Option>
                <Select.Option value="month">
                  {t('common.m', '月')}
                </Select.Option>
              </Select>
            }
            defaultValue={defaultValue}
            value={input}
            onChange={(e) => setInput(e.target.value)}
          />
        </Form.Item>
      </div>
      <div className="confirm">
        <Button className="mr1" size="small" onClick={onCancel}>
          {t('common.cancel', "取消")}
        </Button>
        <Button size="small" onClick={changeExtValue} type="primary" style={{ marginLeft: 10 }}>
          {t('common.confirm', '确认')}
        </Button>
      </div>
    </div>
  );
}

const getMaxTimeRange = (limitDay = 30) => {
  return limitDay * 24 * 3600000
};

function DPWrapper({ onChange, value, hide, limitDay }: Record<string, any>) {
  const domRef = useRef<any>();
  // const [open, setOpen] = useState(true);
  const [internal, setInternal] = useState<[Moment, Moment]>(value);

  const [now] = useState(() => moment());
  const [limit] = useState(() => moment().subtract(limitDay, 'days'));

  const onOk = (range: [any, any]) => {
    const [start, end] = range;
    if (!start) {
      message.warning('请选择开始时间');
      return;
    }
    if (limitDay > 0 && Math.abs(+start - +end) > getMaxTimeRange(limitDay)) {
      message.error({ content: `最长只能选${limitDay}天`, style: { zIndex: 99999 } });
      return;
    }

    onChange(range);
    hide();
  };

  const disabledDate = (date: Moment) => {
    // 大于当前时间的不可以选
    if (date.valueOf() > now.valueOf()) return true;
    if (limitDay) {
      // 是否限制最长时间区间
      if (date.valueOf() < limit.valueOf()) return true;
    }
    // const [one, two] = internal;

    // console.log(date.format('YYYY-MM-DD'), one.format('YYYY-MM-DD'),two.format('YYYY-MM-DD'),)
    // if (two && Math.abs(one?.valueOf() - date.valueOf()) > 3 * 24 * 3600000) return true;
    return false;
  };
  return (
    <div
      ref={domRef}
      className="dpHook"
      style={{ position: 'relative', width: 452, height: 408 }}
    >
      <DatePicker.RangePicker
        showTime
        open
        allowClear
        style={{ width: '100%' }}
        mode={['date', 'date']}
        getPopupContainer={() => domRef.current}
        // defaultPickerValue={}
        value={internal as any}
        onChange={setInternal as any}
        onOk={onOk as any}
        dropdownClassName="fixup"
        disabledDate={disabledDate as any}
      />
    </div>
  );
}

function Panel({ width, height, value, onChange, hide, allowEndTimeEmpty, limitDay = 30 }: Record<string, any>) {

  const onQuickMenuClick = ({ key }: { key: string }) => {
    // eslint-disable-next-line no-new-func
    const fn = new Function('m', `return m().${key};`);
    if (allowEndTimeEmpty) {
      // 快捷选择结束时间为空--暂无
      onChange([fn(moment).valueOf(), null]);
    } else {
      onChange([fn(moment).valueOf(), Date.now()]);
    }
    hide();
  };

  const staticText = t(
    'component.datepicker.customStaticTime',
    '自定义静态时间',
  );

  const formatValue = useMemo(() => {
    if (value?.length > 0) {
      return value.map((ts: moment.MomentInput) => moment(ts))
    } else {
      return null
    }
  }, [])

  return (
    <div
      className="rh-datepicker"
      style={{
        minWidth: width,
        height,
        background: '#fff',
        boxShadow: '0px 2px 8px rgba(80, 83, 99, 0.2)',
      }}
    >
      <Tabs className="rh-datepicker-wrapper" tabPosition="left">
        <Tabs.TabPane
          tab={
            <div className="selectTitle">
              <span>{t('component.datepicker.quickSelect', '快捷选择')}</span>
              <RightOutlined />
            </div>
          }
          key="快捷选择"
          destroyInactiveTabPane
        >
          <Menu onClick={onQuickMenuClick}>
            <Menu.Item key="subtract(5, 'minutes')">
              {t('component.datepicker.last5mins', '过去5分钟')}
            </Menu.Item>
            <Menu.Item key="subtract(1, 'hours')">
              {t('component.datepicker.last1hours', '过去1小时')}
            </Menu.Item>
            <Menu.Item key="subtract(24, 'hours')">
              {t('component.datepicker.last24hours', '过去24小时')}
            </Menu.Item>
            <Menu.Item key="subtract(3, 'days')">
              {t('component.datepicker.last3days', '过去3天')}
            </Menu.Item>
            <Menu.Item key="startOf('day')">
              {t('component.datepicker.today', '今天')}
            </Menu.Item>
            {/* <Menu.Item key="subtract(30, 'days')"  >过去30天</Menu.Item> */}
            {/* <Menu.Item key="startOf('week')"       >本周</Menu.Item> */}
            {/* <Menu.Item key="startOf('month')"      >本月</Menu.Item> */}
          </Menu>
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={
            <div className="selectTitle">
              <span>
                {t('component.datepicker.customDynamicTime', '自定义动态时间')}
              </span>
              <RightOutlined />
            </div>
          }
          key="自定义动态时间"
          style={{ height: '100%' }}
        >
          <InputWrapper onChange={onChange} hide={hide} allowEndTimeEmpty={allowEndTimeEmpty} />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={
            <div className="selectTitle">
              <span>
                {/* <Text style={{ width: 118, textAlign: 'left' }} ellipsis={{ tooltip: false }} title={staticText}> */}
                {staticText}
                {/* </Text> */}
              </span>
              <RightOutlined />
            </div>
          }
          key="自定义静态时间"
        >
          <DPWrapper
            value={formatValue}
            onChange={(e: any[]) => {
              if (e?.length > 0) {
                const [m1, m2] = e;
                const end = m2 ? m2 : moment();
                onChange([m1.valueOf(), end?.valueOf?.()]);
              } else {
                onChange(e);
              }
            }}
            hide={hide}
            limitDay={limitDay}
          />
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
}
/**
 * allowEndTimeEmpty 默认false
 * 配置时间选择组件使用快捷选择/自定义动态时间时，是否结束时间为空
 * 为空时显示为至今
 * @param param0 
 * @returns 
 */

export default function RhDatePicker({
  width = 320,
  height = 'auto',
  value,
  initValue,
  onChange = noop,
  limitDay,
  allowClear = false,
  allowEndTimeEmpty = false,
  ...restProps
}: Record<string, any>) {
  const [inputValue, setInputValue] = useState(value);
  const [visible, setVisible] = useState(false);

  const onChangeWithCheck = useCallback(
    (range: any[]) => {
      const [start, end] = range ?? [];
      if (start && start === end) {
        message.error('请勿选择相同的起止时间');
        return;
      }
      if (start && end && start > end) {
        message.error('开始时间不能大于结束时间');
        return;
      }
      setInputValue(range);
      onChange(range);
    },
    [onChange],
  );

  const finalValue = useMemo(() => {
    if (initValue || inputValue) {
      return inputValue || initValue;
    }
    const val = null;
    // const val = [moment().subtract(1, 'hour').valueOf(), moment().valueOf()];
    return val;
  }, [initValue, inputValue]);

  const hidePanel = useCallback(() => {
    setVisible(false);
  }, []);

  const inputText = useMemo(() => {
    let res = null;
    if (value?.length > 0) {
      if (!value[1]) {
        // 结束时间为空显示为 暂无
        res = `${formatter(value[0], 0)} ~ 暂无`;
      } else {
        res = `${formatter(value[0], 0)} ~ ${formatter(value[1], 9)}`;
      }
      return res;
    }
    return null;
  }, [value]);

  return (
    <div
      style={{ width }}
      {...restProps}
      onMouseOver={() => setVisible(true)}
      onMouseOut={() => setVisible(false)}
    >
      <Dropdown
        overlay={
          <Panel
            width={width}
            height={height}
            hide={hidePanel}
            value={value}
            limitDay={limitDay}
            onChange={onChangeWithCheck}
            allowEndTimeEmpty={allowEndTimeEmpty}
          />
        }
        trigger={['hover']}
        open={visible}
      >
        <div style={{ position: 'relative' }}>
          <Input
            style={{ width, color: !inputText ? '#c6ccd7' : '#505363' }}
            className="custom-input"
            readOnly
            value={inputText || '开始时间  ~  结束时间'}
            suffix={
              allowClear && inputText ? (
                <CloseCircleFilled
                  style={{
                    marginRight: '12px',
                    fontSize: '12px',
                    color: '#bbb0c7',
                  }}
                  onClick={() => {
                    onChange(null);
                    onChangeWithCheck([]);
                  }}
                />
              ) : (
                <></>
              )
            }
          />
          <CalendarOutlined
            style={{
              position: 'absolute',
              right: '8px',
              top: '8px',
              color: '#bbb0c7',
            }}
          />
        </div>
      </Dropdown>
    </div>
  );
}
