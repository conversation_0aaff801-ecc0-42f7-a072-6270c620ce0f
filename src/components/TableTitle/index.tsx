import { Space, Typography } from 'antd';
import HeaderFilter from './HeaderFilter';

interface optionItem {
  key: string;
  label: string;
}
interface TableTitleProps {
  title: string;
  column: string;
  options: optionItem[];
  value: any;
  onChange: (value: any) => void;
  width?: number;
  filter?:boolean
}

const TableTitle = ({ title, column, options, value, onChange, width,filter=true }: TableTitleProps) => {
  return (
    <Space className="flex">
      <Typography.Text className="text-[#fff] text-[18px]">{title}</Typography.Text>
      {filter && (
        <HeaderFilter
          width={width}
          title={title}
          column={column}
          filterOptions={options || []}
          filteredValues={value}
          onFilterChange={(value: any) => {
            onChange?.({ column, value });
          }}
        />
      )}
    </Space>
  );
};

export default TableTitle;
