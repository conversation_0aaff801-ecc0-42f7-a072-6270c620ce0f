.device-filter-modal {
  border-bottom: 0px solid #4070c8;
  .ant-modal-close {
    height: 82px;
    top: 0;
    width: 72px;
    right: 0;
    .modal-close-icon {
      font-size: 32px;
      color: #a5cfff;
      width: 24px;
      height: 24px;
      line-height: 24px;
    }
  }
  .ant-modal-header {
    background-color: transparent;
    // border-bottom: 1px solid #4070c8;
    padding: 0;
    margin: 0;
    .ant-modal-title {
      font-size: 22px;
      color: #e3f8ff;
    }
  }
  .ant-modal-content {
    background-color: #143f8c;
    border: none;
    border-radius: 0;
    padding: 24px;
    box-shadow: none;
  }
  .ant-modal-body {
    padding-top: 32px;
  }
}
.upgeade-radio {
  .ant-radio-wrapper {
    margin-inline-end: 0;
  }
  .ant-radio-wrapper .ant-radio-inner {
    width: 46px;
    height: 46px;
    background-color: transparent;
    border-color: #579cdb;
    border-width: 2px;
    &::after {
      width: 55px;
      height: 55px;
      border-radius: 50%;
      left: -6px;
      top: -6px;
      margin-block-start: 0;
      margin-inline-start: 0;
    }
  }
  .ant-radio-wrapper.ant-radio-wrapper-checked .ant-radio-inner {
    background-color: transparent;
    border-color: #6de875;
    &::after {
      background-color: #6de875;
    }
  }
  .ant-radio-wrapper.ant-radio-wrapper-disabled .ant-radio-inner {
    background: #1c428a;
    border-color: #2f689c;
  }
}
