import { CloseOutlined } from '@ant-design/icons';
import { Modal, Radio, Space } from 'antd';
import React, { useState } from 'react';
import './index.less';

const HeaderFilter = ({
  title,
  column,
  filterOptions,
  filteredValues,
  onFilterChange,
  width=500
}: {
  title: string;
  column: any;
  filterOptions: any;
  onFilterChange: (value: any) => void;
  filteredValues: any;
  width?: number;
}) => {
  const [filterModalVisible, setFilterModalVisible] = useState(false);

  const handleOpenFilter = (value: string) => {
    setFilterModalVisible(true);
  };
  const handleFilterChange = (value: any) => {
    onFilterChange?.(value);
  };

  return (
    <>
      <div className="flex items-center px-2" onClick={() => handleOpenFilter(column)}>
        <img
          style={{
            width: 24,
            height: 24,
            opacity: !filteredValues || filteredValues=== 'all' ? 0.5 : 1,

          }}
          alt="filter"
          src={'/images/Filter.svg'}
        ></img>
      </div>
      <Modal
        open={filterModalVisible}
        centered
        title={title}
        maskClosable={false}
        width={width}
        onCancel={() => setFilterModalVisible(false)}
        footer={null}
        closeIcon={<CloseOutlined className="modal-close-icon" />}
        className="upgrade-configure device-filter-modal"
      >
        <div className="flex flex-col">
          <Radio.Group
            defaultValue={'all'}
            value={filteredValues || undefined}
            onChange={(e) => {
              handleFilterChange(e.target.value);
            }}
            className="upgeade-radio w-full"
          >
            <Radio
              key={'all'}
              value={'all'}
              className="h-16 w-[155px] items-center text-base text-[#76AAD9]"
            >
              <Space direction="vertical">
                <span className="text-xl text-[#E3F8FF]">全部</span>
              </Space>
            </Radio>
          </Radio.Group>
          <Radio.Group
            value={ filteredValues || undefined}
            onChange={(e) => {
              handleFilterChange(e.target.value);
            }}
            className="upgeade-radio w-full flex justify-between flex-wrap"
          >
            {(filterOptions || []).map((option: any) => (
              <Radio
                key={option.key}
                value={option.key}
                className="h-16 min-w-[155px] w-1/2 items-center text-base text-[#76AAD9]"
              >
                <Space direction="vertical">
                  <span className="text-xl text-[#E3F8FF]">{option.label}</span>
                </Space>
              </Radio>
            ))}
          </Radio.Group>
        </div>
      </Modal>
    </>
  );
};

export default HeaderFilter;
