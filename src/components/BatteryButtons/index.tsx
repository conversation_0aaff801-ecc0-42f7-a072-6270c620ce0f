import { Button } from 'antd';

interface BatteryButtonsProps {
  activeIndex?: number;
  onSelect?: (index: number) => void;
}

export const BatteryButtons = ({ activeIndex = 0, onSelect }: BatteryButtonsProps): JSX.Element => {
  return (
    <div className="flex w-full gap-2">
      <Button
        ghost
        onClick={() => onSelect?.(0)}
        className={`flex-1 h-[46px] p-[10.77px] border-[2.02px] ${
          activeIndex === 0
            ? 'border-[#39abff!important] hover:bg-[#39abff]/10'
            : 'border-[#4264a8!important] hover:bg-[#4264a8]/10'
        } bg-transparent`}
      >
        <img
          className="w-[18.84px] h-[18.84px] mr-[6.73px]"
          alt="Union"
          src={activeIndex === 0 ? '/home/<USER>' : '/home/<USER>'}
        />
        <span
          className={`[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[13.5px] leading-[14.8px] tracking-[0] ${
            activeIndex === 0 ? 'text-[#39abff!important]' : 'text-[#4365a9!important]'
          }`}
        >
          1#
        </span>
      </Button>

      <Button
        ghost
        onClick={() => onSelect?.(1)}
        className={`flex-1 h-[46px] p-[10.77px] border-[2.02px] ${
          activeIndex === 1
            ? 'border-[#39abff!important] hover:bg-[#39abff]/10'
            : 'border-[#4264a8!important] hover:bg-[#4264a8]/10'
        } bg-transparent`}
      >
        <img
          className="w-[18.84px] h-[18.84px] mr-[6.73px]"
          alt="Union"
          src={activeIndex === 1 ? '/home/<USER>' : '/home/<USER>'}
        />
        <span
          className={`[font-family:'PingFang_SC-Semibold',Helvetica] font-normal text-[13.5px] leading-[14.8px] tracking-[0] ${
            activeIndex === 1 ? 'text-[#39abff!important]' : 'text-[#4365a9!important]'
          }`}
        >
          2#
        </span>
      </Button>
    </div>
  );
};