import React from 'react';
import { Modal, Form } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import styles from './index.module.less';
import { RhButton } from '@/components/ui/pagbtn';

// 定义RCModalProps接口
export interface RCModalProps {
  showModal: boolean;
  destroyOnClose?: boolean;
  confirmButtonContent?: string;
  children?: React.ReactNode;
  cancelButtonContent?: string;
  centered?: boolean;
  title?: string;
  width?: string | number;
  isDisabledConfirmBtn?: boolean;
  isLoadingConfirmBtn?: boolean;
  closable?: boolean;
  maskClosable?: boolean;
  buttonGroup?: React.ReactNode;
  className?: string;
  bodyStyle?: React.CSSProperties;
  isShowButton?: boolean;
  isShowCancelButton?: boolean;
  onCancel: () => void;
  onOk: () => void;
  getContainer?: HTMLElement | (() => HTMLElement) | false;
  closeIcon?: React.ReactNode;
  [key: string]: any;
}

const RCModal = ({
  showModal,
  destroyOnClose = false,
  confirmButtonContent = '确定',
  children,
  cancelButtonContent = '取消',
  centered = true,
  title,
  width,
  isDisabledConfirmBtn,
  isLoadingConfirmBtn,
  closable = true,
  maskClosable = true,
  buttonGroup,
  className,
  bodyStyle,
  isShowButton = true,
  isShowCancelButton = true,
  onCancel,
  onOk,
  getContainer,
  closeIcon,
  ...restProps
}: RCModalProps) => {
  return (
    <>
      <Modal
        open={showModal}
        destroyOnClose={destroyOnClose}
        closable={closable}
        footer={null}
        style={{ padding: 0, ...bodyStyle, minWidth: 400 }}
        width={width || '33.33333%'}
        centered={centered}
        onCancel={onCancel}
        closeIcon={closeIcon || <CloseOutlined className="modal-close-icon" onClick={onCancel} />}
        maskClosable={maskClosable}
        className={`${styles.RCModal} ${className || ''}`}
        title={title}
        getContainer={getContainer}
        {...restProps}
      >
        <div className="confirm-content">{children}</div>
        <div className={`${styles.modalWrapper} confirm-footer  clearfix`}>
          {isShowButton &&
            (buttonGroup || (
              <div className={`${styles.buttonGroup} flex flex-row`}>
                {isShowCancelButton && (
                  <RhButton
                    style={{ marginRight: '12px' }}
                    onClick={onCancel}
                    text={cancelButtonContent}
                  />
                )}
                <RhButton
                  onClick={onOk}
                  disabled={isDisabledConfirmBtn}
                  text={isLoadingConfirmBtn ? '加载中...' : confirmButtonContent}
                />
              </div>
            ))}
        </div>
      </Modal>
    </>
  );
};

// 静态方法保持不变以保持向后兼容性
// RCModal.confirm = (args) => {
//   let modal = null;
//   const modalOnCancel = () => {
//     modal.destroy();
//     modal = null;
//     if (args.onCancel && typeof args.onCancel === 'function') {
//       args.onCancel();
//     }
//   };
//   modal = Modal.confirm({
//     ...args,
//     content: <ModalContent isShowButton={args.isShowButton} title={args.title} content={args.content} onCancel={modalOnCancel} />
//   });
//   return modal;
// };

// RCModal.confirmForm = (args) => {
//   let modal = null;
//   const modalOnCancel = () => {
//     modal.destroy();
//     modal = null;
//     if (args.onCancel && typeof args.onCancel === 'function') {
//       args.onCancel();
//     }
//   };
//   let formRef;
//   const _onOk = async () => {
//     const _form = formRef?.form;
//     await _form?.validateFields();
//     await args?.onOk?.(_form.getFieldsValue());
//   };
//   const content = forwardRef((props, ref) => {
//     useImperativeHandle(ref, () => ({
//       form: props.form,
//     }));
//     return (
//       <Form>
//         <ModalContent title={args.title} content={args.content(props)} onCancel={modalOnCancel} />
//       </Form>);
//   });
//   const ModalConfirmForm = Form.create({ name: 'modalConfirmForm' })(content);
//   modal = Modal.confirm({
//     ...args,
//     content: <ModalConfirmForm wrappedComponentRef={(form) => { formRef = form; }} />,
//     onOk: _onOk,
//   });
//   return modal;
// };
// todo 加上其他info、warning什么的

export default RCModal;
