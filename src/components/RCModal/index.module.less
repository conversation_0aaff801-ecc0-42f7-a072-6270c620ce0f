.RCModal {
  :global(.ant-modal-header) {
    background-color: transparent;
    border-bottom: none;
    padding: 0;
    margin: 0;
  }

  :global(.ant-modal-content) {
    box-shadow: none;
    background-color: #143F8C;
    border-radius: 0;
    padding: 24px 24px 0;
    box-shadow: 0px 2px 8px 0px rgba(33, 37, 46, 0.15);
  }

  :global(.ant-form-item) {
    &:last-child {
      margin-bottom: 0px;
    }
  }

  :global {

    .confirm-content {
      padding:0 24px 0;
      font-family: 'PingFang SC';
      font-size: 20px;
      font-weight: 400;
      line-height: 1em;
      color: #E3F8FF;
      max-height: 612px;
      overflow-y: auto;
    }

    .confirm-footer {
      display: flex;
      justify-content: space-between;
      gap: 12px;
      margin:  24px  24px 0px;

      button {
        flex: 1;
        width: 50%;
        padding: 12px 16px;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-weight: 400;
        border-radius: 6px;
        line-height: 1.25em;
        cursor: pointer;
        min-width: 80px;
        text-align: center;

        &.cancel-button {
          background-color: transparent;
          border: 1px solid #A5CFFF;
          color: #A5CFFF;

          &:hover {
            color: #E3F8FF;
            border-color: #E3F8FF;
          }
        }

        &.confirm-button {
          background-color: #0478EC;
          border: 1px solid #0478EC;
          color: #FFFFFF;

          &:hover {
            opacity: 0.9;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

.title {
  display: inline-block;
  font-size: 20px;
  font-weight: 600;
  color: rgba(33, 37, 46, 1);
  line-height: 22px;
  letter-spacing: 1px;
  margin-bottom: 24px;
}

.buttonGroup {
  // float: right;
  // padding: 16px 40px 16px;
  width: 100%;
}

.clearfix {
  *zoom: 1;
}

.clearfix:after {
  content: '';
  display: block;
  clear: both;
  height: 0;
  line-height: 0;
  visibility: hidden;
}