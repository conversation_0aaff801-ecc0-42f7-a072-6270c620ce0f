export default function ModalContent({
  content, onCancel
}) {
  return (
    <div className="RcModalContent">
      <CloseOutlined className="modal-close-icon" onClick={onCancel} />
      {content}
      <style jsx>{`
        .RcModalContent :global(.IconClose) {
          position: absolute;
          top: 0;
          right: 0;
          padding: 24px;
          font-size: 12px;
          cursor: pointer;
        }

        .RcModalContent :global(.IconAlert) {
          position: absolute;
          font-size: 24px;
        }

        .Confirm {
          padding-left: 32px;
        }
      `}
      </style>
    </div>
  );
}
