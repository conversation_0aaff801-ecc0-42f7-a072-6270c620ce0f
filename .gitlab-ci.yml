# nodejs18 (此镜像很费时间，但是目前平台没支持或者不好自建nodejs18的runner镜像，临时使用)
image: registry.rootcloud.com/rootcloud-platform/ci-jdk11:1.0.56
# image: luoxy1001/node16-git:v1.2

variables:
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ''
  #YARN_CACHE_FOLDER: .yarn

stages:
  # - test
  - build_without_tag
  - build_and_tag
  - release_and_tag
  - send_failure
  # - build_docker

before_script:
  - echo ${CI_COMMIT_REF_NAME}
  - npm config set registry https://registry.npmmirror.com
  - chmod a+x ./scripts/wechat_hook.sh
  - npm i pnpm -g
  - pnpm -v
  - node -v
  - pnpm install --no-frozen-lockfile

cache:
  paths:
    - node_modules/

# test:
#   stage: test
#   services:
#     - name: docker:19.03.12-dind
#       command: ['--registry-mirror=https://mirror.ccs.tencentyun.com']
#       alias: localhost
#   # coverage: '/total:\s+\(statements\)\s+(\d+.\d+\%)/'
#   script:
#     # 构建组件测试
#     - cd packages/rh-components && yarn build || ./scripts/wechat_hook.sh 'any' '测试失败'
#   tags:
#     - k8s

build_without_tag:
  stage: build_without_tag
  # services:
  #   - name: docker:19.03.12-dind
  #     command: ['--registry-mirror=https://mirror.ccs.tencentyun.com']
  #     alias: localhost
  tags:
    - k8s
  # before_script:
  #   - docker login -u ${DOCKER_USER} -p ${DOCKER_PASSWORD} ${DOCKER_REGISTRY}
  #   - chmod a+x ./scripts/wechat_hook.sh
  #   - chmod a+x ./scripts/docker.sh
  script:
    # - sed -i 's/\r$//' ./scripts/docker.sh
    - sed -i 's/\r$//' ./scripts/wechat_hook.sh
    # - docker login -u ${DOCKER_USER} -p ${DOCKER_PASSWORD} ${DOCKER_REGISTRY} || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'docker login fail'
    - export BRANCH_NAME=${CI_COMMIT_REF_NAME}
    - CI=false BRANCH_NAME=${CI_COMMIT_REF_NAME} PROXY_TYPE=NGINX node ./scripts/build.js 'test' || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'build.js ci 失败'
    # - bash ./scripts/docker.sh --mode=${CI_COMMIT_REF_NAME} || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'docker镜像推送失败！'
  artifacts:
    name: sanyems-ui-dist
    expire_in: 12 hour
    paths:
      - dist/
  only:
    - dev
    - test  # 测试分支 
    - guiyang  # 贵阳分支 
    - jiangxi # 江西分支
    - hangzhou  # 杭州分支
    - liyuan  # 利源工贸分支
    - lafangda  # 拉凡达分支
    - hotfix
    - feat
    # - master

build_and_tag:
  stage: build_and_tag
  # services:
  #   - name: docker:19.03.12-dind
  #     command: ['--registry-mirror=https://mirror.ccs.tencentyun.com']
  #     alias: localhost
  tags:
    - k8s
  # before_script:
  #   - docker login -u ${DOCKER_USER} -p ${DOCKER_PASSWORD} ${DOCKER_REGISTRY}
  #   - chmod a+x ./scripts/wechat_hook.sh
  #   - chmod a+x ./scripts/docker.sh
  script:
    # - sed -i 's/\r$//' ./scripts/docker.sh
    - sed -i 's/\r$//' ./scripts/wechat_hook.sh
    # - docker login -u ${DOCKER_USER} -p ${DOCKER_PASSWORD} ${DOCKER_REGISTRY} || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'docker login fail'
    - yarn semantic-release || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'semantic-release失败！'
    - CI=false node ./scripts/build.js || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'build.js ci 失败'
    # - bash ./scripts/docker.sh --mode=${CI_COMMIT_REF_NAME} || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'docker镜像推送失败！'
  artifacts:
    # API 下载 https://docs.gitlab.com/ee/api/job_artifacts.html
    name: 'sanyleems-ui-release'
    # artifacts 的过期时间1周
    expire_in: 1 week
    paths:
      - src-tauri/target/release/
  only:
    - main

release_and_tag:
  stage: release_and_tag
  image: ubuntu:22.04  # 使用 Ubuntu 22.04 作为基础镜像
  # services:
  #   - name: docker:19.03.12-dind
  #     command: ['--registry-mirror=https://mirror.ccs.tencentyun.com']
  #     alias: localhost
  before_script:
    # 安装基本依赖
    - apt update
    - apt install -y libwebkit2gtk-4.0-dev build-essential curl wget libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev

    # 安装 Rust
    - curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --default-toolchain $RUST_VERSION
    - source "$HOME/.cargo/env"
    # 安装 Tauri CLI
    - cargo install tauri-cli
    # 安装 Node.js（如果前端项目需要）
    - curl -fsSL https://deb.nodesource.com/setup_16.x | bash -
    - apt-get install -y nodejs
    - node -v
    # 安装前端依赖
    - npm i pnpm -g
    - pnpm install --no-frozen-lockfile
  tags:
    - k8s
  # before_script:
  #   - docker login -u ${DOCKER_USER} -p ${DOCKER_PASSWORD} ${DOCKER_REGISTRY}
  #   - chmod a+x ./scripts/wechat_hook.sh
  #   - chmod a+x ./scripts/docker.sh
  script:
    - sed -i 's/\r$//' ./scripts/wechat_hook.sh
    - pnpm build:tauri ||./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'build tauri fail'
    # - sed -i 's/\r$//' ./scripts/docker.sh
    # - docker login -u ${DOCKER_USER} -p ${DOCKER_PASSWORD} ${DOCKER_REGISTRY} || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'docker login fail'
    # - yarn semantic-release || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'semantic-release失败！'
    # - CI=false node ./scripts/build.js || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'build.js ci 失败'
    # - bash ./scripts/docker.sh --mode=${CI_COMMIT_REF_NAME} || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'docker镜像推送失败！'
  artifacts:
    # API 下载 https://docs.gitlab.com/ee/api/job_artifacts.html
    name: 'sanylems-ui-release'
    # artifacts 的过期时间1周
    expire_in: 1 week
    paths:
      - src-tauri/target/release/bundle/
  only:
    - tauri
    - release


# build_docker:
#   stage: build_and_tag
#   services:
#     - name: docker:19.03.12-dind
#       command: ['--registry-mirror=https://mirror.ccs.tencentyun.com']
#       alias: localhost
#   tags:
#     - k8s
#   script:
#     - docker login -u ${DOCKER_USER} -p ${DOCKER_PASSWORD} ${DOCKER_REGISTRY} || ./scripts/wechat_hook.sh ${CI_COMMIT_REF_NAME} 'docker login fail'
#     - docker pull le5le/app:latest
#     - docker tag le5le/app:latest registry.rootcloud.com/rootcloud-platform/postgres:latest
#     - docker push registry.rootcloud.com/rootcloud-platform/postgres:latest
#     - docker tag le5le/app:latest registry.rootcloud.com/rootcloud-platform/le5le/app:latest
#     - docker push registry.rootcloud.com/rootcloud-platform/le5le/app:latest
#   only:
#     - docker


send_failure:
  stage: send_failure
  tags:
    - k8s
  script:
    - echo "on_failure"
    - ./scripts/wechat_hook.sh fail
  when: on_failure

after_script:
  - echo "End CI"
