<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/ico" href="/src/assets/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>三一锂能EMS</title>
</head>

<body>
  <div id="root"></div>
  <script defer>
    ; (function () {
      const prefersDark =
        window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
      const setting = localStorage.getItem('color-schema') || 'auto'
      if (setting === 'dark' || (prefersDark && setting !== 'light'))
        document.documentElement.classList.toggle('dark', true)
    })()
  </script>
  <script type="module" src="/src/main.tsx"></script>
  <script>
    // 等待 Tauri API 初始化
    window.addEventListener('DOMContentLoaded', async () => {
      try {
        let taruiApp = window.__TAURI__;

        document.addEventListener('keydown', async (event) => {
          try {
            if (window.parent !== window) {
              // 在 iframe 中，发送事件到父窗口
              window.parent.postMessage({
                type: 'tauri-action',
                action: event.key === 'Escape' ? 'toggleFullscreen' :
                  ((event.ctrlKey || event.metaKey) && event.key === 'q') ? 'closeWindow' : null
              }, '*');
              return
            }

            if (taruiApp) {
              const { window: tauriWindow } = taruiApp;
              const currentWindow = await tauriWindow.getCurrentWindow();
              console.log('currentWindow=', currentWindow);
              if (event.key === 'Escape') {
                await currentWindow.toggleFullscreen();
              }

              if ((event.ctrlKey || event.metaKey) && event.key === 'q') {
                await currentWindow.close();
              }
            }
          } catch (error) {
            console.error('快捷键处理错误:', error);

          }
        });
      } catch (error) {
        console.error('Tauri API 初始化错误:', error);
      }

      // 向父窗口发送就绪消息
      window.parent.postMessage({ type: 'iframe-ready' }, '*');
    });
  </script>
</body>

</html>